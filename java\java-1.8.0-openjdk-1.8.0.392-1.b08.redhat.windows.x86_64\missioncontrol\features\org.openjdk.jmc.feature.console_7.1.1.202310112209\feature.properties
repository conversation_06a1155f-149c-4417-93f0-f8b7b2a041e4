#
#  Copyright (c) 2018, 2019, Oracle and/or its affiliates. All rights reserved.
#
#  DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
#  The contents of this file are subject to the terms of either the Universal Permissive License 
#  v 1.0 as shown at http://oss.oracle.com/licenses/upl
#   
#  or the following license:
#   
#  Redistribution and use in source and binary forms, with or without modification, are permitted
#  provided that the following conditions are met:
#   
#  1. Redistributions of source code must retain the above copyright notice, this list of conditions
#  and the following disclaimer.
#   
#  2. Redistributions in binary form must reproduce the above copyright notice, this list of
#  conditions and the following disclaimer in the documentation and/or other materials provided with
#  the distribution.
#   
#  3. Neither the name of the copyright holder nor the names of its contributors may be used to
#  endorse or promote products derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
#  IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
#  FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
#  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
#  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
#  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
#  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
#  WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
name=JDK Mission Control Console
provider=Oracle Corporation
copyright=Copyright \u00A9 2018, 2019, Oracle and/or its affiliates. All rights reserved.
description=The JDK Mission Control Console feature contains all the Console related plug-ins.
descriptionUrl=http://www.oracle.com/missioncontrol

#
#  Copyright (c) 2018, 2019, Oracle and/or its affiliates. All rights reserved.
#
#  DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
#
#  The contents of this file are subject to the terms of either the Universal Permissive License 
#  v 1.0 as shown at http://oss.oracle.com/licenses/upl
#   
#  or the following license:
#   
#  Redistribution and use in source and binary forms, with or without modification, are permitted
#  provided that the following conditions are met:
#   
#  1. Redistributions of source code must retain the above copyright notice, this list of conditions
#  and the following disclaimer.
#   
#  2. Redistributions in binary form must reproduce the above copyright notice, this list of
#  conditions and the following disclaimer in the documentation and/or other materials provided with
#  the distribution.
#   
#  3. Neither the name of the copyright holder nor the names of its contributors may be used to
#  endorse or promote products derived from this software without specific prior written permission.
#
#  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
#  IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
#  FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
#  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
#  DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
#  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
#  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
#  WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
licenseUrl=http://oss.oracle.com/licenses/upl
license=\
Copyright (c) 2018, 2019, Oracle and/or its affiliates. All rights reserved.\n\
\n\
The Universal Permissive License (UPL), Version 1.0\n\
\n\
Subject to the condition set forth below, permission is hereby granted to any \n\
person obtaining a copy of this software, associated documentation and/or data \n\
(collectively the "Software"), free of charge and under any and all copyright \n\
rights in the Software, and any and all patent rights owned or freely \n\
licensable by each licensor hereunder covering either (i) the unmodified \n\
Software as contributed to or provided by such licensor, or (ii) the Larger \n\
Works (as defined below), to deal in both\n\
\n\
(a) the Software, and\n\
\n\
(b) any piece of software and/or hardware listed in the lrgrwrks.txt file if \n\
    one is included with the Software (each a \u201CLarger Work\u201D to which the \n\
    Software is contributed by such licensors), without restriction, including \n\
    without limitation the rights to copy, create derivative works of, \n\
    display, perform, and distribute the Software and make, use, sell, offer \n\
    for sale, import, export, have made, and have sold the Software and the \n\
    Larger Work(s), and to sublicense the foregoing rights on either these or \n\
    other terms.\n\
\n\
This license is subject to the following condition:\n\
\n\
The above copyright notice and either this complete permission notice or at a \n\
minimum a reference to the UPL must be included in all copies or substantial \n\
portions of the Software.\n\
\n\
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR \n\
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, \n\
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE \n\
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER \n\
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, \n\
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE \n\
SOFTWARE.\n\
\n\
\n\
Copyright (c) 2018, 2019 Oracle America, Inc. All rights reserved.\n\
\n\
Redistribution and use in source and binary forms, with or without \n\
modification, are permitted provided that the following conditions are met:\n\
\n\
1. Redistributions of source code must retain the above copyright notice, \n\
   this list of conditions and the following disclaimer.\n\
\n\
2. Redistributions in binary form must reproduce the above copyright notice, \n\
   this list of conditions and the following disclaimer in the documentation \n\
   and/or other materials provided with the distribution.\n\
\n\
3. Neither the name of the copyright holder nor the names of its contributors \n\
   may be used to endorse or promote products derived from this software \n\
   without specific prior written permission.\n\
\n\
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" \n\
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE \n\
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE \n\
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE \n\
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL \n\
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR \n\
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER \n\
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, \n\
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE \n\
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较lunar_python和LunarCalendar两个库的功能
测试农历转换和干支计算
"""

def test_lunar_python():
    """测试lunar_python库"""
    print("🔍 测试 lunar_python 库")
    print("=" * 50)
    
    try:
        from lunar_python import Solar
        
        print("✅ lunar_python 导入成功")
        
        # 测试日期
        test_date = (1988, 3, 15)
        year, month, day = test_date
        
        print(f"\n📅 测试日期: {year}-{month:02d}-{day:02d}")
        
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        
        print(f"农历: {lunar.toString()}")
        
        # 获取八字
        eightChar = lunar.getEightChar()
        
        print(f"\n🎯 四柱八字:")
        print(f"年柱: {eightChar.getYear()}")
        print(f"月柱: {eightChar.getMonth()}")
        print(f"日柱: {eightChar.getDay()}")
        print(f"时柱: {eightChar.getTime()}")
        
        print(f"\n🌟 五行:")
        print(f"年柱: {eightChar.getYearWuXing()}")
        print(f"月柱: {eightChar.getMonthWuXing()}")
        print(f"日柱: {eightChar.getDayWuXing()}")
        print(f"时柱: {eightChar.getTimeWuXing()}")
        
        print(f"\n⭐ 十神:")
        print(f"年柱: {eightChar.getYearShiShenGan()}")
        print(f"月柱: {eightChar.getMonthShiShenGan()}")
        print(f"日柱: {eightChar.getDayShiShenGan()}")
        print(f"时柱: {eightChar.getTimeShiShenGan()}")
        
        return {
            'library': 'lunar_python',
            'lunar_date': lunar.toString(),
            'year_pillar': eightChar.getYear(),
            'month_pillar': eightChar.getMonth(),
            'day_pillar': eightChar.getDay(),
            'time_pillar': eightChar.getTime(),
            'success': True
        }
        
    except Exception as e:
        print(f"❌ lunar_python 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'library': 'lunar_python', 'success': False, 'error': str(e)}

def test_lunar_calendar():
    """测试LunarCalendar库"""
    print("\n🔍 测试 LunarCalendar 库")
    print("=" * 50)
    
    try:
        from LunarCalendar import Converter, Solar
        
        print("✅ LunarCalendar 导入成功")
        
        # 测试日期
        test_date = (1988, 3, 15)
        year, month, day = test_date
        
        print(f"\n📅 测试日期: {year}-{month:02d}-{day:02d}")
        
        solar = Solar(year, month, day)
        lunar = Converter.Solar2Lunar(solar)
        
        print(f"农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查干支属性
        print(f"\n🔍 检查LunarCalendar的干支功能:")
        
        ganzhi_attrs = ['gz_year', 'gz_month', 'gz_day', 'gzYear', 'gzMonth', 'gzDay']
        found_ganzhi = False
        
        for attr in ganzhi_attrs:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    print(f"  {attr}: {value}")
                    found_ganzhi = True
                except Exception as e:
                    print(f"  {attr}: 获取失败 - {e}")
        
        # 检查所有属性
        print(f"\n📋 LunarCalendar对象的所有属性:")
        all_attrs = [attr for attr in dir(lunar) if not attr.startswith('_')]
        for attr in all_attrs[:10]:  # 只显示前10个
            try:
                value = getattr(lunar, attr)
                if not callable(value):
                    print(f"  {attr}: {value}")
            except:
                pass
        
        if not found_ganzhi:
            print("❌ LunarCalendar库不支持干支计算")
        
        return {
            'library': 'LunarCalendar',
            'lunar_date': f"农历{lunar.year}年{lunar.month}月{lunar.day}日",
            'has_ganzhi': found_ganzhi,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'library': 'LunarCalendar', 'success': False, 'error': str(e)}

def create_final_converter():
    """创建最终的转换器脚本"""
    print("\n📝 创建最终转换器")
    print("=" * 50)
    
    converter_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历干支转换器 - 使用lunar_python库
支持完整的年月日时柱计算、五行分析、十神分析
"""

from lunar_python import Solar

def convert_date_to_bazi(year, month, day, hour=12):
    """
    将阳历日期转换为八字信息
    
    Args:
        year: 年份
        month: 月份
        day: 日期
        hour: 小时 (0-23)
    
    Returns:
        dict: 包含完整八字信息的字典
    """
    try:
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        eightChar = lunar.getEightChar()
        
        return {
            'solar_date': f"{year}年{month}月{day}日",
            'lunar_date': lunar.toString(),
            'pillars': {
                'year': eightChar.getYear(),
                'month': eightChar.getMonth(),
                'day': eightChar.getDay(),
                'time': eightChar.getTime()
            },
            'wuxing': {
                'year': eightChar.getYearWuXing(),
                'month': eightChar.getMonthWuXing(),
                'day': eightChar.getDayWuXing(),
                'time': eightChar.getTimeWuXing()
            },
            'shishen': {
                'year': eightChar.getYearShiShenGan(),
                'month': eightChar.getMonthShiShenGan(),
                'day': eightChar.getDayShiShenGan(),
                'time': eightChar.getTimeShiShenGan()
            }
        }
    except Exception as e:
        return {'error': str(e)}

def main():
    """主函数 - 交互式转换器"""
    print("🌙 农历干支转换器")
    print("=" * 50)
    
    while True:
        try:
            print("\\n请输入阳历日期:")
            year = int(input("年份 (如 1988): "))
            month = int(input("月份 (1-12): "))
            day = int(input("日期 (1-31): "))
            hour = int(input("时辰 (0-23，默认12): ") or "12")
            
            result = convert_date_to_bazi(year, month, day, hour)
            
            if 'error' in result:
                print(f"❌ 转换失败: {result['error']}")
                continue
            
            print(f"\\n🌟 转换结果:")
            print(f"阳历: {result['solar_date']}")
            print(f"农历: {result['lunar_date']}")
            
            print(f"\\n🎯 四柱八字:")
            print(f"年柱: {result['pillars']['year']}")
            print(f"月柱: {result['pillars']['month']}")
            print(f"日柱: {result['pillars']['day']}")
            print(f"时柱: {result['pillars']['time']}")
            
            print(f"\\n🌟 五行:")
            print(f"年柱: {result['wuxing']['year']}")
            print(f"月柱: {result['wuxing']['month']}")
            print(f"日柱: {result['wuxing']['day']}")
            print(f"时柱: {result['wuxing']['time']}")
            
            print(f"\\n⭐ 十神:")
            print(f"年柱: {result['shishen']['year']}")
            print(f"月柱: {result['shishen']['month']}")
            print(f"日柱: {result['shishen']['day']}")
            print(f"时柱: {result['shishen']['time']}")
            
            if input("\\n继续转换？(y/n): ").lower() != 'y':
                break
                
        except KeyboardInterrupt:
            print("\\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('bazi_converter.py', 'w', encoding='utf-8') as f:
            f.write(converter_script)
        print("✅ 最终转换器已创建: bazi_converter.py")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🌙 农历库功能对比测试")
    print("=" * 70)
    
    # 测试两个库
    lunar_python_result = test_lunar_python()
    lunar_calendar_result = test_lunar_calendar()
    
    # 总结
    print("\n" + "="*70)
    print("📊 测试总结:")
    print(f"lunar_python: {'✅ 成功' if lunar_python_result['success'] else '❌ 失败'}")
    print(f"LunarCalendar: {'✅ 成功' if lunar_calendar_result['success'] else '❌ 失败'}")
    
    if lunar_python_result['success']:
        print(f"\n🎯 推荐使用: lunar_python")
        print("  - 支持完整的年月日时柱计算")
        print("  - 支持五行分析")
        print("  - 支持十神分析")
        print("  - 专为中国农历和八字设计")
        
        # 创建最终转换器
        create_final_converter()
        
        print("\n🚀 使用方法:")
        print("  python bazi_converter.py  # 交互式转换器")
        print("  python final_lunar_converter.py  # 演示版本")
    
    print("\n🎉 安装完成！您现在可以进行准确的农历干支计算了！")

if __name__ == "__main__":
    main()

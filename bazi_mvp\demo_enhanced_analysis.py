#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示增强的八字分析系统
展示多样化语料输出的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.corpus_manager import corpus_manager
from modules.couple_compatibility import CoupleCompatibilityCalculator

def demo_personality_diversity():
    """演示个性分析的多样性"""
    print("=" * 60)
    print("🎭 个性分析多样性演示")
    print("=" * 60)
    
    day_pillars = ['甲子', '丙寅', '戊辰', '庚午', '壬申']
    
    for day_pillar in day_pillars:
        print(f"\n【{day_pillar}日柱个性分析】")
        print("-" * 30)
        
        # 获取3次不同的分析内容
        for i in range(3):
            personality_content = corpus_manager.get_personality_analysis(day_pillar, count=1)
            if personality_content:
                print(f"{i+1}. {personality_content[0]}")
        print()

def demo_compatibility_analysis():
    """演示合婚分析的丰富性"""
    print("=" * 60)
    print("💕 合婚分析丰富性演示")
    print("=" * 60)
    
    compatibility_levels = ['high_compatibility', 'medium_compatibility', 'low_compatibility']
    level_names = ['高度匹配', '中等匹配', '需要磨合']
    
    for level, name in zip(compatibility_levels, level_names):
        print(f"\n【{name}等级分析】")
        print("-" * 30)
        
        analysis = corpus_manager.get_compatibility_analysis(level, count=2)
        
        if analysis['overall']:
            print(f"总体评价: {analysis['overall'][0]}")
        
        if analysis['communication']:
            print(f"沟通建议: {analysis['communication'][0]}")
        
        if analysis['financial']:
            print(f"财务建议: {analysis['financial'][0]}")
        
        if analysis['career']:
            print(f"事业建议: {analysis['career'][0]}")
        print()

def demo_wuxing_pairing():
    """演示五行配对分析"""
    print("=" * 60)
    print("🌟 五行配对分析演示")
    print("=" * 60)
    
    pairings = [
        ('金', '水', '金水相生'),
        ('水', '木', '水木相生'),
        ('木', '火', '木火相生'),
        ('火', '土', '火土相生'),
        ('土', '金', '土金相生')
    ]
    
    for element1, element2, desc in pairings:
        print(f"\n【{element1}{element2}配对 - {desc}】")
        print("-" * 30)
        
        analysis = corpus_manager.get_wuxing_pairing_analysis(element1, element2)
        
        print(f"配对等级: {analysis['compatibility']}")
        print(f"配对描述: {analysis['description']}")
        
        if analysis['advantages']:
            print("配对优势:")
            for advantage in analysis['advantages']:
                print(f"  • {advantage}")
        
        if analysis['suggestions']:
            print("相处建议:")
            for suggestion in analysis['suggestions']:
                print(f"  • {suggestion}")
        print()

def demo_life_advice():
    """演示生活建议的实用性"""
    print("=" * 60)
    print("🎯 生活建议实用性演示")
    print("=" * 60)
    
    elements = ['金', '木', '水', '火', '土']
    advice_types = ['财运提升建议', '健康养生建议', '事业发展建议']
    
    for element in elements[:3]:  # 只演示前3个五行
        print(f"\n【{element}命人生活指导】")
        print("-" * 30)
        
        for advice_type in advice_types:
            advice_list = corpus_manager.get_life_advice(element, advice_type, count=2)
            if advice_list:
                print(f"\n{advice_type}:")
                for advice in advice_list:
                    print(f"  • {advice}")
        print()

def demo_complete_analysis():
    """演示完整的合婚分析流程"""
    print("=" * 60)
    print("🔮 完整合婚分析演示")
    print("=" * 60)
    
    # 创建测试数据
    male_bazi = {
        'name': '王先生',
        'birth_date': '1988-03-15 09:30',
        'year_pillar': '戊辰',
        'month_pillar': '乙卯',
        'day_pillar': '甲子',
        'hour_pillar': '己巳'
    }
    
    female_bazi = {
        'name': '李女士',
        'birth_date': '1990-07-22 15:45',
        'year_pillar': '庚午',
        'month_pillar': '癸未',
        'day_pillar': '丁卯',
        'hour_pillar': '戊申'
    }
    
    print(f"男方信息: {male_bazi['name']} - {male_bazi['birth_date']}")
    print(f"女方信息: {female_bazi['name']} - {female_bazi['birth_date']}")
    print()
    
    # 创建合婚计算器并进行分析
    calculator = CoupleCompatibilityCalculator()
    
    try:
        result = calculator.calculate_compatibility(male_bazi, female_bazi)
        
        print("【合婚分析结果】")
        print(f"综合评分: {result['overall_score']}分")
        print(f"配对等级: {result['compatibility_level']}")
        print()
        
        print("【详细分析报告】")
        print(result['detailed_analysis'])
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def demo_multiple_runs_comparison():
    """演示多次运行的内容差异"""
    print("=" * 60)
    print("🔄 多次运行内容差异演示")
    print("=" * 60)
    
    print("同一日柱的3次不同分析结果:")
    print("-" * 30)
    
    for i in range(3):
        print(f"\n第{i+1}次分析:")
        personality = corpus_manager.get_personality_analysis('丙子', count=1)
        if personality:
            print(f"  {personality[0]}")
    
    print("\n" + "=" * 30)
    print("同一五行配对的3次不同描述:")
    print("-" * 30)
    
    for i in range(3):
        print(f"\n第{i+1}次分析:")
        wuxing_analysis = corpus_manager.get_wuxing_pairing_analysis('木', '火')
        print(f"  {wuxing_analysis['description']}")

if __name__ == "__main__":
    print("🌟 八字占卜语料库系统演示")
    print("展示丰富多样的分析内容和个性化输出")
    print()
    
    # 演示各种功能
    demo_personality_diversity()
    demo_compatibility_analysis()
    demo_wuxing_pairing()
    demo_life_advice()
    demo_complete_analysis()
    demo_multiple_runs_comparison()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("语料库系统成功提供了丰富、多样化的八字分析内容")
    print("每次分析都能获得不同的表述，避免重复，提升用户体验")
    print("=" * 60)

Key:

JDK-X  - https://bugs.openjdk.java.net/browse/JDK-X
CVE-XXXX-YYYY: https://cve.mitre.org/cgi-bin/cvename.cgi?name=XXXX-YYYY

New in release OpenJDK 8u392 (2023-10-17):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u392

* CVEs
  - CVE-2023-22067
  - CVE-2023-22081
* Security fixes
  - JDK-8286503, JDK-8312367: Enhance security classes
  - JDK-8297856: Improve handling of Bidi characters
  - JDK-8303384: Improved communication in CORBA
  - JDK-8305815, JDK-8307278: Update Libpng to 1.6.39
  - JDK-8309966: Enhanced TLS connections
* Other changes
  - JDK-6722928: Provide a default native GSS-API library on Windows
  - JDK-8040887: [TESTBUG] Remove test/runtime/6925573/SortMethodsTest.java
  - JDK-8042726: [TESTBUG] TEST.groups file was not updated after runtime/6925573/SortMethodsTest.java removal
  - JDK-8139348: Deprecate 3DES and RC4 in Kerberos
  - JDK-8173072: zipfs fails to handle incorrect info-zip "extended timestamp extra field"
  - JDK-8200468: Port the native GSS-API bridge to Windows
  - JDK-8202952: C2: Unexpected dead nodes after matching
  - JDK-8205399: Set node color on pinned HashMap.TreeNode deletion
  - JDK-8209115: adjust libsplashscreen linux ppc64le builds for easier libpng update
  - JDK-8214046: [macosx] Undecorated Frame does not Iconify when set to
  - JDK-8219804: java/net/MulticastSocket/Promiscuous.java fails intermittently due to NumberFormatException
  - JDK-8225687: Newly added sspi.cpp in JDK-6722928 still contains some small errors
  - JDK-8232225: Rework the fix for JDK-8071483
  - JDK-8242330: Arrays should be cloned in several JAAS Callback classes
  - JDK-8253269: The CheckCommonColors test should provide more info on failure
  - JDK-8283441: C2: segmentation fault in ciMethodBlocks::make_block_at(int)
  - JDK-8284910: Buffer clean in PasswordCallback
  - JDK-8287073: NPE from CgroupV2Subsystem.getInstance()
  - JDK-8287663: Add a regression test for JDK-8287073
  - JDK-8295685: Update Libpng to 1.6.38
  - JDK-8295894: Remove SECOM certificate that is expiring in September 2023
  - JDK-8308788: [8u] Remove duplicate HaricaCA.java test
  - JDK-8309122: Bump update version of OpenJDK: 8u392
  - JDK-8309143: [8u] fix archiving inconsistencies in GHA
  - JDK-8310026: [8u] make java_lang_String::hash_code consistent across platforms
  - JDK-8314960: Add Certigna Root CA - 2
  - JDK-8315135: Memory leak in the native implementation of Pack200.Unpacker.unpack()
  - JDK-8317040: Exclude cleaner test failing on older releases

Notes on individual issues:
===========================

other-libs/corba:idl:

8303384: Improved communication in CORBA
========================================
The JDK's CORBA implementation now provides the option to limit
serialisation in stub objects to those with the "IOR:" prefix.  For
ORB constrained stub classes:

* _DynArrayStub
* _DynEnumStub
* _DynFixedStub
* _DynSequenceStub
* _DynStructStub
* _DynUnionStub
* _DynValueStub
* _DynAnyStub
* _DynAnyFactoryStub

this is enabled by default and may be disabled by setting the system
property org.omg.DynamicAny.disableIORCheck to 'true'.

For remote service stub classes:

* _NamingContextStub
* _BindingIteratorStub
* _NamingContextExtStub
* _ServantActivatorStub
* _ServantLocatorStub
* _ServerManagerStub
* _ActivatorStub
* _RepositoryStub
* _InitialNameServiceStub
* _LocatorStub
* _ServerStub

it is disabled by default and may be enabled by setting the system
property org.omg.CORBA.IDL.Stubs.enableIORCheck to 'true'.

security-libs/org.ietf.jgss:

JDK-6722928: Added a Default Native GSS-API Library on Windows
==============================================================

A native GSS-API library named `sspi_bridge.dll` has been added to the
JDK on the Windows platform.  As with native GSS-API library provision
on other operating systems, it will only be loaded when the
`sun.security.jgss.native` system property is set to "true". A user
can still load a third-party native GSS-API library instead by setting
the `sun.security.jgss.lib` system property to the appropriate path.

The library is client-side only and uses the default credentials.
Native GSS support automatically uses cached credentials from the
underlying operating system, so the
`javax.security.auth.useSubjectCredsOnly` system property should be
set to false.

The `com.sun.security.auth.module.Krb5LoginModule` does not call
native JGSS and so its use in your JAAS config should be avoided.

security-libs/org.ietf.jgss:krb5:

JDK-8139348: Deprecate 3DES and RC4 in Kerberos
===============================================
The `des3-hmac-sha1` and `rc4-hmac` Kerberos encryption types (etypes)
are now deprecated and disabled by default.  To re-enable them, you
can either enable all weak crypto (which also includes `des-cbc-crc`
and `des-cbc-md5`) by setting `allow_weak_crypto = true` in the
`krb5.conf` configuration file or explicitly list all the preferred
encryption types using the `default_tkt_enctypes`,
`default_tgs_enctypes`, or `permitted_enctypes` settings.

security-libs/java.security:

JDK-8295894: Removed SECOM Trust System's RootCA1 Root Certificate
==================================================================
The following root certificate from SECOM Trust System has been
removed from the `cacerts` keystore:

Alias Name: secomscrootca1 [jdk]
Distinguished Name: OU=Security Communication RootCA1, O=SECOM Trust.net, C=JP

JDK-8314960: Added Certigna Root CA Certificate
===============================================
The following root certificate has been added to the cacerts
truststore:

Name: Certigna (Dhimyotis)
Alias Name: certignarootca
Distinguished Name: CN=Certigna Root CA, OU=0002 48146308100036, O=Dhimyotis, C=FR


New in release OpenJDK 8u382 (2023-07-18):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u382

* CVEs
  - CVE-2023-22045
  - CVE-2023-22049
* Security fixes
  - JDK-8298676: Enhanced Look and Feel
  - JDK-8300596: Enhance Jar Signature validation
  - JDK-8304468: Better array usages
  - JDK-8305312: Enhanced path handling
* Other changes
  - JDK-8072678: Wrong exception messages in java.awt.color.ICC_ColorSpace
  - JDK-8151460: Metaspace counters can have inconsistent values
  - JDK-8152432: Implement setting jtreg @requires properties vm.flavor, vm.bits, vm.compMode
  - JDK-8185736: missing default exception handler in calls to rethrow_Stub
  - JDK-8186801: Add regression test to test mapping based charsets (generated at build time)
  - JDK-8215105: java/awt/Robot/HiDPIScreenCapture/ScreenCaptureTest.java: Wrong Pixel Color
  - JDK-8241311: Move some charset mapping tests from closed to open
  - JDK-8263059: security/infra/java/security/cert/CertPathValidator/certification/ComodoCA.java fails due to revoked cert
  - JDK-8268558: [TESTBUG] Case 2 in TestP11KeyFactoryGetRSAKeySpec is skipped
  - JDK-8271199: Mutual TLS handshake fails signing client certificate with custom sensitive PKCS11 key
  - JDK-8276841: Add support for Visual Studio 2022
  - JDK-8277881: Missing SessionID in TLS1.3 resumption in compatibility mode
  - JDK-8278851: Correct signer logic for jars signed with multiple digest algorithms
  - JDK-8282345: handle latest VS2022 in abstract_vm_version
  - JDK-8282600: SSLSocketImpl should not use user_canceled workaround when not necessary
  - JDK-8285515: (dc) DatagramChannel.disconnect fails with "Invalid argument" on macOS 12.4
  - JDK-8289301: P11Cipher should not throw out of bounds exception during padding
  - JDK-8293232: Fix race condition in pkcs11 SessionManager
  - JDK-8293815: P11PSSSignature.engineUpdate should not print debug messages during normal operation
  - JDK-8295530: Update Zlib Data Compression Library to Version 1.2.13
  - JDK-8298108: Add a regression test for JDK-8297684
  - JDK-8298271: java/security/SignedJar/spi-calendar-provider/TestSPISigned.java failing on Windows
  - JDK-8301119: Support for GB18030-2022
  - JDK-8301400: Allow additional characters for GB18030-2022 support
  - JDK-8302791: Add specific ClassLoader object to Proxy IllegalArgumentException message
  - JDK-8303028: Update system property for Java SE specification maintenance version
  - JDK-8303462: Bump update version of OpenJDK: 8u382
  - JDK-8304760: Add 2 Microsoft TLS roots
  - JDK-8305165: [8u] ServiceThread::nmethods_do is not called to keep nmethods from being zombied while in the queue
  - JDK-8305681: Allow additional characters for GB18030-2022 (Level 2) support
  - JDK-8305975: Add TWCA Global Root CA
  - JDK-8307134: Add GTS root CAs
  - JDK-8307310: Backport the tests for JDK-8058969 and JDK-8039271 to the OpenJDK8
  - JDK-8307531: [aarch64] JDK8 single-step debugging is extremely slow
  - JDK-8310947: gb18030-2000 not selectable with LANG=zh_CN.GB18030 after JDK-8301119

Notes on individual issues:
===========================

core-libs/java.lang:

JDK-8305681: Allow additional characters for GB18030-2022 (Level 2) support
===========================================================================
In order to support "Implementation Level 2" of the GB18030-2022
standard, the JDK must be able to use characters from the CJK Unified
Ideographs Extension E block of Unicode 8.0.  The addition of these
characters forms Maintenance Release 5 of the Java SE 8 specification,
which is implemented in this release of OpenJDK via the addition of a
new UnicodeBlock instance,
Character.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_E.

core-libs/java.util.jar:

8300596: Enhance Jar Signature validation
=========================================
A System property "jdk.jar.maxSignatureFileSize" is introduced to
configure the maximum number of bytes allowed for the
signature-related files in a JAR file during verification. The default
value is 8000000 bytes (8 MB).

security-libs/java.security:

JDK-8307134: Added 4 GTS Root CA Certificates
=============================================
The following root certificates have been added to the cacerts
truststore:

Name: Google Trust Services LLC
Alias Name: gtsrootcar1
Distinguished Name: CN=GTS Root R1, O=Google Trust Services LLC, C=US

Name: Google Trust Services LLC
Alias Name: gtsrootcar2
Distinguished Name: CN=GTS Root R2, O=Google Trust Services LLC, C=US

Name: Google Trust Services LLC
Alias Name: gtsrootcar3
Distinguished Name: CN=GTS Root R3, O=Google Trust Services LLC, C=US

Name: Google Trust Services LLC
Alias Name: gtsrootcar4
Distinguished Name: CN=GTS Root R4, O=Google Trust Services LLC, C=US

JDK-8304760: Added Microsoft Corporation's 2 TLS Root CA Certificates
=====================================================================
The following root certificates has been added to the cacerts
truststore:

Name: Microsoft Corporation
Alias Name: microsoftecc2017
Distinguished Name: CN=Microsoft ECC Root Certificate Authority 2017, O=Microsoft Corporation, C=US

Name: Microsoft Corporation
Alias Name: microsoftrsa2017
Distinguished Name: CN=Microsoft RSA Root Certificate Authority 2017, O=Microsoft Corporation, C=US

JDK-8305975: Added TWCA Root CA Certificate
===========================================
The following root certificate has been added to the cacerts
truststore:

Name: TWCA
Alias Name: twcaglobalrootca
Distinguished Name: CN=TWCA Global Root CA, OU=Root CA, O=TAIWAN-CA, C=TW

New in release OpenJDK 8u372 (2023-04-18):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u372

* CVEs
  - CVE-2023-21930
  - CVE-2023-21937
  - CVE-2023-21938
  - CVE-2023-21939
  - CVE-2023-21954
  - CVE-2023-21967
  - CVE-2023-21968
* Security fixes
  - JDK-8287404: Improve ping times
  - JDK-8288436: Improve Xalan supports
  - JDK-8294474: Better AES support
  - JDK-8295304: Runtime support improvements
  - JDK-8296496, JDK-8292652: Overzealous check in sizecalc.h prevents large memory allocation
  - JDK-8296676, JDK-8296622: Improve String platform support
  - JDK-8296684: Improve String platform support
  - JDK-8296692: Improve String platform support
  - JDK-8296700: Improve String platform support
  - JDK-8296832: Improve Swing platform support
  - JDK-8297371: Improve UTF8 representation redux
  - JDK-8298191: Enhance object reclamation process
  - JDK-8298310: Enhance TLS session negotiation
  - JDK-8298667: Improved path handling
  - JDK-8299129: Enhance NameService lookups
* New features
  - JDK-8230305: Cgroups v2: Container awareness
* Other changes
  - JDK-6734341: REGTEST fails: SelectionAutoscrollTest.html
  - JDK-6829250: Reg test: java/awt/Toolkit/ScreenInsetsTest/ScreenInsetsTest.java fails in Windows
  - JDK-7001973: java/awt/Graphics2D/CopyAreaOOB.java fails
  - JDK-7124238: [macosx] Font in BasicHTML document is bigger than it should be
  - JDK-7124381: DragSourceListener.dragDropEnd() never been called on completion of dnd operation
  - JDK-8039888: [TEST_BUG] keyboard garbage after javax/swing/plaf/windows/WindowsRootPaneUI/WrongAltProcessing/WrongAltProcessing.java
  - JDK-8042098: [TESTBUG] Test sun/java2d/AcceleratedXORModeTest.java fails on Windows
  - JDK-8065422: Trailing dot in hostname causes TLS handshake to fail with SNI disabled
  - JDK-8072770: [TESTBUG] Some Introspector tests fail with a Java heap bigger than 4GB
  - JDK-8075964: Test java/awt/Mouse/TitleBarDoubleClick/TitleBarDoubleClick.html fails intermittently with timeout error
  - JDK-8137101: [TEST_BUG] javax/swing/plaf/basic/BasicHTML/4251579/bug4251579.java failure due to timing
  - JDK-8142540: [TEST_BUG] Test sun/awt/dnd/8024061/bug8024061.java fails on ubuntu
  - JDK-8156579: Two JavaBeans tests failed
  - JDK-8156581: Cleanup of ProblemList.txt
  - JDK-8159135: [PIT] javax/swing/JMenuItem/8152981/MenuItemIconTest.java always fail
  - JDK-8177560: @headful key can be removed from the tests for JavaSound
  - JDK-8196196: Headful tests should not be run in headless mode
  - JDK-8196467: javax/swing/JInternalFrame/Test6325652.java fails
  - JDK-8197408: Bad pointer comparison and small cleanup in os_linux.cpp
  - JDK-8203485: [freetype] text rotated on 180 degrees is too narrow
  - JDK-8205959: Do not restart close if errno is EINTR
  - JDK-8216366: Add rationale to PER_CPU_SHARES define
  - JDK-8226236: win32: gc/metaspace/TestCapacityUntilGCWrapAround.java fails
  - JDK-8228585: jdk/internal/platform/cgroup/TestCgroupMetrics.java - NumberFormatException because of large long values (memory limit_in_bytes)
  - JDK-8229182: [TESTBUG] runtime/containers/docker/TestMemoryAwareness.java test fails on SLES12
  - JDK-8229202: Docker reporting causes secondary crashes in error handling
  - JDK-8231111: Cgroups v2: Rework Metrics in java.base so as to recognize unified hierarchy
  - JDK-8232207: Linux os::available_memory re-reads cgroup configuration on every invocation
  - JDK-8233570: [TESTBUG] HTMLEditorKit test bug5043626.java is failing on macos
  - JDK-8234484: Add ability to configure third port for remote JMX
  - JDK-8237479: 8230305 causes slowdebug build failure
  - JDK-8239559: Cgroups: Incorrect detection logic on some systems
  - JDK-8239785: Cgroups: Incorrect detection logic on old systems in hotspot
  - JDK-8239827: The test OpenByUNCPathNameTest.java should be changed to be manual
  - JDK-8240189: [TESTBUG] Some cgroup tests are failing after JDK-8231111
  - JDK-8241087: Build failure with VS 2019 (16.5.0) due to C2039 and C2873
  - JDK-8242468: VS2019 build missing vcruntime140_1.dll
  - JDK-8243543: jtreg test security/infra/java/security/cert/CertPathValidator/certification/BuypassCA.java fails
  - JDK-8244500: jtreg test error in test/hotspot/jtreg/containers/docker/TestMemoryAwareness.java
  - JDK-8245543: Cgroups: Incorrect detection logic on some systems (still reproducible)
  - JDK-8245654: Add Certigna Root CA
  - JDK-8247676: vcruntime140_1.dll is not needed on 32-bit Windows
  - JDK-8248899: security/infra/java/security/cert/CertPathValidator/certification/QuoVadisCA.java fails, Certificate has been revoked
  - JDK-8252359: HotSpot Not Identifying it is Running in a Container
  - JDK-8252957: Wrong comment in CgroupV1Subsystem::cpu_quota
  - JDK-8253435: Cgroup: 'stomping of _mount_path' crash if manually mounted cpusets exist
  - JDK-8253714: [cgroups v2] Soft memory limit incorrectly using memory.high
  - JDK-8253727: [cgroups v2] Memory and swap limits reported incorrectly
  - JDK-8253797: [cgroups v2] Account for the fact that swap accounting is disabled on some systems
  - JDK-8253939: [TESTBUG] Increase coverage of the cgroups detection code
  - JDK-8254001: [Metrics] Enhance parsing of cgroup interface files for version detection
  - JDK-8254717: isAssignableFrom checks in KeyFactorySpi.engineGetKeySpec appear to be backwards
  - JDK-8254997: Remove unimplemented OSContainer::read_memory_limit_in_bytes
  - JDK-8257620: Do not use objc_msgSend_stret to get macOS version
  - JDK-8262379: Add regression test for JDK-8257746
  - JDK-8263404: RsaPrivateKeySpec is always recognized as RSAPrivateCrtKeySpec in RSAKeyFactory.engineGetKeySpec
  - JDK-8266391: Replace use of reflection in jdk.internal.platform.Metrics
  - JDK-8270317: Large Allocation in CipherSuite
  - JDK-8275535: Retrying a failed authentication on multiple LDAP servers can lead to users blocked
  - JDK-8275650: Problemlist java/io/File/createTempFile/SpecialTempFile.java for Windows 11
  - JDK-8275713: TestDockerMemoryMetrics test fails on recent runc
  - JDK-8278951: containers/cgroup/PlainRead.java fails on Ubuntu 21.10
  - JDK-8280048: Missing comma in copyright header
  - JDK-8282398: EndingDotHostname.java test fails because SSL cert expired
  - JDK-8282511: Use fixed certificate validation date in SSLExampleCert template
  - JDK-8282947: JFR: Dump on shutdown live-locks in some conditions
  - JDK-8283277: ISO 4217 Amendment 171 Update
  - JDK-8283606: Tests may fail with zh locale on MacOS
  - JDK-8284102: [TESTBUG] [11u] Retroactively add regression test for JDK-8272124
  - JDK-8284690: [macos] VoiceOver : Getting java.lang.IllegalArgumentException: Invalid location on Editable JComboBox
  - JDK-8284756: [11u] Remove unused isUseContainerSupport in CgroupV1Subsystem
  - JDK-8284977: MetricsTesterCgroupV2.getLongValueEntryFromFile fails when named value doesn't exist
  - JDK-8286624: Regression Test CoordinateTruncationBug.java fails on OL8.3
  - JDK-8287107: CgroupSubsystemFactory.setCgroupV2Path asserts with freezer controller
  - JDK-8287109: Distrust.java failed with CertificateExpiredException
  - JDK-8287463: JFR: Disable TestDevNull.java on Windows
  - JDK-8287741: Fix of JDK-8287107 (unused cgv1 freezer controller) was incomplete
  - JDK-8289549: ISO 4217 Amendment 172 Update
  - JDK-8289695: [TESTBUG] TestMemoryAwareness.java fails on cgroups v2 and crun
  - JDK-8291570: [TESTBUG] Part of JDK-8250984 absent from 11u
  - JDK-8292083: Detected container memory limit may exceed physical machine memory
  - JDK-8292541: [Metrics] Reported memory limit may exceed physical machine memory
  - JDK-8293472: Incorrect container resource limit detection if manual cgroup fs mounts present
  - JDK-8293540: [Metrics] Incorrectly detected resource limits with additional cgroup fs mounts
  - JDK-8293767: AWT test TestSinhalaChar.java has old SCCS markings
  - JDK-8294307: ISO 4217 Amendment 173 Update
  - JDK-8294767: 8u contains two copies of test/../FileUtils.java, one uses JDK9+ features
  - JDK-8295322: Tests for JDK-8271459 were not backported to 11u
  - JDK-8295952: Problemlist existing compiler/rtm tests also on x86
  - JDK-8295982: Failure in sun/security/tools/keytool/WeakAlg.java - ks: The process cannot access the file because it is being used by another process
  - JDK-8296239: ISO 4217 Amendment 174 Update
  - JDK-8296480: java/security/cert/pkix/policyChanges/TestPolicy.java is failing
  - JDK-8296485: BuildEEBasicConstraints.java test fails with SunCertPathBuilderException
  - JDK-8296632: Write a test to verify the content change of TextArea sends TextEvent
  - JDK-8296957: One more cast in SAFE_SIZE_NEW_ARRAY2
  - JDK-8297329: [8u] hotspot needs to recognise VS2019
  - JDK-8297739: Bump update version of OpenJDK: 8u372
  - JDK-8297996: [8u] generated images are broken due to renaming of MSVC runtime DLL's
  - JDK-8298027: Remove SCCS id's from awt jtreg tests
  - JDK-8298307: Enable hotspot/tier1 for 32-bit builds in GHA for 8u
  - JDK-8299439: java/text/Format/NumberFormat/CurrencyFormat.java fails for hr_HR
  - JDK-8299445: EndingDotHostname.java fails because of compilation errors
  - JDK-8299483: ProblemList java/text/Format/NumberFormat/CurrencyFormat.java
  - JDK-8299548: Fix hotspot/test/runtime/Metaspace/MaxMetaspaceSizeTest.java in 8u
  - JDK-8299804: Fix non-portable code in hotspot shell tests in 8u
  - JDK-8300014: Some backports placed the tests in the wrong location
  - JDK-8300119: CgroupMetrics.getTotalMemorySize0() can report invalid results on 32 bit systems
  - JDK-8301122: [8u] Fix unreliable vs2010 download link
  - JDK-8301143: [TESTBUG] jfr/event/sampling/TestNative was backported to JDK8u without proper native wrapper
  - JDK-8301246: NPE in FcFontManager.getDefaultPlatformFont() on Linux without installed fontconfig
  - JDK-8301332: [8u] Fix writing of test files after the cgroups v2 backport
  - JDK-8301550: [8u] Enable additional linux build testing in GitHub
  - JDK-8301620: [8u] some shell tests are passed but have unexpected operator errors
  - JDK-8301760: Fix possible leak in SpNegoContext dispose
  - JDK-8303408: [AIX] Broken jdk8u build after JDK-8266391
  - JDK-8303828: [Solaris] Broken jdk8u build after JDK-8266391
  - JDK-8304053: Revert os specific stubs for SystemMetrics
  - JDK-8305113: (tz) Update Timezone Data to 2023c

Notes on individual issues:
===========================

hotspot:
core-libs:

JDK-8305562: Cgroups v2: Container awareness
============================================
The HotSpot runtime code as well as the core libraries code in the JDK
has been updated in order to detect a cgroup v2 host system when
running OpenJDK within a Linux container.

Since the 8u202 release of OpenJDK, the container detection code
recognized cgroup v1 (legacy) host Linux systems. With 8u372 and later
releases, both versions of the underlying cgroups pseudo filesystem
will be detected and corresponding container limits applied to the
OpenJDK runtime.

Without this enhancement, OpenJDK would not apply container resource
limits when running on a cgroup v2 Linux host system, but would use
the underlying hosts' resource limits instead.

client-libs/javax.swing:

JDK-8296832: Improve Swing platform support
===========================================
Earlier OpenJDK releases would always render HTML object tags embedded in
Swing HTML components. With this release, rendering only occurs when the
new system property "swing.html.object" is set to true. By default, it
is set to false.

core-svc/javax.management:

JDK-8234484: Added Ability to Configure Third Port for Remote JMX
=================================================================
A local access port can now be configured for JMX connections by
setting the property `com.sun.management.jmxremote.local.port`. This
local port was previously selected at random, which could lead to port
collisions. The property works in the same way as the existing
properties for configuring the remote access port
(`com.sun.management.jmxremote.port`) and the RMI port
(`com.sun.management.jmxremote.rmi.port`)

security-libs/java.security:

JDK-8245654: Added Certigna(Dhimyotis) Root CA Certificate
==========================================================
The following root certificate has been added to the cacerts truststore:

Name: Certigna (Dhimyotis)
Alias Name: certignarootca
Distinguished Name: CN=Certigna, O=Dhimyotis, C=FR

New in release OpenJDK 8u362 (2023-01-17):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u362
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u362.html

* CVEs
  - CVE-2023-21830
  - CVE-2023-21843
* Security fixes
  - JDK-8285021: Improve CORBA communication
  - JDK-8286496: Improve Thread labels
  - JDK-8288516: Enhance font creation
  - JDK-8289350: Better media supports
  - JDK-8293554: Enhanced DH Key Exchanges
  - JDK-8293598: Enhance InetAddress address handling
  - JDK-8293717: Objective view of ObjectView
  - JDK-8293734: Improve BMP image handling
  - JDK-8293742: Better Banking of Sounds
  - JDK-8295687: Better BMP bounds
* Other changes
  - JDK-6885993: Named Thread: introduce print() and print_on(outputStream* st) methods
  - JDK-7124218: [TEST_BUG] [macosx] Space should select cell in the JTable
  - JDK-8054066: com/sun/jdi/DoubleAgentTest.java fails with timeout
  - JDK-8067941: [TESTBUG] Fix tests for OS with 64K page size.
  - JDK-8071530: Update OS detection code to reflect Windows 10 version change
  - JDK-8073464: GC workers do not have thread names
  - JDK-8079255: [TEST_BUG] [macosx] Test closed/java/awt/Robot/RobotWheelTest/RobotWheelTest fails for Mac only
  - JDK-8129827: [TEST_BUG] Test java/awt/Robot/RobotWheelTest/RobotWheelTest.java fails
  - JDK-8148005: One byte may be corrupted by get_datetime_string()
  - JDK-8159599: [TEST_BUG] java/awt/Modal/ModalInternalFrameTest/ModalInternalFrameTest.java
  - JDK-8159720: Failure of C2 compilation with tiered prevents some C1 compilations
  - JDK-8195607: sun/security/pkcs11/Secmod/TestNssDbSqlite.java failed with "NSS initialization failed" on NSS 3.34.1
  - JDK-8197859: VS2017 Complains about UINTPTR_MAX definition in globalDefinitions_VisCPP.hpp
  - JDK-8206456: [TESTBUG] docker jtreg tests fail on systems without cpuset.effective_cpus / cpuset.effective_mems
  - JDK-8221529: [TESTBUG] Docker tests use old/deprecated image on AArch64
  - JDK-8224506: [TESTBUG] TestDockerMemoryMetrics.java fails with exitValue = 137
  - JDK-8233551: [TESTBUG] SelectEditTableCell.java fails on MacOS
  - JDK-8241086: Test runtime/NMT/HugeArenaTracking.java is failing on 32bit Windows
  - JDK-8253702: BigSur version number reported as 10.16, should be 11.nn
  - JDK-8255559: Leak File Descriptors Because of ResolverLocalFilesystem#engineResolveURI()
  - JDK-8265527: tools/javac/diags/CheckExamples.java fails after JDK-8078024 8u backport
  - JDK-8269039: Disable SHA-1 Signed JARs
  - JDK-8269850: Most JDK releases report macOS version 12 as 10.16 instead of 12.0
  - JDK-8270344: Session resumption errors
  - JDK-8271459: C2: Missing NegativeArraySizeException when creating StringBuilder with negative capacity
  - JDK-8273176: handle latest VS2019 in abstract_vm_version
  - JDK-8274563: jfr/event/oldobject/TestClassLoaderLeak.java fails when GC cycles are not happening
  - JDK-8274840: Update OS detection code to recognize Windows 11
  - JDK-8275887: jarsigner prints invalid digest/signature algorithm warnings if keysize is weak/disabled
  - JDK-8280890: Cannot use '-Djava.system.class.loader' with class loader in signed JAR
  - JDK-8283277: ISO 4217 Amendment 171 Update
  - JDK-8283903: GetContainerCpuLoad does not return the correct result in share mode
  - JDK-8284389: Improve stability of GHA Pre-submit testing by caching cygwin installer
  - JDK-8284622: Update versions of some Github Actions used in JDK workflow
  - JDK-8286582: Build fails on macos aarch64 when using --with-zlib=bundled
  - JDK-8288928: Incorrect GPL header in pnglibconf.h (backport of JDK-8185041)
  - JDK-8289549: ISO 4217 Amendment 172 Update
  - JDK-8292762: Remove .jcheck directories from jdk8u subcomponents
  - JDK-8293181: Bump update version of OpenJDK: 8u362
  - JDK-8293461: Add a test for JDK-8290832
  - JDK-8293828: JFR: jfr/event/oldobject/TestClassLoaderLeak.java still fails when GC cycles are not happening
  - JDK-8294307: ISO 4217 Amendment 173 Update
  - JDK-8294357: (tz) Update Timezone Data to 2022d
  - JDK-8294863: Enable partial tier1 testing in GHA for JDK8
  - JDK-8295164: JDK 8 jdi tests should not use tasklist command on Windows
  - JDK-8295173: (tz) Update Timezone Data to 2022e
  - JDK-8295288: Some vm_flags tests associate with a wrong BugID
  - JDK-8295714: GHA ::set-output is deprecated and will be removed
  - JDK-8295723: security/infra/wycheproof/RunWycheproof.java fails with Assertion Error
  - JDK-8295915: Problemlist compiler/rtm failures specific to 8u
  - JDK-8295950: Enable langtools/tier1 in GHA for 8u
  - JDK-8296108: (tz) Update Timezone Data to 2022f
  - JDK-8296239: ISO 4217 Amendment 174 Update
  - JDK-8296555: Enable hotspot/tier1 for 64-bit builds in GHA for 8u
  - JDK-8296715: CLDR v42 update for tzdata 2022f
  - JDK-8296959: Fix hotspot shell tests of 8u on multilib systems
  - JDK-8297141: Fix hotspot/test/runtime/SharedArchiveFile/DefaultUseWithClient.java for 8u
  - JDK-8297804: (tz) Update Timezone Data to 2022g
  - JDK-8299439: java/text/Format/NumberFormat/CurrencyFormat.java fails for hr_HR
  - JDK-8299483: ProblemList java/text/Format/NumberFormat/CurrencyFormat.java
  - JDK-8300178: JDK-8286496 causes build failure on older GCC
  - JDK-8300225: JDK-8288516 causes build failure on Windows + VS2010

Notes on individual issues:
===========================

client-libs/javax.imageio:

JDK-8295687: Better BMP bounds
==============================
Loading a linked ICC profile within a BMP image is now disabled by
default. To re-enable it, set the new system property
`sun.imageio.bmp.enabledLinkedProfiles` to `true`.  This new property
replaces the old property,
`sun.imageio.plugins.bmp.disableLinkedProfiles`.

client-libs/javax.sound:

JDK-8293742: Better Banking of Sounds
=====================================
Previously, the SoundbankReader implementation,
`com.sun.media.sound.JARSoundbankReader`, would download a JAR
soundbank from a URL.  This behaviour is now disabled by default. To
re-enable it, set the new system property `jdk.sound.jarsoundbank` to
`true`.

hotspot/runtime:

JDK-8274840: Release Now Recognises Windows 11
==============================================
This release now correctly sets the `os.name` property to `Windows
11`, as would be expected.

other-libs/corba:idl:

JDK-8285021: Improve CORBA communication
========================================
The JDK's CORBA implementation now refuses by default to deserialize
objects, unless they have the "IOR:" prefix.  The previous behaviour
can be re-enabled by setting the new property
`com.sun.CORBA.ORBAllowDeserializeObject` to `true`.

security-libs/java.security:

JDK-8269039: Disabled SHA-1 Signed JARs
=======================================
JARs signed with SHA-1 algorithms are now restricted by default and
treated as if they were unsigned. This applies to the algorithms used
to digest, sign, and optionally timestamp the JAR. It also applies to
the signature and digest algorithms of the certificates in the
certificate chain of the code signer and the Timestamp Authority, and
any CRLs or OCSP responses that are used to verify if those
certificates have been revoked. These restrictions also apply to
signed JCE providers.

To reduce the compatibility risk for JARs that have been previously
timestamped, there is one exception to this policy:

- Any JAR signed with SHA-1 algorithms and timestamped prior to
  January 01, 2019 will not be restricted.

This exception may be removed in a future JDK release. To determine if
your signed JARs are affected by this change, run:

$ jarsigner -verify -verbose -certs`

on the signed JAR, and look for instances of "SHA1" or "SHA-1" and
"disabled" and a warning that the JAR will be treated as unsigned in
the output.

For example:

   Signed by "CN="Signer""
   Digest algorithm: SHA-1 (disabled)
   Signature algorithm: SHA1withRSA (disabled), 2048-bit key

   WARNING: The jar will be treated as unsigned, because it is signed with a weak algorithm that is now disabled by the security property:

   jdk.jar.disabledAlgorithms=MD2, MD5, RSA keySize < 1024, DSA keySize < 1024, SHA1 denyAfter 2019-01-01

JARs affected by these new restrictions should be replaced or
re-signed with stronger algorithms.

Users can, *at their own risk*, remove these restrictions by modifying
the `java.security` configuration file (or override it by using the
`java.security.properties` system property) and removing "SHA1 usage
SignedJAR & denyAfter 2019-01-01" from the
`jdk.certpath.disabledAlgorithms` security property and "SHA1
denyAfter 2019-01-01" from the `jdk.jar.disabledAlgorithms` security
property.

New in release OpenJDK 8u352 (2022-10-18):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u352
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u352.txt

* Security fixes
  - JDK-8282252: Improve BigInteger/Decimal validation
  - JDK-8285662: Better permission resolution
  - JDK-8286511: Improve macro allocation
  - JDK-8286519: Better memory handling
  - JDK-8286526, CVE-2022-21619: Improve NTLM support
  - JDK-8286533, CVE-2022-21626: Key X509 usages
  - JDK-8286910, CVE-2022-21624: Improve JNDI lookups
  - JDK-8286918, CVE-2022-21628: Better HttpServer service
  - JDK-8288508: Enhance ECDSA usage
* Other changes
  - JDK-7131823: bug in GIFImageReader
  - JDK-7186258: InetAddress$Cache should replace currentTimeMillis with nanoTime for more precise and accurate
  - JDK-8028265: Add legacy tz tests to OpenJDK
  - JDK-8039955: [TESTBUG] jdk/lambda/LambdaTranslationTest1 - java.lang.AssertionError: expected [d:1234.000000] but found [d:1234,000000]
  - JDK-8049228: Improve multithreaded scalability of InetAddress cache
  - JDK-8071507: (ref) Clear phantom reference as soft and weak references do
  - JDK-8087283: Add support for the XML Signature here() function to the JDK XPath implementation
  - JDK-8130895: Test javax/swing/system/6799345/TestShutdown.java fails on Solaris11 Sparcv9
  - JDK-8136354: [TEST_BUG] Test  java/awt/image/RescaleOp/RescaleAlphaTest.java with Bad action for script
  - JDK-8139668: Generate README-build.html from markdown
  - JDK-8143847: Remove REF_CLEANER reference category
  - JDK-8147862: Null check too late in sun.net.httpserver.ServerImpl
  - JDK-8150669: C1 intrinsic for Class.isPrimitive
  - JDK-8155742: [Windows] robot.keyPress(KeyEvent.VK_ALT_GRAPH) throws java.lang.IllegalArgumentException in windows
  - JDK-8173339: AArch64: Fix minimum stack size computations
  - JDK-8173361: various crashes in JvmtiExport::post_compiled_method_load
  - JDK-8175797: (ref) Reference::enqueue method should clear the reference object before enqueuing
  - JDK-8178832: (ref) jdk.lang.ref.disableClearBeforeEnqueue property is ignored
  - JDK-8183107: PKCS11 regression regarding checkKeySize
  - JDK-8193780: (ref) Remove the undocumented "jdk.lang.ref.disableClearBeforeEnqueue" system property
  - JDK-8194873: right ALT key hotkeys no longer work in Swing components
  - JDK-8201793: (ref) Reference object should not support cloning
  - JDK-8214427: probable bug in logic of ConcurrentHashMap.addCount()
  - JDK-8232950: SUNPKCS11 Provider incorrectly check key length for PSS Signatures.
  - JDK-8233019: java.lang.Class.isPrimitive() (C1) returns wrong result if Klass* is aligned to 32bit
  - JDK-8235218: Minimal VM is broken after JDK-8173361
  - JDK-8235385: Crash on aarch64 JDK due to long offset
  - JDK-8245263: Enable TLSv1.3 by default on JDK 8u for Client roles
  - JDK-8254178: Remove .hgignore
  - JDK-8254318: Remove .hgtags
  - JDK-8256722: handle VC++:1927 VS2019 in  abstract_vm_version
  - JDK-8260589: Crash in JfrTraceIdLoadBarrier::load(_jclass*)
  - JDK-8280963: Incorrect PrintFlags formatting on Windows
  - JDK-8282538: PKCS11 tests fail on CentOS Stream 9
  - JDK-8283849: AsyncGetCallTrace may crash JVM on guarantee
  - JDK-8285400: Add '@apiNote' to the APIs defined in Java SE 8 MR 3
  - JDK-8285497: Add system property for Java SE specification maintenance version
  - JDK-8287132: Retire Runtime.runFinalizersOnExit so that it always throws UOE
  - JDK-8287508: The tests added to jdk-8 by 8235385 are to be ported to jdk-11
  - JDK-8287521: Bump update version of OpenJDK: 8u352
  - JDK-8288763: Pack200 extraction failure with invalid size
  - JDK-8288865: [aarch64] LDR instructions must use legitimized addresses
  - JDK-8290000: Bump macOS GitHub actions to macOS 11
  - JDK-8292579: (tz) Update Timezone Data to 2022c
  - JDK-8292688: Support Security properties in security.testlibrary.Proc

Notes on individual issues:
===========================

core-libs/java.lang:

JDK-8201793: (ref) Reference object should not support cloning
==============================================================
`java.lang.ref.Reference::clone` method always throws
`CloneNotSupportedException`. `Reference` objects cannot be
meaningfully cloned. To create a new Reference object, call the
constructor to create a `Reference` object with the same referent and
reference queue instead.

JDK-8175797: (ref) Reference::enqueue method should clear the reference object before enqueuing
===============================================================================================
`java.lang.ref.Reference.enqueue` method clears the reference object
before it is added to the registered queue. When the `enqueue` method
is called, the reference object is cleared and `get()` method will
return null in OpenJDK 8u352.

Typically when a reference object is enqueued, it is expected that the
reference object is cleared explicitly via the `clear` method to avoid
memory leak because its referent is no longer referenced. In other
words the `get` method is expected not to be called in common cases
once the `enqueue`method is called. In the case when the `get` method
from an enqueued reference object and existing code attempts to access
members of the referent, `NullPointerException` may be thrown. Such
code will need to be updated.

JDK-8071507: (ref) Clear phantom reference as soft and weak references do
=========================================================================
This enhancement changes phantom references to be automatically
cleared by the garbage collector as soft and weak references.

An object becomes phantom reachable after it has been finalized. This
change may cause the phantom reachable objects to be GC'ed earlier -
previously the referent is kept alive until PhantomReference objects
are GC'ed or cleared by the application. This potential behavioral
change might only impact existing code that would depend on
PhantomReference being enqueued rather than when the referent be freed
from the heap.

core-libs/java.net:

JDK-8286918: Better HttpServer service
======================================
The HttpServer can be optionally configured with a maximum connection
limit by setting the jdk.httpserver.maxConnections system property. A
value of 0 or a negative integer is ignored and considered to
represent no connection limit. In the case of a positive integer
value, any newly accepted connections will be first checked against
the current count of established connections and, if the configured
limit has been reached, then the newly accepted connection will be
closed immediately.

security-libs/javax.net.ssl:

JDK-8282859: Enable TLSv1.3 by Default on JDK 8 for Client Roles
================================================================
The TLSv1.3 implementation is now enabled by default for client roles
in 8u352. It has been enabled by default for server roles since 8u272.

Note that TLS 1.3 is not directly compatible with previous
versions. Enabling it on the client may introduce compatibility issues
on either the server or the client side. Here are some more details on
potential compatibility issues that you should be aware of:

* TLS 1.3 uses a half-close policy, while TLS 1.2 and prior versions
  use a duplex-close policy. For applications that depend on the
  duplex-close policy, there may be compatibility issues when
  upgrading to TLS 1.3.

* The signature_algorithms_cert extension requires that pre-defined
  signature algorithms are used for certificate authentication. In
  practice, however, an application may use non-supported signature
  algorithms.

* The DSA signature algorithm is not supported in TLS 1.3. If a server
  is configured to only use DSA certificates, it cannot upgrade to TLS
  1.3.

* The supported cipher suites for TLS 1.3 are not the same as TLS 1.2
  and prior versions. If an application hard-codes cipher suites which
  are no longer supported, it may not be able to use TLS 1.3 without
  modifying the application code.

* The TLS 1.3 session resumption and key update behaviors are
  different from TLS 1.2 and prior versions. The compatibility should
  be minimal, but it could be a risk if an application depends on the
  handshake details of the TLS protocols.

The TLS 1.3 protocol can be disabled by using the jdk.tls.client.protocols
system property:

java -Djdk.tls.client.protocols="TLSv1.2" ...

Alternatively, an application can explicitly set the enabled protocols
with the javax.net.ssl APIs e.g.

sslSocket.setEnabledProtocols(new String[] {"TLSv1.2"});

or:

SSLParameters params = sslSocket.getSSLParameters();
params.setProtocols(new String[] {"TLSv1.2"});
sslSocket.setSSLParameters(params);

New in release OpenJDK 8u345 (2022-08-01):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u345
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u345.txt

* Other changes
  - JDK-8290832: It is no longer possible to change "user.dir" in the JDK8
  - JDK-8291568: Bump update version of OpenJDK: 8u345

Notes on individual issues:
===========================

core-libs/java.io:

JDK-8290832: It is no longer possible to change "user.dir" in the JDK8
======================================================================
A change, JDK-8194154, was introduced in the 8u342 release of OpenJDK
causing the JDK to ignore attempts to set the `user.dir` property.
While this change is suitable for a major release (it was originally
introduced in the initial release of OpenJDK 11), changing the
behaviour of such a property in an update release creates
compatibility issues in software that relies on the behaviour in prior
versions of OpenJDK 8.  As a result, we have reverted this change in
8u345.

New in release OpenJDK 8u342 (2022-07-19):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u342
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u342.txt

* Security fixes
  - JDK-8272243: Improve DER parsing
  - JDK-8272249: Better properties of loaded Properties
  - JDK-8277608: Address IP Addressing
  - JDK-8281859, CVE-2022-21540: Improve class compilation
  - JDK-8281866, CVE-2022-21541: Enhance MethodHandle invocations
  - JDK-8283190: Improve MIDI processing
  - JDK-8284370: Improve zlib usage
  - JDK-8285407, CVE-2022-34169: Improve Xalan supports
* Other changes
  - JDK-8031567: Better model for storing source revision information
  - JDK-8076190: Customizing the generation of a PKCS12 keystore
  - JDK-8129572: Cleanup usage of getResourceAsStream in jaxp
  - JDK-8132256: jaxp: Investigate removal of com/sun/org/apache/bcel/internal/util/ClassPath.java
  - JDK-8168926: C2: Bytecode escape analyzer crashes due to stack overflow
  - JDK-8170385: JDK-8031567 broke source bundles
  - JDK-8170392: JDK-8031567 broke builds from source bundles
  - JDK-8170530: bash configure output contains a typo in a suggested library name
  - JDK-8190753: (zipfs): Accessing a large entry (> 2^31 bytes) leads to a negative initial size for ByteArrayOutputStream
  - JDK-8194154: System property user.dir should not be changed
  - JDK-8202142: jfr/event/io/TestInstrumentation is unstable
  - JDK-8209771: jdk.test.lib.Utils::runAndCheckException error
  - JDK-8221988: add possibility to build with Visual Studio 2019
  - JDK-8223396: [TESTBUG] several jfr tests do not clean up files created in /tmp
  - JDK-8230865: [TESTBUG] jdk/jfr/event/io/EvilInstrument.java fails at-run shell MakeJAR.sh target
  - JDK-8235211: serviceability/attach/RemovingUnixDomainSocketTest.java fails with AttachNotSupportedException: Unable to open socket file
  - JDK-8244973: serviceability/attach/RemovingUnixDomainSocketTest.java fails "stderr was not empty"
  - JDK-8248876: LoadObject with bad base address created for exec file on linux
  - JDK-8253424: Add support for running pre-submit testing using GitHub Actions
  - JDK-8253865: Pre-submit testing using GitHub Actions does not detect failures reliably
  - JDK-8254054: Pre-submit testing using GitHub Actions should not use the deprecated set-env command
  - JDK-8254173: Add Zero, Minimal hotspot targets to submit workflow
  - JDK-8254175: Build no-pch configuration in debug mode for submit checks
  - JDK-8254282: Add Linux x86_32 builds to submit workflow
  - JDK-8255239: The timezone of the hs_err_pid log file is corrupted in Japanese locale
  - JDK-8255305: Add Linux x86_32 tier1 to submit workflow
  - JDK-8255352: Archive important test outputs in submit workflow
  - JDK-8255373: Submit workflow artifact name is always "test-results_.zip"
  - JDK-8255895: Submit workflow artifacts miss hs_errs/replays due to ZIP include mismatch
  - JDK-8256127: Add cross-compiled foreign architectures builds to submit workflow
  - JDK-8256277: Github Action build on macOS should define OS and Xcode versions
  - JDK-8256354: Github Action build on Windows should define OS and MSVC versions
  - JDK-8256393: Github Actions build on Linux should define OS and GCC versions
  - JDK-8256414: add optimized build to submit workflow
  - JDK-8256747: GitHub Actions: decouple the hotspot build-only jobs from Linux x64 testing
  - JDK-8257056: Submit workflow should apt-get update to avoid package installation errors
  - JDK-8259924: GitHub actions fail on Linux x86_32 with "Could not configure libc6:i386"
  - JDK-8260460: GitHub actions still fail on Linux x86_32 with "Could not configure libc6:i386"
  - JDK-8261107: ArrayIndexOutOfBoundsException in the ICC_Profile.getInstance(InputStream)
  - JDK-8263667: Avoid running GitHub actions on branches named pr/*
  - JDK-8266187: Memory leak in appendBootClassPath()
  - JDK-8274658: ISO 4217 Amendment 170 Update
  - JDK-8274751: Drag And Drop hangs on Windows
  - JDK-8278138: OpenJDK8 fails to start on Windows 8.1 after upgrading compiler to VS2017
  - JDK-8279669: test/jdk/com/sun/jdi/TestScaffold.java uses wrong condition
  - JDK-8281814: Debuginfo.diz contains redundant build path after backport JDK-8025936
  - JDK-8282225: GHA: Allow one concurrent run per PR only
  - JDK-8282458: Update .jcheck/conf file for 8u move to git
  - JDK-8282552: Bump update version of OpenJDK: 8u342
  - JDK-8283350: (tz) Update Timezone Data to 2022a
  - JDK-8284620: CodeBuffer may leak _overflow_arena
  - JDK-8284772: 8u GHA: Use GCC Major Version Dependencies Only
  - JDK-8285445: cannot open file "NUL:"
  - JDK-8285523: Improve test java/io/FileOutputStream/OpenNUL.java
  - JDK-8285591: [11] add signum checks in DSA.java engineVerify
  - JDK-8285727: [11u, 17u] Unify fix for JDK-8284920 with version from head
  - JDK-8286989: Build failure on macOS after 8281814
  - JDK-8287537: 8u JDK-8284620 backport broke AArch64 build

Notes on individual issues:
===========================

security-libs/java.security:

JDK-8215293: Customizing PKCS12 keystore Generation
===================================================
New system and security properties have been added to enable users to
customize the generation of PKCS #12 keystores. This includes
algorithms and parameters for key protection, certificate protection,
and MacData. The detailed explanation and possible values for these
properties can be found in the "PKCS12 KeyStore properties" section of
the `java.security` file.

Also, support for the following SHA-2 based HmacPBE algorithms has
been added to the SunJCE provider:

* HmacPBESHA224
* HmacPBESHA256
* HmacPBESHA384
* HmacPBESHA512
* HmacPBESHA512/224
* HmacPBESHA512/256

core-libs/java.io:

JDK-8285660: Enable Windows Alternate Data Streams by default
=============================================================
The Windows implementation of `java.io.File` has been changed so that
strict validity checks are **not** performed by default on file
paths. This includes allowing colons (':') in the path other than only
immediately after a single drive letter. It also allows paths that
represent NTFS Alternate Data Streams (ADS), such as
"filename:streamname". This restores the default behavior of
`java.io.File` to what it was prior to the April 2022 CPU in which
strict validity checks were not performed by default on file paths on
Windows. To re-enable strict path checking in `java.io.File`, the
system property `jdk.io.File.enableADS` should be set to `false` (case
ignored). This might be preferable, for example, if Windows special
device paths such as `NUL:` are *not* used.

New in release OpenJDK 8u332 (2022-04-22):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u332
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u332.txt

* Security fixes
  - JDK-8269938: Enhance XML processing passes redux
  - JDK-8270504, CVE-2022-21426: Better XPath expression handling
  - JDK-8272255: Completely handle MIDI files
  - JDK-8272261: Improve JFR recording file processing
  - JDK-8272594: Better record of recordings
  - JDK-8274221: More definite BER encodings
  - JDK-8275151, CVE-2022-21443: Improved Object Identification
  - JDK-8277227: Better identification of OIDs
  - JDK-8277672, CVE-2022-21434: Better invocation handler handling
  - JDK-8278008, CVE-2022-21476: Improve Santuario processing
  - JDK-8278356: Improve file creation
  - JDK-8278449: Improve keychain support
  - JDK-8278805: Enhance BMP image loading
  - JDK-8278972, CVE-2022-21496: Improve URL supports
  - JDK-8281388: Change wrapping of EncryptedPrivateKeyInfo
* Other changes
  - JDK-8033980: Xerces Update: datatype XMLGregorianCalendarImpl and DurationImpl
  - JDK-8035437: Xerces Update: xml/serialize/DOMSerializerImpl
  - JDK-8035577: Xerces Update: impl/xpath/regex/RangeToken.java
  - JDK-8037259: xerces update: xpointer update
  - JDK-8041523: Xerces Update: Serializer improvements from Xalan
  - JDK-8141508: java.lang.invoke.LambdaConversionException: Invalid receiver type
  - JDK-8162572: Update License Header for all JAXP sources
  - JDK-8167014: jdeps: Missing message: warn.skipped.entry
  - JDK-8198411: [TEST_BUG] Two java2d tests are unstable in mach5
  - JDK-8202822: Add .git to .hgignore
  - JDK-8205540: test/hotspot/jtreg/vmTestbase/nsk/jdb/trace/trace001/trace001.java fails with Debuggee did not exit after 15 <cont> commands
  - JDK-8209178: Proxied HttpsURLConnection doesn't send BODY when retrying POST request
  - JDK-8210283: Support git as an SCM alternative in the build
  - JDK-8218682: [TEST_BUG] DashOffset fails in mach5
  - JDK-8225690: Multiple AttachListener threads can be created
  - JDK-8227738: jvmti/DataDumpRequest/datadumpreq001 failed due to "exit code is 134"
  - JDK-8227815: Minimal VM: set_state is not a member of AttachListener
  - JDK-8240633: Memory leaks in the implementations of FileChooserUI
  - JDK-8241768: git needs .gitattributes
  - JDK-8247766: [aarch64] guarantee(val < (1U << nbits)) failed: Field too big for insn
  - JDK-8253147: The javax/swing/JPopupMenu/7154841/bug7154841.java fail on big screens
  - JDK-8253353: Crash in C2: guarantee(n != NULL) failed: No Node
  - JDK-8266749: AArch64: Backtracing broken on PAC enabled systems
  - JDK-8270290: NTLM authentication fails if HEAD request is used
  - JDK-8273229: Update OS detection code to recognize Windows Server 2022
  - JDK-8273341: Update Siphash to version 1.0
  - JDK-8273575: memory leak in appendBootClassPath(), paths must be deallocated
  - JDK-8274524: SSLSocket.close() hangs if it is called during the ssl handshake
  - JDK-8277224: sun.security.pkcs.PKCS9Attributes.toString() throws NPE
  - JDK-8277488: Add expiry exception for Digicert (geotrustglobalca) expiring in May 2022
  - JDK-8279077: JFR crashes on Linux ppc due to missing crash protector in signal handler
  - JDK-8280060: The sun/rmi/server/Activation.java class use Thread.dumpStack()
  - JDK-8282300: Throws NamingException instead of InvalidNameException after JDK-8278972
  - JDK-8282397: createTempFile method of java.io.File is failing when called with suffix of spaces character
  - JDK-8284548: Invalid XPath expression causes StringIndexOutOfBoundsException
  - JDK-8284920: Incorrect Token type causes XPath expression to return empty result
  - JDK-8284936: Fix Java 7 bootstrap breakage due to use of Arrays.stream
* Shenandoah
  - JDK-8260632: Build failures after JDK-8253353
  - JDK-8282458: Update .jcheck/conf file for sh-jdk8u move to git

New in release OpenJDK 8u322 (2022-01-18):
===========================================
Live versions of these release notes can be found at:
  * https://bit.ly/openjdk8u322
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u322.txt

* Security fixes
  - JDK-8264934, CVE-2022-21248: Enhance cross VM serialization
  - JDK-8268488: More valuable DerValues
  - JDK-8268494: Better inlining of inlined interfaces
  - JDK-8268512: More content for ContentInfo
  - JDK-8268795: Enhance digests of Jar files
  - JDK-8268801: Improve PKCS attribute handling
  - JDK-8268813, CVE-2022-21283: Better String matching
  - JDK-8269151: Better construction of EncryptedPrivateKeyInfo
  - JDK-8269944: Better HTTP transport redux
  - JDK-8270392, CVE-2022-21293: Improve String constructions
  - JDK-8270416, CVE-2022-21294: Enhance construction of Identity maps
  - JDK-8270492, CVE-2022-21282: Better resolution of URIs
  - JDK-8270498, CVE-2022-21296: Improve SAX Parser configuration management
  - JDK-8270646, CVE-2022-21299: Improved scanning of XML entities
  - JDK-8271962: Better TrueType font loading
  - JDK-8271968: Better canonical naming
  - JDK-8271987: Manifest improved manifest entries
  - JDK-8272014, CVE-2022-21305: Better array indexing
  - JDK-8272026, CVE-2022-21340: Verify Jar Verification
  - JDK-8272236, CVE-2022-21341: Improve serial forms for transport
  - JDK-8272272: Enhance jcmd communication
  - JDK-8272462: Enhance image handling
  - JDK-8273290: Enhance sound handling
  - JDK-8273748, CVE-2022-21349: Improve Solaris font rendering
  - JDK-8273756, CVE-2022-21360: Enhance BMP image support
  - JDK-8273838, CVE-2022-21365: Enhanced BMP processing
* Other changes
  - JDK-6801613: Cross-platform pageDialog and printDialog top margin entry broken
  - JDK-8011541: [TEST_BUG] closed/javax/swing/plaf/metal/MetalUtils/bug6190373.java fails NPE since 7u25b03
  - JDK-8025430: [TEST_BUG] javax/swing/JEditorPane/5076514/bug5076514.java failed since jdk8b108
  - JDK-8041928: MouseEvent.getModifiersEx gives wrong result
  - JDK-8042199: The build of J2DBench via makefile is broken after the JDK-8005402
  - JDK-8044365: (dc) MulticastSendReceiveTests.java failing with ENOMEM when joining group (OS X 10.9)
  - JDK-8048021: Remove @version tag in jaxp repo
  - JDK-8049348: compiler/intrinsics/bmi/verifycode tests on lzcnt and tzcnt use incorrect assumption about REXB prefix usage
  - JDK-8060027: Tests java/beans/XMLEncoder/Test4903007.java and java/beans/XMLEncoder/java_awt_GridBagLayout.java
  - JDK-8066588: javax/management/remote/mandatory/connection/RMIConnector_NPETest.java fails to compile
  - JDK-8066652: Default TimeZone is GMT not local if user.timezone is invalid on Mac OS
  - JDK-8069034: gc/g1/TestEagerReclaimHumongousRegionsClearMarkBits.java nightly failure
  - JDK-8077590: windows_i586_6.2-product-c2-runThese8_Xcomp_vm failing after win compiler upgrade
  - JDK-8080287: The image of BufferedImage.TYPE_INT_ARGB and BufferedImage.TYPE_INT_ARGB_PRE is blank
  - JDK-8140329: [TEST_BUG] test FullScreenAfterSplash.java failed because image was not generated
  - JDK-8140472: java/net/ipv6tests/TcpTest.java failed intermittently with java.net.BindException: Address already in use: NET_Bind
  - JDK-8147051: StaxEntityResolverWrapper should create StaxXMLInputSource with a resolver indicator
  - JDK-8148915: Intermittent failures of bug6400879.java
  - JDK-8176837: SunPKCS11 provider needs to check more details on PKCS11 Mechanism
  - JDK-8177393: Result of RescaleOp for 4BYTE_ABGR images may be 25% black
  - JDK-8177536: Avoid Apple Peer-to-Peer interfaces in networking tests
  - JDK-8182036: Load from initializing arraycopy uses wrong memory state
  - JDK-8183369: RFC unconformity of HttpURLConnection with proxy
  - JDK-8183543: Aarch64: C2 compilation often fails with "failed spill-split-recycle sanity check"
  - JDK-8187450: JNI local refs exceeds capacity warning in NetworkInterface::getAll
  - JDK-8187649: ArrayIndexOutOfBoundsException in java.util.JapaneseImperialCalendar
  - JDK-8190482: InnocuousThread creation should not require the caller to possess enableContextClassLoaderOverride
  - JDK-8190793: Httpserver does not detect truncated request body
  - JDK-8196572: Tests ColConvCCMTest.java and MTColConvTest.java fail
  - JDK-8202788: Explicitly reclaim cached thread-local direct buffers at thread exit
  - JDK-8210058: Algorithmic Italic font leans opposite angle in Printing
  - JDK-8220150: macos10.14 Mojave returns anti-aliased glyphs instead of aliased B&W glyphs
  - JDK-8225082: Remove IdenTrust certificate that is expiring in September 2021
  - JDK-8225083: Remove Google certificate that is expiring in December 2021
  - JDK-8226806: [macOS 10.14] Methods of Java Robot should be called from appropriate thread
  - JDK-8231254: (fs) Add test for macOS Catalina changes to protect system software
  - JDK-8231438: [macOS] Dark mode for the desktop is not supported
  - JDK-8232178: MacVolumesTest failed after upgrade to MacOS Catalina
  - JDK-8232226: [macos 10.15] test/jdk/java/awt/color/EqualityTest/EqualityTest.java may fail
  - JDK-8235153: [TESTBUG] [macos 10.15] java/awt/Graphics/DrawImageBG/SystemBgColorTest.java fails
  - JDK-8236897: Fix the copyright header for pkcs11gcm2.h
  - JDK-8237499: JFR: Include stack trace in the ThreadStart event
  - JDK-8239886: Minimal VM build fails after JDK-8237499
  - JDK-8261397: Try Catch Method Failing to Work When Dividing An Integer By 0
  - JDK-8262731: [macOS] Exception from "Printable.print" is swallowed during "PrinterJob.print"
  - JDK-8272342: [TEST_BUG] java/awt/print/PrinterJob/PageDialogMarginTest.java catches all exceptions
  - JDK-8273308: PatternMatchTest.java fails on CI
  - JDK-8273342: Null pointer dereference in classFileParser.cpp:2817
  - JDK-8273826: Correct Manifest file name and NPE checks
  - JDK-8273968: JCK javax_xml tests fail in CI
  - JDK-8274407: (tz) Update Timezone Data to 2021c
  - JDK-8274467: TestZoneInfo310.java fails with tzdata2021b
  - JDK-8274468: TimeZoneTest.java fails with tzdata2021b
  - JDK-8274595: DisableRMIOverHTTPTest failed: connection refused
  - JDK-8274779: HttpURLConnection: HttpClient and HttpsClient incorrectly check request method when set to POST
  - JDK-8275766: (tz) Update Timezone Data to 2021e
  - JDK-8275849: TestZoneInfo310.java fails with tzdata2021e
  - JDK-8276536: Update TimeZoneNames files to follow the changes made by JDK-8275766

Notes on individual issues:
===========================

security-libs/java.security:

JDK-8271434: Removed IdenTrust Root Certificate
===============================================
The following root certificate from IdenTrust has been removed from
the `cacerts` keystore:

Alias Name: identrustdstx3 [jdk]
Distinguished Name: CN=DST Root CA X3, O=Digital Signature Trust Co.

JDK-8272535: Removed Google's GlobalSign Root Certificate
=========================================================
The following root certificate from Google has been removed from the
`cacerts` keystore:

Alias Name: globalsignr2ca [jdk]
Distinguished Name: CN=GlobalSign, O=GlobalSign, OU=GlobalSign Root CA - R2

core-libs/java.time:

JDK-8274857:  Update Timezone Data to 2021c
===========================================
IANA Time Zone Database, on which JDK's Date/Time libraries are based,
has been updated to version 2021c
(https://mm.icann.org/pipermail/tz-announce/2021-October/000067.html). Note
that with this update, some of the time zone rules prior to the year
1970 have been modified according to the changes which were introduced
with 2021b. For more detail, refer to the announcement of 2021b
(https://mm.icann.org/pipermail/tz-announce/2021-September/000066.html)

New in release OpenJDK 8u312 (2021-10-19):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u312
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u312.txt

* Security fixes
  - JDK-8130183, CVE-2021-35588: InnerClasses: VM permits wrong Throw ClassFormatError if InnerClasses attribute's inner_class_info_index is 0
  - JDK-8161016: Strange behavior of URLConnection with proxy
  - JDK-8163326, CVE-2021-35550: Update the default enabled cipher suites preference
  - JDK-8254967, CVE-2021-35565: com.sun.net.HttpsServer spins on TLS session close
  - JDK-8263314: Enhance XML Dsig modes
  - JDK-8265167, CVE-2021-35556: Richer Text Editors
  - JDK-8265574: Improve handling of sheets
  - JDK-8265580, CVE-2021-35559: Enhanced style for RTF kit
  - JDK-8265776: Improve Stream handling for SSL
  - JDK-8266097, CVE-2021-35561: Better hashing support
  - JDK-8266103: Better specified spec values
  - JDK-8266109: More Resilient Classloading
  - JDK-8266115: More Manifest Jar Loading
  - JDK-8266137, CVE-2021-35564: Improve Keystore integrity
  - JDK-8266689, CVE-2021-35567: More Constrained Delegation
  - JDK-8267086: ArrayIndexOutOfBoundsException in java.security.KeyFactory.generatePublic
  - JDK-8267712: Better LDAP reference processing
  - JDK-8267729, CVE-2021-35578: Improve TLS client handshaking
  - JDK-8267735, CVE-2021-35586: Better BMP support
  - JDK-8268193: Improve requests of certificates
  - JDK-8268199: Correct certificate requests
  - JDK-8268506: More Manifest Digests
  - JDK-8269618, CVE-2021-35603: Better session identification
  - JDK-8269624: Enhance method selection support
  - JDK-8270398: Enhance canonicalization
  - JDK-8270404: Better canonicalization
* Other changes
  - JDK-6847157: java.lang.NullPointerException: HDC for component at sun.java2d.loops.Blit.Blit
  - JDK-7146776: deadlock between URLStreamHandler.getHostAddress and file.Handler.openconnection
  - JDK-7188942: Remove support of pbuffers in OGL Java2d pipeline
  - JDK-8004148: NPE in sun.awt.SunToolkit.getWindowDeactivationTime
  - JDK-8022323: [JavaSecurityScanner] review package com.sun.management.* Native methods should be private
  - JDK-8027154: [TESTBUG] Test java/awt/Mouse/GetMousePositionTest/GetMousePositionWithPopup.java fails
  - JDK-8035001: TEST_BUG: the retry logic in RMID.start() should check that the subprocess hasn't terminated
  - JDK-8035424: (reflect) Performance problem in sun.reflect.generics.parser.SignatureParser
  - JDK-8042557: compiler/uncommontrap/TestSpecTrapClassUnloading.java fails with: GC triggered before VM initialization completed
  - JDK-8054118: java/net/ipv6tests/UdpTest.java failed intermittently
  - JDK-8065215: Print warning summary at end of configure
  - JDK-8072767: DefaultCellEditor for comboBox creates ActionEvent with wrong source object
  - JDK-8079891: Store configure log in $BUILD/configure.log
  - JDK-8080082: configure fails if you create an empty directory and then run configure from it
  - JDK-8086003: Test fails on OSX with java.lang.RuntimeException 'Narrow klass base: 0x0000000000000000, Narrow klass shift: 3' missing
  - JDK-8131062: aarch64: add support for GHASH acceleration
  - JDK-8134869: AARCH64: GHASH intrinsic is not optimal
  - JDK-8134989: java/net/MulticastSocket/TestInterfaces.java failed due to unexpected IP address
  - JDK-8156584: Initialization race in sun.security.x509.AlgorithmId.get
  - JDK-8157404: Unable to read certain PKCS12 keystores from SequenceInputStream
  - JDK-8166673: The new implementation of Robot.waitForIdle() may hang
  - JDK-8170467: (reflect) Optimize SignatureParser's use of StringBuilders
  - JDK-8194246: JVM crashes when calling getStackTrace if stack contains a method that is a member of a very large class
  - JDK-8196181: sun/java2d/GdiRendering/InsetClipping.java fails
  - JDK-8202837: PBES2 AlgorithmId encoding error in PKCS12 KeyStore
  - JDK-8206189: sun/security/pkcs12/EmptyPassword.java fails with Sequence tag error
  - JDK-8214418: half-closed SSLEngine status may cause application dead loop
  - JDK-8214513: A PKCS12 keystore from Java 8 using custom PBE parameters cannot be read in Java 11
  - JDK-8220786: Create new switch to redirect error reporting output to stdout or stderr
  - JDK-8222751: closed/test/jdk/sun/security/util/DerIndefLenConverter/IndefBerPkcs12.java fail
  - JDK-8229243: SunPKCS11-Solaris provider tests failing on Solaris 11.4
  - JDK-8231222: fix pkcs11 P11_DEBUG guarded native traces
  - JDK-8237495: Java MIDI fails with a dereferenced memory error when asked to send a raw 0xF7
  - JDK-8238567: SoftMainMixer.processAudioBuffers(): Wrong handling of stoppedMixers
  - JDK-8240518: Incorrect JNU_ReleaseStringPlatformChars in Windows Print
  - JDK-8241248: NullPointerException in sun.security.ssl.HKDF.extract(HKDF.java:93)
  - JDK-8244154: Update SunPKCS11 provider with PKCS11 v3.0 header files
  - JDK-8247469: getSystemCpuLoad() returns -1 on linux when some offline cpus are present and cpusets.effective_cpus is not available
  - JDK-8248901: Signed immediate support in .../share/assembler.hpp is broken.
  - JDK-8259338: Add expiry exception for identrustdstx3 alias to VerifyCACerts.java test
  - JDK-8262000: jdk/jfr/event/gc/detailed/TestPromotionFailedEventWithParallelScavenge.java failed with "OutOfMemoryError: Java heap space"
  - JDK-8262829: Native crash in Win32PrintServiceLookup.getAllPrinterNames()
  - JDK-8263311: Watch registry changes for remote printers update instead of polling
  - JDK-8263382: java/util/logging/ParentLoggersTest.java failed with "checkLoggers: getLoggerNames() returned unexpected loggers"
  - JDK-8264752: SIGFPE crash with option FlightRecorderOptions:threadbuffersize=30M
  - JDK-8265238: [8u] [macos] build failure in OpenJDK8u after JDK-8211301 in older xcode
  - JDK-8265836: OperatingSystemImpl.getCpuLoad() returns incorrect CPU load inside a container
  - JDK-8265978: make test should look for more locations when searching for exit code
  - JDK-8266206: Build failure after JDK-8264752 with older GCCs
  - JDK-8268103: JNI functions incorrectly return a double after JDK-8265836
  - JDK-8268965: TCP Connection Reset when connecting simple socket to SSL server
  - JDK-8269594: assert(_handle_mark_nesting > 1) failed: memory leak: allocating handle outside HandleMark
  - JDK-8269763: The JEditorPane is blank after JDK-8265167
  - JDK-8269810: [8u] Update generated_configure.sh after JDK-8250876 backport
  - JDK-8269851: OperatingSystemMXBean getProcessCpuLoad reports incorrect process cpu usage in containers
  - JDK-8269859: BacktraceBuilder._cprefs needs to be accessed as unsigned short
  - JDK-8269882: stack-use-after-scope in NewObjectA
  - JDK-8269953: config.log is not in build directory after 8u backport of JDK-8079891
  - JDK-8270137: Kerberos Credential Retrieval from Cache not Working in Cross-Realm Setup
  - JDK-8271466: StackGap test fails on aarch64 due to "-m64"
  - JDK-8272124: Cgroup v1 initialization causes NullPointerException when cgroup path contains colon
  - JDK-8272214: [8u] Build failure after backport of JDK-8248901
  - JDK-8272714: [8u] Build failure after backport of JDK-8248901 with MSVC 2013
* Shenandoah
  - [backport] JDK-8269661: JNI_GetStringCritical does not lock char array
  - Re-cast JNI critical strings patch to be Shenandoah-specific

Notes on individual issues:
===========================

core-libs/java.net:

JDK-8164200: Modified HttpURLConnection behavior when no suitable proxy is found
================================================================================
The behavior of HttpURLConnection when using a ProxySelector has been
modified with this JDK release. HttpURLConnection used to fall back to
a DIRECT connection attempt if the configured proxy(s) failed to make
a connection. This release introduces a change whereby no DIRECT
connection will be attempted in such a scenario. Instead, the
HttpURLConnection.connect() method will fail and throw an IOException
which occurred from the last proxy tested.

security-libs/javax.net.ssl:

JDK-8219551: Updated the Default Enabled Cipher Suites Preference
=================================================================
The preference of the default enabled cipher suites has been
changed. The compatibility impact should be minimal. If needed,
applications can customize the enabled cipher suites and the
preference. For more details, refer to the SunJSSE provider
documentation and the JSSE Reference Guide documentation.

New in release OpenJDK 8u302 (2021-07-20):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u302
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u302.txt

* Security fixes
  - JDK-8256157: Improve bytecode assembly
  - JDK-8256491: Better HTTP transport
  - JDK-8258432, CVE-2021-2341: Improve file transfers
  - JDK-8260453: Improve Font Bounding
  - JDK-8260960: Signs of jarsigner signing
  - JDK-8260967, CVE-2021-2369: Better jar file validation
  - JDK-8262380: Enhance XML processing passes
  - JDK-8262403: Enhanced data transfer
  - JDK-8262410: Enhanced rules for zones
  - JDK-8262477: Enhance String Conclusions
  - JDK-8262967: Improve Zip file support
  - JDK-8264066, CVE-2021-2388: Enhance compiler validation
  - JDK-8264079: Improve abstractions
  - JDK-8264460: Improve NTLM support
* Other changes
  - JDK-6878250: (so) IllegalBlockingModeException thrown when reading from a closed SocketChannel's InputStream
  - JDK-6990210: [TEST_BUG] EventDispatchThread/HandleExceptionOnEDT/HandleExceptionOnEDT.java fails on gnome
  - JDK-7059970: Test case: javax/imageio/plugins/png/ITXtTest.java is not closing a file
  - JDK-7106851: Test should not use System.exit
  - JDK-8019470: Changes needed to compile JDK 8 on MacOS with clang compiler
  - JDK-8028618: [TEST BUG] javax/swing/JScrollBar/bug4202954/bug4202954.java fails
  - JDK-8030123: java/beans/Introspector/Test8027648.java fails
  - JDK-8032050: Clean up for java/rmi/activation/Activatable/shutdownGracefully/ShutdownGracefully.java
  - JDK-8033289: clang: clean up unused function warning
  - JDK-8034856: gcc warnings compiling src/solaris/native/sun/security/pkcs11
  - JDK-8034857: gcc warnings compiling src/solaris/native/sun/management
  - JDK-8035000: clean up ActivationLibrary.DestroyThread
  - JDK-8035054: JarFacade.c should not include ctype.h
  - JDK-8035287: gcc warnings compiling various libraries files
  - JDK-8036095: RMI tests using testlibrary.RMID and testlibrary.JavaVM do not pass through vmoptions
  - JDK-8037825: Fix warnings and enable "warnings as errors" in serviceability native libraries
  - JDK-8042891: Format issues embedded in macros for two g1 source files
  - JDK-8043264: hsdis library not picked up correctly on expected paths
  - JDK-8043646: libosxapp.dylib fails to build on Mac OS 10.9 with clang
  - JDK-8047939: [TESTBUG] Rewrite test/runtime/8001071/Test8001071.sh
  - JDK-8055754: filemap.cpp does not compile with clang
  - JDK-8064909: FragmentMetaspace.java got OutOfMemoryError
  - JDK-8066508: JTReg tests timeout on slow devices when run using JPRT
  - JDK-8066807: langtools/test/Makefile should use -agentvm not -samevm
  - JDK-8071374: -XX:+PrintAssembly -XX:+PrintSignatureHandlers crash fastdebug VM with assert(limit == __null || limit <= nm->code_end()) in RelocIterator::initialize
  - JDK-8073446: TimeZone getOffset API does not return a dst offset between years 2038-2137
  - JDK-8074835: Resolve disabled warnings for libj2gss
  - JDK-8074836: Resolve disabled warnings for libosxkrb5
  - JDK-8075071: [TEST_BUG] TimSortStackSize2.java: OOME: Java heap space: MaxHeap shrinked by MaxRAMFraction
  - JDK-8077364: "if( !this )" construct prevents build on Xcode 6.3
  - JDK-8078855: [TEST_BUG] javax/swing/JComboBox/8032878/bug8032878.java fails in WindowsClassicLookAndFeel
  - JDK-8081764: [TEST_BUG] Test javax/swing/plaf/aqua/CustomComboBoxFocusTest.java fails on Windows, Solaris Sparcv9 and Linux but passes on MacOSX
  - JDK-8129511: PlatformMidi.c:83 uses malloc without malloc header
  - JDK-8130308: Too low memory usage in TestPromotionFromSurvivorToTenuredAfterMinorGC.java
  - JDK-8130430: [TEST_BUG] remove unnecessary internal calls from javax/swing/JRadioButton/8075609/bug8075609.java
  - JDK-8132148: G1 hs_err region dump legend out of sync with region values
  - JDK-8132709: [TESTBUG] gc/g1/TestHumongousShrinkHeap.java might fail on embedded
  - JDK-8134672: [TEST_BUG] Some tests should check isDisplayChangeSupported
  - JDK-8134883: C1 hard crash in range check elimination in Nashorn test262parallel
  - JDK-8136592: [TEST_BUG] Fix 2 platform-specific closed regtests for jigsaw
  - JDK-8138820: JDK Hotspot build fails with Xcode 7.0.1
  - JDK-8151786: [TESTBUG] java/beans/XMLEncoder/Test4625418.java timed out intermittently
  - JDK-8159898: Negative array size in java/beans/Introspector/Test8027905.java
  - JDK-8166046: [TESTBUG] compiler/stringopts/TestStringObjectInitialization.java fails with OOME
  - JDK-8166724: gc/g1/TestHumongousShrinkHeap.java fails with OOME
  - JDK-8172188: JDI tests fail due to "permission denied" when creating temp file
  - JDK-8177809: File.lastModified() is losing milliseconds (always ends in 000)
  - JDK-8178403: DirectAudio in JavaSound may hang and leak
  - JDK-8180478: tools/launcher/MultipleJRE.sh fails on Windows because of extra-''
  - JDK-8183910: gc/arguments/TestAggressiveHeap.java fails intermittently
  - JDK-8190332: PngReader throws NegativeArraySizeException/OOM error when IHDR width is very large
  - JDK-8190679: java/util/Arrays/TimSortStackSize2.java fails with "Initial heap size set to a larger value than the maximum heap size"
  - JDK-8191955: AArch64: incorrect prefetch distance causes an internal error
  - JDK-8196092: javax/swing/JComboBox/8032878/bug8032878.java fails
  - JDK-8199265: java/util/Arrays/TimSortStackSize2.java fails with OOM
  - JDK-8200550: Xcode 9.3 produce warning -Wexpansion-to-defined
  - JDK-8202299: Java Keystore fails to load PKCS12/PFX certificates created in WindowsServer2016
  - JDK-8203196: C1 emits incorrect code due to integer overflow in _tableswitch keys
  - JDK-8205014: com/sun/jndi/ldap/DeadSSLLdapTimeoutTest.java failed with "Read timed out"
  - JDK-8206243: java -XshowSettings fails if memory.limit_in_bytes overflows LONG.max
  - JDK-8206925: Support the certificate_authorities extension
  - JDK-8209996: [PPC64] Fix JFR profiling
  - JDK-8214345: infinite recursion while checking super class
  - JDK-8217230: assert(t == t_no_spec) failure in NodeHash::check_no_speculative_types()
  - JDK-8217348: assert(thread->is_Java_thread()) failed: just checking
  - JDK-8225081: Remove Telia Company CA certificate expiring in April 2021
  - JDK-8225116: Test OwnedWindowsLeak.java intermittently fails
  - JDK-8228757: Fail fast if the handshake type is unknown
  - JDK-8230428: Cleanup dead CastIP node code in formssel.cpp
  - JDK-8231631: sun/net/ftp/FtpURLConnectionLeak.java fails intermittently with NPE
  - JDK-8231841: AArch64: debug.cpp help() is missing an AArch64 line for pns
  - JDK-8231949: [PPC64, s390]: Make async profiling more reliable
  - JDK-8234011: (zipfs) Memory leak in ZipFileSystem.releaseDeflater()
  - JDK-8239053: [8u] clean up undefined-var-template warnings
  - JDK-8239400: [8u] clean up undefined-var-template warnings
  - JDK-8241649: Optimize Character.toString
  - JDK-8241829: Cleanup the code for PrinterJob on windows
  - JDK-8242565: Policy initialization issues when the denyAfter constraint is enabled
  - JDK-8243559: Remove root certificates with 1024-bit keys
  - JDK-8247350: [aarch64] assert(false) failed: wrong size of mach node
  - JDK-8249142: java/awt/FontClass/CreateFont/DeleteFont.sh is unstable
  - JDK-8249278: Revert JDK-8226253 which breaks the spec of AccessibleState.SHOWING for JList
  - JDK-8250876: Fix issues with cross-compile on macos
  - JDK-8252883: AccessDeniedException caused by delayed file deletion on Windows
  - JDK-8253375: OSX build fails with Xcode 12.0 (12A7209)
  - JDK-8254631: Better support ALPN byte wire values in SunJSSE
  - JDK-8255086: Update the root locale display names
  - JDK-8255734: VM should ignore SIGXFSZ on ppc64, s390 too
  - JDK-8256818: SSLSocket that is never bound or connected leaks socket resources
  - JDK-8257039: [8u] GenericTaskQueue destructor is incorrect
  - JDK-8257670: sun/security/ssl/SSLSocketImpl/SSLSocketLeak.java reports leaks
  - JDK-8257884: Re-enable sun/security/ssl/SSLSocketImpl/SSLSocketLeak.java as automatic test
  - JDK-8257997: sun/security/ssl/SSLSocketImpl/SSLSocketLeak.java again reports leaks after JDK-8257884
  - JDK-8257999: Parallel GC crash in gc/parallel/TestDynShrinkHeap.java: new region is not in covered_region
  - JDK-8258419: RSA cipher buffer cleanup
  - JDK-8258669: fastdebug jvm crashes when do event based tracing for monitor inflation
  - JDK-8258753: StartTlsResponse.close() hangs due to synchronization issues
  - JDK-8259271: gc/parallel/TestDynShrinkHeap.java still fails "assert(covered_region.contains(new_memregion)) failed: new region is not in covered_region"
  - JDK-8259619: C1: 3-arg StubAssembler::call_RT stack-use condition is incorrect
  - JDK-8259886: Improve SSL session cache performance and scalability
  - JDK-8260029: aarch64: fix typo in verify_oop_array
  - JDK-8260236: better init AnnotationCollector _contended_group
  - JDK-8260255: C1: LoopInvariantCodeMotion constructor can leave some fields uninitialized
  - JDK-8260484: CheckExamples.java / NoJavaLangTest.java fail with jtreg 4.2
  - JDK-8260704: ParallelGC: oldgen expansion needs release-store for _end
  - JDK-8261355: No data buffering in SunPKCS11 Cipher encryption when the underlying mechanism has no padding
  - JDK-8261867: Backport relevant test changes & additions from JDK-8130125
  - JDK-8262110: DST starts from incorrect time in 2038
  - JDK-8262446: DragAndDrop hangs on Windows
  - JDK-8262726: AArch64: C1 StubAssembler::call_RT can corrupt stack
  - JDK-8262730: Enable jdk8u MacOS external debug symbols
  - JDK-8262864: No debug symbols in image for Windows --with-native-debug-symbols=external
  - JDK-8263061: copy wrong unpack200 debuginfo to bin directory after 8252395
  - JDK-8263504: Some OutputMachOpcodes fields are uninitialized
  - JDK-8263600: change rmidRunning to a simple lookup
  - JDK-8264509: jdk8u MacOS zipped debug symbols won't build
  - JDK-8264562: assert(verify_field_bit(1)) failed: Attempting to write an uninitialized event field: type
  - JDK-8264640: CMS ParScanClosure misses a barrier
  - JDK-8264816: Weak handles leak causes GC to take longer
  - JDK-8265462: Handle multiple slots in the NSS Internal Module from SunPKCS11's Secmod
  - JDK-8265666: Enable AIX build platform to make external debug symbols
  - JDK-8265832: runtime/StackGap/testme.sh fails to compile in 8u
  - JDK-8265988: Fix sun/text/IntHashtable/Bug4170614 for JDK 8u
  - JDK-8266191: Missing aarch64 parts of JDK-8181872 (C1: possible overflow when strength reducing integer multiply by constant)
  - JDK-8266723: JFR periodic events are causing extra allocations
  - JDK-8266929: Unable to use algorithms from 3p providers
  - JDK-8267235: [macos_aarch64] InterpreterRuntime::throw_pending_exception messing up LR results in crash
  - JDK-8267426: MonitorVmStartTerminate test timed out on Embedded VM
  - JDK-8267545: [8u] Enable Xcode 12 builds on macOS
  - JDK-8267689: [aarch64] Crash due to bad shift in indirect addressing mode
  - JDK-8268444: keytool -v -list print is incorrect after backport JDK-8141457
  - JDK-8269388: Default build of OpenJDK 8 fails on newer GCCs with warnings as errors on format-overflow
  - JDK-8269468: JDK-8269388 breaks the build on older GCCs
  - JDK-8270533: AArch64: size_fits_all_mem_uses should return false if its output is a CAS
* Shenandoah
  - [backport] JDK-8259580: Shenandoah: uninitialized label in VerifyThreadGCState
  - [backport] JDK-8259954: gc/shenandoah/mxbeans tests fail with -Xcomp
  - [backport] JDK-8261251: Shenandoah: Use object size for full GC humongous
  - [backport] JDK-8261413: Shenandoah: Disable class-unloading in I-U mode
  - [backport] JDK-8265239: Shenandoah: Shenandoah heap region count could be off by 1
  - [backport] JDK-8266802: Shenandoah: Round up region size to page size unconditionally
  - [backport] JDK-8267561: Shenandoah: Reference processing not properly setup for outside of cycle degenerated GC
  - [backport] JDK-8268127: Shenandoah: Heap size may be too small for region to align to large page size
  - [backport] JDK-8268699: Shenandoah: Add test for JDK-8268127
  - Shenandoah: Process weak roots during class unloading cycle

Notes on individual issues:
===========================

security-libs/java.security:

JDK-8256902: Removed Root Certificates with 1024-bit Keys
=========================================================
The following root certificates with weak 1024-bit RSA public keys
have been removed from the `cacerts` keystore:

Alias Name: thawtepremiumserverca [jdk]
Distinguished Name: EMAILADDRESS=<EMAIL>, CN=Thawte Premium Server CA, OU=Certification Services Division, O=Thawte Consulting cc, L=Cape Town, ST=Western Cape, C=ZA

Alias Name: verisignclass2g2ca [jdk]
Distinguished Name: OU=VeriSign Trust Network, OU="(c) 1998 VeriSign, Inc. - For authorized use only", OU=Class 2 Public Primary Certification Authority - G2, O="VeriSign, Inc.", C=US

Alias Name: verisignclass3ca [jdk]
Distinguished Name: OU=Class 3 Public Primary Certification Authority, O="VeriSign, Inc.", C=US

Alias Name: verisignclass3g2ca [jdk]
Distinguished Name: OU=VeriSign Trust Network, OU="(c) 1998 VeriSign, Inc. - For authorized use only", OU=Class 3 Public Primary Certification Authority - G2, O="VeriSign, Inc.", C=US

Alias Name: verisigntsaca [jdk]
Distinguished Name: CN=Thawte Timestamping CA, OU=Thawte Certification, O=Thawte, L=Durbanville, ST=Western Cape, C=ZA

JDK-8261361: Removed Telia Company's Sonera Class2 CA certificate
=================================================================

The following root certificate have been removed from the cacerts truststore:

Alias Name: soneraclass2ca
Distinguished Name: CN=Sonera Class2 CA, O=Sonera, C=FI

security-libs/javax.net.ssl:

JDK-8257548: Improve Encoding of TLS Application-Layer Protocol Negotiation (ALPN) Values
=========================================================================================
Certain TLS ALPN values couldn't be properly read or written by the
SunJSSE provider. This is due to the choice of Strings as the API
interface and the undocumented internal use of the UTF-8 Character Set
which converts characters larger than U+00007F (7-bit ASCII) into
multi-byte arrays that may not be expected by a peer.

ALPN values are now represented using the network byte representation
expected by the peer, which should require no modification for
standard 7-bit ASCII-based character Strings. However, SunJSSE now
encodes/decodes String characters as 8-bit ISO_8859_1/LATIN-1
characters.  This means applications that used characters above
U+000007F that were previously encoded using UTF-8 may need to either
be modified to perform the UTF-8 conversion, or set the Java security
property `jdk.tls.alpnCharset` to "UTF-8" revert the behavior.

See the updated guide at
https://docs.oracle.com/javase/8/docs/technotes/guides/security/jsse/alpn.html
for more information.

JDK-8244460: Support for certificate_authorities Extension
==========================================================
The "certificate_authorities" extension is an optional extension
introduced in TLS 1.3. It is used to indicate the certificate
authorities (CAs) that an endpoint supports and should be used by the
receiving endpoint to guide certificate selection.

With this JDK release, the "certificate_authorities" extension is
supported for TLS 1.3 in both the client and the server sides.  This
extension is always present for client certificate selection, while it
is optional for server certificate selection.

Applications can enable this extension for server certificate
selection by setting the `jdk.tls.client.enableCAExtension` system
property to `true`.  The default value of the property is `false`.

Note that if the client trusts more CAs than the size limit of the
extension (less than 2^16 bytes), the extension is not enabled.  Also,
some server implementations do not allow handshake messages to exceed
2^14 bytes.  Consequently, there may be interoperability issues when
`jdk.tls.client.enableCAExtension` is set to `true` and the client
trusts more CAs than the server implementation limit.

New in release OpenJDK 8u292 (2021-04-20):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u292
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u292.txt

* Security fixes
  - JDK-8227467: Better class method invocations
  - JDK-8244473: Contextualize registration for JNDI
  - JDK-8244543: Enhanced handling of abstract classes
  - JDK-8249906, CVE-2021-2163: Enhance opening JARs
  - JDK-8250568, CVE-2021-2161: Less ambiguous processing
  - JDK-8253799: Make lists of normal filenames
* Other changes
  - JDK-6345095: regression test EmptyClipRenderingTest fails
  - JDK-6896810: TEST_BUG: java/lang/ref/SoftReference/Pin.java fails with OOME during System.out.println
  - JDK-6949753: [TEST BUG]: java/awt/print/PageFormat/PDialogTest.java needs update by removing a infinite loop
  - JDK-7107012: sun.jvm.hotspot.code.CompressedReadStream readDouble() conversion to long mishandled
  - JDK-7112454: TEST_BUG: java/awt/Choice/PopdownGeneratesMouseEvents/PopdownGeneratesMouseEvents.html failed
  - JDK-7131835: [TEST_BUG] Test does not consider that the rounded edges of the window in Mac OS 10.7
  - JDK-7185221: [macosx] Regtest should not throw exception if a suitable display mode found
  - JDK-8031126: java/lang/management/ThreadMXBean/ThreadUserTime.java fails intermittently
  - JDK-8035166: Remove dependency on EC classes from pkcs11 provider
  - JDK-8035186: j2se_jdk/jdk/test/java/lang/invoke/lambda/LogGeneratedClassesTest.java - assertion error
  - JDK-8038723: Openup some PrinterJob tests
  - JDK-8041464: [TEST_BUG] CustomClassLoaderTransferTest does not support OS X
  - JDK-8041561: Inconsistent opacity behaviour between JCheckBox and JRadioButton
  - JDK-8061777: (zipfs) IllegalArgumentException in ZipCoder.toString when using Shitft_JIS
  - JDK-8078024: javac, several incorporation steps are silently failing when an error should be reported
  - JDK-8078450: Implement consistent process for quarantine of tests
  - JDK-8078614: WindowsClassicLookAndFeel MetalComboBoxUI.getbaseLine fails with IllegalArgumentException
  - JDK-8080953: [TEST_BUG]Test java/awt/FontClass/DebugFonts.java fails due to wrongly typed bugid
  - JDK-8081547: Prepare client libs regression tests for running in a concurrent, headless jtreg environment
  - JDK-8129626: G1: set_in_progress() and clear_started() needs a barrier on non-TSO platforms
  - JDK-8141457: keytool default cert fingerprint algorithm should be SHA-256
  - JDK-8145051: Wrong parameter name in synthetic lambda method leads to verifier error
  - JDK-8150204: (fs) Enhance java/nio/file/Files/probeContentType/Basic.java debugging output
  - JDK-8158525: Update a few java/net tests to use the loopback address instead of the host address
  - JDK-8160217: JavaSound should clean up resources better
  - JDK-8167281: IIOMetadataNode bugs in getElementsByTagName and NodeList.item methods
  - JDK-8168996: C2 crash at postaloc.cpp:140 : assert(false) failed: unexpected yanked node
  - JDK-8171410: aarch64: long multiplyExact shifts by 31 instead of 63
  - JDK-8172404: Tools should warn if weak algorithms are used before restricting them
  - JDK-8185934: keytool shows "Signature algorithm: SHA1withECDSA, -1-bit key"
  - JDK-8191915: JCK tests produce incorrect results with C2
  - JDK-8198334: java/awt/FileDialog/8003399/bug8003399.java fails in headless mode
  - JDK-8202343: Disable TLS 1.0 and 1.1
  - JDK-8209333: Socket reset issue for TLS 1.3 socket close
  - JDK-8211301: [macos] support full window content options
  - JDK-8211339: NPE during SSL handshake caused by HostnameChecker
  - JDK-8216987: ciMethodData::load_data() unpacks MDOs with non-atomic copy
  - JDK-8217338: [Containers] Improve systemd slice memory limit support
  - JDK-8219991: New fix of the deadlock in sun.security.ssl.SSLSocketImpl
  - JDK-8221408: Windows 32bit build build errors/warnings in hotspot
  - JDK-8223186: HotSpot compile warnings from GCC 9
  - JDK-8225435: Upgrade IANA Language Subtag Registry to the latest for JDK14
  - JDK-8225805: Java Access Bridge does not close the logger
  - JDK-8226899: Problemlist compiler/rtm tests
  - JDK-8227642: [TESTBUG] Make docker tests podman compatible
  - JDK-8228434: jdk/net/Sockets/Test.java fails after JDK-8227642
  - JDK-8229284: jdk/internal/platform/cgroup/TestCgroupMetrics.java fails for - memory:getMemoryUsage
  - JDK-8230388: Problemlist additional compiler/rtm tests
  - JDK-8233228: Disable weak named curves by default in TLS, CertPath, and Signed JAR
  - JDK-8234727: sun/security/ssl/X509TrustManagerImpl tests support TLSv1.3
  - JDK-8234728: Some security tests should support TLSv1.3
  - JDK-8235263: Revert TLS 1.3 change that wrapped IOExceptions
  - JDK-8235311: Tag mismatch may alert bad_record_mac
  - JDK-8235874: The ordering of Cipher Suites is not maintained provided through jdk.tls.client.cipherSuites and jdk.tls.server.cipherSuites system property.
  - JDK-8236500: Windows ucrt.dll should be looked up in versioned WINSDK subdirectory
  - JDK-8238579: HttpsURLConnection drops the timeout and hangs forever in read
  - JDK-8239091: Reversed arguments in call to strstr in freetype "debug" code.
  - JDK-8240353: AArch64: missing support for -XX:+ExtendedDTraceProbes in C1
  - JDK-8240827: Downport SSLSocketImpl.java from "8221882: Use fiber-friendly java.util.concurrent.locks in JSSE"
  - JDK-8242141: New System Properties to configure the TLS signature schemes
  - JDK-8244621: [macos10.15] Garbled FX printing plus CoreText warnings on Catalina when building with Xcode 11
  - JDK-8248336: AArch64: C2: offset overflow in BoxLockNode::emit
  - JDK-8249183: JVM crash in "AwtFrame::WmSize" method
  - JDK-8249251: [dark_mode ubuntu 20.04] The selected menu is not highlighted in GTKLookAndFeel
  - JDK-8249588: libwindowsaccessbridge issues on 64bit Windows
  - JDK-8250582: Revert Principal Name type to NT-UNKNOWN when requesting TGS Kerberos tickets
  - JDK-8250984: Memory Docker tests fail on some Linux kernels w/o cgroupv1 swap limit capabilities
  - JDK-8251397: NPE on ClassValue.ClassValueMap.cacheArray
  - JDK-8252470: java/awt/dnd/DisposeFrameOnDragCrash/DisposeFrameOnDragTest.java fails on Windows
  - JDK-8253368: TLS connection always receives close_notify exception
  - JDK-8253476: TestUseContainerSupport.java fails on some Linux kernels w/o swap limit capabilities
  - JDK-8253932: SSL debug log prints incorrect caller info
  - JDK-8254854: [cgroups v1] Metric limits not properly detected on some join controller combinations
  - JDK-8255880: UI of Swing components is not redrawn after their internal state changed
  - JDK-8255908: ExceptionInInitializerError due to UncheckedIOException while initializing cgroupv1 subsystem
  - JDK-8255937: Better cleanup for test/jdk/javax/imageio/stream/StreamFlush.java
  - JDK-8256421: Add 2 HARICA roots to cacerts truststore
  - JDK-8256642: [TEST_BUG] jdk/test/javax/sound/midi/MidiSystem/DefaultProperties.java failed
  - JDK-8258079: Eliminate ParNew's use of klass_or_null()
  - JDK-8256682: JDK-8202343 is incomplete
  - JDK-8257746: Regression introduced with JDK-8250984 - memory might be null in some machines
  - JDK-8258241: [8u] Missing doPrivileged() hunks from JDK-8226575
  - JDK-8258247: Couple of issues in fix for JDK-8249906
  - JDK-8258396: SIGILL in jdk.jfr.internal.PlatformRecorder.rotateDisk()
  - JDK-8258430: 8u backport of JDK-8063107 missing test/javax/swing/JRadioButton/8041561/bug8041561.java changes
  - JDK-8258833: Cancel multi-part cipher operations in SunPKCS11 after failures
  - JDK-8258933: G1 needs klass_or_null_acquire
  - JDK-8259048: (tz) Upgrade time-zone data to tzdata2020f
  - JDK-8259312: VerifyCACerts.java fails as soneraclass2ca cert will
  - JDK-8259384: CUP version wrong in THIRD_PARTY_README after JDK-8233548
  - JDK-8259428: AlgorithmId.getEncodedParams() should return copy
  - JDK-8259568: PPC64 builds broken after JDK-8221408 8u backport
  - JDK-8260349: Cannot programmatically retrieve Metaspace max set via JAVA_TOOL_OPTIONS
  - JDK-8260356: (tz) Upgrade time-zone data to tzdata2021a
  - JDK-8260930: AARCH64: Invalid value passed to critical JNI function
  - JDK-8261183: Follow on to Make lists of normal filenames
  - JDK-8261231: Windows IME was disabled after DnD operation
  - JDK-8261766: [8u] hotspot needs to recognise cl.exe 19.16 to build with VS2017
  - JDK-8262073: assert(allocates2(pc)) failed: not in CodeBuffer memory
  - JDK-8262075: sun/security/krb5/auto/UseCacheAndStoreKey.java timed out intermittently
  - JDK-8263008: AARCH64: Add debug info for libsaproc.so
  - JDK-8264171: Missing aarch64 parts of JDK-8236179 (C1 register allocation failure with T_ADDRESS)
* Shenandoah
  - Normalise whitespace in AArch64 sources prior to merge of upstreamed version in 8u292-b01.
  - Revert differences against upstream 8u
  - [backport] 8202976: Add C1 lea patching support for x86
  - [backport] 8221507: Implement JFR Events for Shenandoah
  - [backport] 8224573: Fix windows build after JDK-8221507
  - [backport] 8228369: Shenandoah: Refactor LRB C1 stubs
  - [backport] 8229474: Shenandoah: Cleanup CM::update_roots()
  - [backport] 8229709: x86_32 build and test failures after JDK-8228369 (Shenandoah: Refactor LRB C1 stubs)
  - [backport] 8231087: Shenandoah: Self-fixing load reference barriers for C1/C2
  - [backport] 8232747: Shenandoah: Concurrent GC should deactivate SATB before processing weak roots
  - [backport] 8232992: Shenandoah: Implement self-fixing interpreter LRB
  - [backport] 8233021: Shenandoah: SBSC2::is_shenandoah_lrb_call should match all LRB shapes
  - [backport] 8233165: Shenandoah:SBSA::gen_load_reference_barrier_stub() should use pointer register for address on aarch64
  - [backport] 8233574: Shenandoah: build is broken without jfr
  - [backport] 8237837: Shenandoah: assert(mem == __null) failed: only one safepoint
  - [backport] 8238153: CTW: C2 (Shenandoah) compilation fails with "Unknown node in get_load_addr: CreateEx"
  - [backport] 8238851: Shenandoah: C1: Resolve into registers of correct type
  - [backport] 8240315: Shenandoah: Rename ShLBN::get_barrier_strength()
  - [backport] 8240751: Shenandoah: fold ShenandoahTracer definition
  - [backport] 8241765: Shenandoah: AARCH64 need to save/restore call clobbered registers before calling keepalive barrier
  - [backport] 8244510: Shenandoah: invert SHC2Support::is_in_cset condition
  - [backport] 8244663: Shenandoah: C2 assertion fails in Matcher::collect_null_checks
  - [backport] 8244721: CTW: C2 (Shenandoah) compilation fails with "unexpected infinite loop graph shape"
  - [backport] 8251451: Shenandoah: Remark ObjectSynchronizer roots with I-U
  - [backport] 8252660: Shenandoah: support manageable SoftMaxHeapSize option
  - [backport] 8253224: Shenandoah: ShenandoahStrDedupQueue destructor calls virtual num_queues()
  - [backport] 8253778: ShenandoahSafepoint::is_at_shenandoah_safepoint should not access VMThread state from other threads
  - [backport] 8255457: Shenandoah: cleanup ShenandoahMarkTask
  - [backport] 8255760: Shenandoah: match constants style in ShenandoahMarkTask fallback
  - [backport] 8256806: Shenandoah: optimize shenandoah/jni/TestPinnedGarbage.java test
  - [backport] 8257641: Shenandoah: Query is_at_shenandoah_safepoint() from control thread should return false
  - Fix register allocation for thread register is 32bit LRB
  - Fix Shenandoah bindings in ADLC formssel
  - Shenandoah: Backed out weak roots cleaning during full gc

Notes on individual issues:
===========================

security-libs/java.security:

JDK-8260597: Added 2 HARICA Root CA Certificates
================================================

The following root certificates have been added to the cacerts truststore:

Alias Name: haricarootca2015
Distinguished Name: CN=Hellenic Academic and Research Institutions RootCA 2015, O=Hellenic Academic and Research Institutions Cert. Authority, L=Athens, C=GR

Alias Name: haricaeccrootca2015
Distinguished Name: CN=Hellenic Academic and Research Institutions ECC RootCA 2015, O=Hellenic Academic and Research Institutions Cert. Authority, L=Athens, C=GR

JDK-8236730: Weak Named Curves in TLS, CertPath, and Signed JAR Disabled by Default
===================================================================================
Weak named curves are disabled by default by adding them to the
following `disabledAlgorithms` security properties:

* jdk.tls.disabledAlgorithms
* jdk.certpath.disabledAlgorithms
* jdk.jar.disabledAlgorithms

Red Hat has always disabled many of the curves provided by upstream,
so the only addition in this release is:

* secp256k1

The curves that remain enabled are:

* secp256r1
* secp384r1
* secp521r1
* X25519
* X448

When large numbers of weak named curves need to be disabled, adding
individual named curves to each `disabledAlgorithms` property would be
overwhelming. To relieve this, a new security property,
`jdk.disabled.namedCurves`, is implemented that can list the named
curves common to all of the `disabledAlgorithms` properties. To use
the new property in the `disabledAlgorithms` properties, precede the
full property name with the keyword `include`.  Users can still add
individual named curves to `disabledAlgorithms` properties separate
from this new property.  No other properties can be included in the
`disabledAlgorithms` properties.

To restore the named curves, remove the `include
jdk.disabled.namedCurves` either from specific or from all
`disabledAlgorithms` security properties. To restore one or more
curves, remove the specific named curve(s) from the
`jdk.disabled.namedCurves` property.

JDK-8244286: Tools Warn If Weak Algorithms Are Used
===================================================
The `keytool` and `jarsigner` tools have been updated to warn users
when weak cryptographic algorithms are used in keys, certificates, and
signed JARs before they are disabled. The weak algorithms are set in
the `jdk.security.legacyAlgorithms` security property in the
`java.security` configuration file. In this release, the tools issue
warnings for the SHA-1 hash algorithm and 1024-bit RSA/DSA keys.

security-libs/javax.net.ssl:

JDK-8256490: Disable TLS 1.0 and 1.1
====================================
TLS 1.0 and 1.1 are versions of the TLS protocol that are no longer
considered secure and have been superseded by more secure and modern
versions (TLS 1.2 and 1.3).

These versions have now been disabled by default. If you encounter
issues, you can, at your own risk, re-enable the versions by removing
"TLSv1" and/or "TLSv1.1" from the `jdk.tls.disabledAlgorithms`
security property in the `java.security` configuration file.

JDK-8242147: New System Properties to Configure the TLS Signature Schemes
=========================================================================
Two new system properties have been added to customize the TLS
signature schemes in JDK. `jdk.tls.client.SignatureSchemes` has been
added for the TLS client side, and `jdk.tls.server.SignatureSchemes`
has been added for the server side.

Each system property contains a comma-separated list of supported
signature scheme names specifying the signature schemes that could be
used for the TLS connections.

The names are described in the "Signature Schemes" section of the
*Java Security Standard Algorithm Names Specification*.

tools/javac:

JDK-8177368: Several incorporation steps are silently failing when an error should be reported
==============================================================================================
Reporting previously silent errors found during incorporation, JLS
8§18.3, was supposed to be a clean-up with performance only
implications. But consider the test case:

import java.util.Arrays;
import java.util.List;

class Klass {
  public static <A> List<List<A>> foo(List<? extends A>... lists) {
    return foo(Arrays.asList(lists));
    }

  public static <B> List<List<B>> foo(List<? extends List<? extends B>> lists) {
    return null;
  }
}

This code was not accepted before the patch for [1], but after this
patch the compiler is accepting it. Accepting this code is the right
behavior as not reporting incorporation errors was a bug in the
compiler.  While determining the applicability of method: <B>
List<List<B>> foo(List<? extends List<? extends B>> lists) for which
we have the constraints: b <: Object t <: List<? extends B> t<:Object
List<? extends A> <: t first, inference variable b is selected for
instantiation: b = CAP1 of ? extends A so this implies that: t <:
List<? extends CAP1 of ? extends A> t<: Object List<? extends A> <: t

Now all the bounds are checked for consistency. While checking if
List<? extends A> is a subtype of List<? extends CAP1 of ? extends A>
a bound error is reported. Before the compiler was just swallowing
it. As now the error is reported while inference variable b is being
instantiated, the bound set is rolled back to it's initial state, 'b'
is instantiated to Object, and with this instantiation the constraint
set is solvable, the method is applicable, it's the only applicable
one and the code is accepted as correct. The compiler behavior in this
case is defined at JLS 8 §18.4

This fix has source compatibility impact, right now code that wasn't
being accepted is now being accepted by the javac compiler. Currently
there are no reports of any other kind of incompatibility.

[1] https://bugs.openjdk.java.net/browse/JDK-8078024

New in release OpenJDK 8u282 (2021-01-19):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u282
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u282.txt

* Security fixes
  - JDK-8247619: Improve Direct Buffering of Characters
* Other changes
  - JDK-6962725: Regtest javax/swing/JFileChooser/6738668/bug6738668.java fails under Linux
  - JDK-8008657: JSpinner setComponentOrientation doesn't affect on text orientation
  - JDK-8022535: [TEST BUG] javax/swing/text/html/parser/Test8017492.java fails
  - JDK-8025936: Windows .pdb and .map files does not have proper dependencies setup
  - JDK-8030350: Enable additional compiler warnings for GCC
  - JDK-8031423: Test java/awt/dnd/DisposeFrameOnDragCrash/DisposeFrameOnDragTest.java fails by Timeout on Windows
  - JDK-8036122: Fix warning 'format not a string literal'
  - JDK-8039279: Move awt tests to openjdk repository
  - JDK-8041592: [TEST_BUG] Move 42 AWT hw/lw mixing tests to jdk
  - JDK-8043126: move awt automated functional tests from AWT_Events/Lw and AWT_Events/AWT to OpenJDK repository
  - JDK-8043131: Move ShapedAndTranslucentWindows and GC functional AWT tests to regression tree
  - JDK-8043899: compiler/5091921/Test7005594.java fails if specified -Xmx is less than 1600m
  - JDK-8044157: [TEST_BUG] Improve recently submitted AWT_Mixing tests
  - JDK-8044172: [TEST_BUG] Move regtests for 4523758 and AltPlusNumberKeyCombinationsTest to jdk
  - JDK-8044429: move awt automated tests for AWT_Modality to OpenJDK repository
  - JDK-8044765: Move functional tests AWT_SystemTray/Automated to openjdk repository
  - JDK-8046221: [TEST_BUG] Cleanup datatransfer tests
  - JDK-8047180: Move functional tests AWT_Headless/Automated to OpenJDK repository
  - JDK-8047367: move awt automated tests from AWT_Modality to OpenJDK repository - part 2
  - JDK-8048246: Move AWT_DnD/Clipboard/Automated functional tests to OpenJDK
  - JDK-8049617: move awt automated tests from AWT_Modality to OpenJDK repository - part 3
  - JDK-8049694: Migrate functional AWT_DesktopProperties/Automated tests to OpenJDK
  - JDK-8050885: move awt automated tests from AWT_Modality to OpenJDK repository - part 4
  - JDK-8051440: move tests about maximizing undecorated to OpenJDK
  - JDK-8051853: new URI("x/").resolve("..").getSchemeSpecificPart() returns null!
  - JDK-8052012: move awt automated tests from AWT_Modality to OpenJDK repository - part 5
  - JDK-8052408: Move AWT_BAT functional tests to OpenJDK (3 of 3)
  - JDK-8053657: [TEST_BUG] move some 5 tests related to undecorated Frame/JFrame to JDK
  - JDK-8054143: move awt automated tests from AWT_Modality to OpenJDK repository - part 6
  - JDK-8054358: move awt automated tests from AWT_Modality to OpenJDK repository - part 7
  - JDK-8054359: move awt automated tests from AWT_Modality to OpenJDK repository - part 8
  - JDK-8055360: Move the rest part of AWT ShapedAndTranslucent tests to OpenJDK
  - JDK-8055664: move 14 tests about setLocationRelativeTo to jdk
  - JDK-8055836: move awt tests from AWT_Modality to OpenJDK repository - part 9
  - JDK-8057694: move awt tests from AWT_Modality to OpenJDK repository - part 10
  - JDK-8058805: [TEST_BUG]Test java/awt/TrayIcon/SecurityCheck/NoPermissionTest/NoPermissionTest.java fails
  - JDK-8062808: Turn on the -Wreturn-type warning
  - JDK-8063102: Change open awt regression tests to avoid sun.awt.SunToolkit.realSync, part 1
  - JDK-8063104: Change open awt regression tests to avoid sun.awt.SunToolkit.realSync, part 2
  - JDK-8063106: Change open swing regression tests to avoid sun.awt.SunToolkit.realSync, part 1
  - JDK-8063107: Change open swing regression tests to avoid sun.awt.SunToolkit.realSync, part 2
  - JDK-8064573: [TEST_BUG] javax/swing/text/AbstractDocument/6968363/Test6968363.java is asocial pressing VK_LEFT and not releasing
  - JDK-8064575: [TEST_BUG] javax/swing/JEditorPane/6917744/bug6917744.java 100 times press keys and never releases
  - JDK-8064809: [TEST_BUG] javax/swing/JComboBox/4199622/bug4199622.java contains a lot of keyPress and not a single keyRelease
  - JDK-8067441: Some tests fails with error: cannot find symbol getSystemMnemonicKeyCodes()
  - JDK-8068228: Test closed/java/awt/Mouse/MaximizedFrameTest/MaximizedFrameTest fails with GTKLookAndFeel
  - JDK-8068275: Some tests failed after JDK-8063104
  - JDK-8069211: (zipfs) ZipFileSystem creates corrupted zip if entry output stream gets closed more than once
  - JDK-8074807: Fix some tests unnecessary using internal API
  - JDK-8076315: move 4 manual functional swing tests to regression suite
  - JDK-8130772: Util.hitMnemonics does not work: getSystemMnemonicKeyCodes() returns ALT_MASK rather than VK_ALT
  - JDK-8132664: closed/javax/swing/DataTransfer/DefaultNoDrop/DefaultNoDrop.java locks on Windows
  - JDK-8134632: Mark javax/sound/midi/Devices/InitializationHang.java as headful
  - JDK-8148854: Class names "SomeClass" and "LSomeClass;" treated by JVM as an equivalent
  - JDK-8148916: Mark bug6400879.java as intermittently failing
  - JDK-8148983: Fix extra comma in changes for JDK-8148916
  - JDK-8152545: Use preprocessor instead of compiling a program to generate native nio constants
  - JDK-8156803: Turn StressLCM/StressGCM flags to diagnostic
  - JDK-8160438: javax/swing/plaf/nimbus/8057791/bug8057791.java fails
  - JDK-8160761: [TESTBUG] Several compiler tests fail with product bits
  - JDK-8163161: [PIT][TEST_BUG] increase timeout in javax/swing/plaf/nimbus/8057791/bug8057791.java
  - JDK-8165808: Add release barriers when allocating objects with concurrent collection
  - JDK-8166015: [PIT][TEST_BUG] stray character in java/awt/Focus/ModalDialogActivationTest/ModalDialogActivationTest.java
  - JDK-8166583: Add oopDesc::klass_or_null_acquire()
  - JDK-8166663: Simplify oops_on_card_seq_iterate_careful
  - JDK-8166862: CMS needs klass_or_null_acquire
  - JDK-8168292: [TESTBUG] [macosx] Test java/awt/TrayIcon/DragEventSource/DragEventSource.java fails on OS X
  - JDK-8168682: jdk/test/java/lang/ClassLoader/forNameLeak/ClassForNameLeak.java fails with -Xcomp
  - JDK-8179083: Uninitialized notifier in Java Monitor Wait tracing event
  - JDK-8185003: JMX: Add a version of ThreadMXBean.dumpAllThreads with a maxDepth argument
  - JDK-8197981: Missing return statement in __sync_val_compare_and_swap_8
  - JDK-8202076: test/jdk/java/io/File/WinSpecialFiles.java on windows with VS2017
  - JDK-8205507: jdk/javax/xml/crypto/dsig/GenerationTests.java timed out
  - JDK-8207766: [testbug] Adapt tests for Aix.
  - JDK-8212070: Introduce diagnostic flag to abort VM on failed JIT compilation
  - JDK-8213448: [TESTBUG] enhance jfr/jvm/TestDumpOnCrash
  - JDK-8215727: Restore JFR thread sampler loop to old / previous behavior
  - JDK-8217362: Emergency dump does not work when disk=false is set
  - JDK-8217766: Container Support doesn't work for some Join Controllers combinations
  - JDK-8219013: Update Apache Santuario (XML Signature) to version 2.1.3
  - JDK-8219562: Line of code in osContainer_linux.cpp L102 appears unreachable
  - JDK-8220579: [Containers] SubSystem.java out of sync with osContainer_linux.cpp
  - JDK-8220657: JFR.dump does not work when filename is set
  - JDK-8221340: [TESTBUG] TestCgroupMetrics.java fails after fix for JDK-8219562
  - JDK-8221342: [TESTBUG] Generate Dockerfile for docker testing
  - JDK-8221710: [TESTBUG] more configurable parameters for docker testing
  - JDK-8223108: Test java/awt/EventQueue/NonComponentSourcePost.java is unstable
  - JDK-8224502: [TESTBUG] JDK docker test TestSystemMetrics.java fails with access issues and OOM
  - JDK-8225072: Add LuxTrust certificate that is expiring in March 2021 to list of allowed but expired certs
  - JDK-8227006: [linux] Runtime.availableProcessors execution time increased by factor of 100
  - JDK-8229868: Update Apache Santuario TPRM version
  - JDK-8231209: [REDO] ThreadMXBean::getThreadAllocatedBytes() can be quicker for self thread
  - JDK-8231968: getCurrentThreadAllocatedBytes default implementation s/b getThreadAllocatedBytes
  - JDK-8232114: JVM crashed at imjpapi.dll in native code
  - JDK-8233548: Update CUP to v0.11b
  - JDK-8234270: [REDO] JDK-8204128 NMT might report incorrect numbers for Compiler area
  - JDK-8234339: replace JLI_StrTok in java_md_solinux.c
  - JDK-8238448: RSASSA-PSS signature verification fail when using certain odd key sizes
  - JDK-8239105: Add exception for expiring Digicert root certificates to VerifyCACerts test
  - JDK-8242335: Additional Tests for RSASSA-PSS
  - JDK-8242480: Negative value may be returned by getFreeSwapSpaceSize() in the docker
  - JDK-8244225: stringop-overflow warning on strncpy call from compile_the_world_in
  - JDK-8245400: Upgrade to LittleCMS 2.11
  - JDK-8246648: issue with OperatingSystemImpl getFreeSwapSpaceSize in docker after 8242480
  - JDK-8248214: Add paddings for TaskQueueSuper to reduce false-sharing cache contention
  - JDK-8249176: Update GlobalSignR6CA test certificates
  - JDK-8249846: Change of behavior after JDK-8237117: Better ForkJoinPool behavior
  - JDK-8250636: iso8601_time returns incorrect offset part on MacOS
  - JDK-8250665: Wrong translation for the month name of May in ar_JO,LB,SY
  - JDK-8250928: JFR: Improve hash algorithm for stack traces
  - JDK-8251365: Build failure on AIX after 8250636
  - JDK-8251469: Better cleanup for test/jdk/javax/imageio/SetOutput.java
  - JDK-8251840: Java_sun_awt_X11_XToolkit_getDefaultScreenData should not be in make/mapfiles/libawt_xawt/mapfile-vers
  - JDK-8252384: [TESTBUG] Some tests refer to COMPAT provider rather than JRE
  - JDK-8252395: [8u] --with-native-debug-symbols=external doesn't include debuginfo files for binaries
  - JDK-8252497: Incorrect numeric currency code for ROL
  - JDK-8252754: Hash code calculation of JfrStackTrace is inconsistent
  - JDK-8252904: VM crashes when JFR is used and JFR event class is transformed
  - JDK-8252975: [8u] JDK-8252395 breaks the build for --with-native-debug-symbols=internal
  - JDK-8253036: Support building the Zero assembler port on AArch64
  - JDK-8253284: Zero OrderAccess barrier mappings are incorrect
  - JDK-8253550: [8u] JDK-8252395 breaks the build for make STRIP_POLICY=no_strip
  - JDK-8253752: test/sun/management/jmxremote/bootstrap/RmiBootstrapTest.java fails randomly
  - JDK-8253837: JFR 8u fix symbol and cstring hashtable equals implementaion
  - JDK-8254081: java/security/cert/PolicyNode/GetPolicyQualifiers.java fails due to an expired certificate
  - JDK-8254144: Non-x86 Zero builds fail with return-type warning in os_linux_zero.cpp
  - JDK-8254166: Zero: return-type warning in zeroInterpreter_zero.cpp
  - JDK-8254683: [TEST_BUG] jdk/test/sun/tools/jconsole/WorkerDeadlockTest.java fails
  - JDK-8254982: (tz) Upgrade time-zone data to tzdata2020c
  - JDK-8255003: Build failures on Solaris
  - JDK-8255226: (tz) Upgrade time-zone data to tzdata2020d
  - JDK-8255269: Unsigned overflow in g1Policy.cpp
  - JDK-8255603: Memory/Performance regression after JDK-8210985
  - JDK-8255717: Fix JFR crash in WriteObjectSampleStacktrace due to object not initialized
  - JDK-8256618: Zero: Linux x86_32 build still fails
  - JDK-8256671: Incorrect assignment operator used in guarantee() in genCollectedHeap
  - JDK-8256752: 8252395 incorrect copy rule for macos .dSYM folder
  - JDK-8257397: [TESTBUG] test/lib/containers/docker/Common.java refers to -Xlog:os+container=trace
  - JDK-8258630: Add expiry exception for QuoVadis root certificate
* AArch64 port
  - Fix AArch64 build failure after JDK-8062808 backport
* Shenandoah
  - Fix racy update of code roots

Notes on individual issues:
===========================

security-libs/javax.xml.crypto:

JDK-8230839: Updated XML Signature Implementation to Apache Santuario 2.1.3
===========================================================================
The XML Signature implementation in the `java.xml.crypto` module has
been updated to version 2.1.3 of Apache Santuario. New features
include:

* Added support for embedding elliptic curve public keys in the
  KeyValue element

New in release OpenJDK 8u275 (2020-11-05):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u275
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u275.txt

* Regression fixes
  - JDK-8214440: ldap over a TLS connection negotiate failed with "javax.net.ssl.SSLPeerUnverifiedException: hostname of the server '' does not match the hostname in the server's certificate"
  - JDK-8223940: Private key not supported by chosen signature algorithm
  - JDK-8236512: PKCS11 Connection closed after Cipher.doFinal and NoPadding
  - JDK-8250861: Crash in MinINode::Ideal(PhaseGVN*, bool)

New in release OpenJDK 8u272 (2020-10-20):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u272
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u272.txt

* New features
  - JDK-8245468: Add TLSv1.3 implementation classes from 11.0.7
* Security fixes
  - JDK-8233624: Enhance JNI linkage
  - JDK-8236196: Improve string pooling
  - JDK-8236862, CVE-2020-14779: Enhance support of Proxy class
  - JDK-8237990, CVE-2020-14781: Enhanced LDAP contexts
  - JDK-8237995, CVE-2020-14782: Enhance certificate processing
  - JDK-8240124: Better VM Interning
  - JDK-8241114, CVE-2020-14792: Better range handling
  - JDK-8242680, CVE-2020-14796: Improved URI Support
  - JDK-8242685, CVE-2020-14797: Better Path Validation
  - JDK-8242695, CVE-2020-14798: Enhanced buffer support
  - JDK-8243302: Advanced class supports
  - JDK-8244136, CVE-2020-14803: Improved Buffer supports
  - JDK-8244479: Further constrain certificates
  - JDK-8244955: Additional Fix for JDK-8240124
  - JDK-8245407: Enhance zoning of times
  - JDK-8245412: Better class definitions
  - JDK-8245417: Improve certificate chain handling
  - JDK-8248574: Improve jpeg processing
  - JDK-8249927: Specify limits of jdk.serialProxyInterfaceLimit
  - JDK-8253019: Enhanced JPEG decoding
* Other changes
  - JDK-6574989: TEST_BUG: javax/sound/sampled/Clip/bug5070081.java fails sometimes
  - JDK-8006205: [TESTBUG] NEED_TEST: please JTREGIFY test/compiler/7177917/Test7177917.java
  - JDK-8023697: failed class resolution reports different class name in detail message for the first and subsequent times
  - JDK-8025886: replace [[ and == bash extensions in regtest
  - JDK-8026236: Add PrimeTest for BigInteger
  - JDK-8031625: javadoc problems referencing inner class constructors
  - JDK-8035493: JVMTI PopFrame capability must instruct compilers not to prune locals
  - JDK-8036088: Replace strtok() with its safe equivalent strtok_s() in DefaultProxySelector.c
  - JDK-8039082: [TEST_BUG] Test java/awt/dnd/BadSerializationTest/BadSerializationTest.java fails
  - JDK-8046274: Removing dependency on jakarta-regexp
  - JDK-8048933: -XX:+TraceExceptions output should include the message
  - JDK-8057003: Large reference arrays cause extremely long synchronization times
  - JDK-8060721: Test runtime/SharedArchiveFile/LimitSharedSizes.java fails in jdk 9 fcs new platforms/compiler
  - JDK-8061616: HotspotDiagnosticMXBean.getVMOption() throws IllegalArgumentException for flags of type double
  - JDK-8062947: Fix exception message to correctly represent LDAP connection failure
  - JDK-8064319: Need to enable -XX:+TraceExceptions in release builds
  - JDK-8075774: Small readability and performance improvements for zipfs
  - JDK-8076151: [TESTBUG] Test java/awt/FontClass/CreateFont/fileaccess/FontFile.java fails
  - JDK-8078334: Mark regression tests using randomness
  - JDK-8078880: Mark a few more intermittently failuring security-libs
  - JDK-8080462: Update SunPKCS11 provider with PKCS11 v2.40 support
  - JDK-8132206: move ScanTest.java into OpenJDK
  - JDK-8132376: Add @requires os.family to the client tests with access to internal OS-specific API
  - JDK-8132745: minor cleanup of java/util/Scanner/ScanTest.java
  - JDK-8137087: [TEST_BUG] Cygwin failure of java/awt/appletviewer/IOExceptionIfEncodedURLTest/IOExceptionIfEncodedURLTest.sh
  - JDK-8144539: Update PKCS11 tests to run with security manager
  - JDK-8145808: java/awt/Graphics2D/MTGraphicsAccessTest/MTGraphicsAccessTest.java hangs on Win. 8
  - JDK-8148754: C2 loop unrolling fails due to unexpected graph shape
  - JDK-8148854: Class names "SomeClass" and "LSomeClass;" treated by JVM as an equivalent
  - JDK-8151678: com/sun/jndi/ldap/LdapTimeoutTest.java failed due to timeout on DeadServerNoTimeoutTest is incorrect
  - JDK-8151788: NullPointerException from ntlm.Client.type3
  - JDK-8151834: Test SmallPrimeExponentP.java times out intermittently
  - JDK-8152077: (cal) Calendar.roll does not always roll the hours during daylight savings
  - JDK-8153430: jdk regression test MletParserLocaleTest, ParserInfiniteLoopTest reduce default timeout
  - JDK-8153583: Make OutputAnalyzer.reportDiagnosticSummary public
  - JDK-8154313: Generated javadoc scattered all over the place
  - JDK-8156169: Some sound tests rarely hangs because of incorrect synchronization
  - JDK-8160768: Add capability to custom resolve host/domain names within the default JNDI LDAP provider
  - JDK-8161973: PKIXRevocationChecker.getSoftFailExceptions() not working
  - JDK-8163251: Hard coded loop limit prevents reading of smart card data greater than 8k
  - JDK-8165936: Potential Heap buffer overflow when seaching timezone info files
  - JDK-8165996: PKCS11 using NSS throws an error regarding secmod.db when NSS uses sqlite
  - JDK-8166148: Fix for JDK-8165936 broke solaris builds
  - JDK-8167300: Scheduling failures during gcm should be fatal
  - JDK-8167615: Opensource unit/regression tests for JavaSound
  - JDK-8168517: java/lang/ProcessBuilder/Basic.java failed
  - JDK-8169925: PKCS #11 Cryptographic Token Interface license
  - JDK-8172012: [TEST_BUG] delays needed in javax/swing/JTree/4633594/bug4633594.java
  - JDK-8173300: [TESTBUG]compiler/tiered/NonTieredLevelsTest.java fails with compiler.whitebox.SimpleTestCaseHelper(int) must be compiled
  - JDK-8177334: Update xmldsig implementation to Apache Santuario 2.1.1
  - JDK-8177628: Opensource unit/regression tests for ImageIO
  - JDK-8183341: Better cleanup for javax/imageio/AllowSearch.java
  - JDK-8183349: Better cleanup for jdk/test/javax/imageio/plugins/shared/CanWriteSequence.java and WriteAfterAbort.java
  - JDK-8183351: Better cleanup for jdk/test/javax/imageio/spi/AppletContextTest/BadPluginConfigurationTest.sh
  - JDK-8184762: ZapStackSegments should use optimized memset
  - JDK-8191678: [TESTBUG] Add keyword headful in java/awt FocusTransitionTest test.
  - JDK-8192953: sun/management/jmxremote/bootstrap/*.sh tests fail with error : revokeall.exe: Permission denied
  - JDK-8193137: Nashorn crashes when given an empty script file
  - JDK-8193234: When using -Xcheck:jni an internally allocated buffer can leak
  - JDK-8194298: Add support for per Socket configuration of TCP keepalive
  - JDK-8198004: javax/swing/JFileChooser/6868611/bug6868611.java throws error
  - JDK-8200313: java/awt/Gtk/GtkVersionTest/GtkVersionTest.java fails
  - JDK-8201633: Problems with AES-GCM native acceleration
  - JDK-8203357: Container Metrics
  - JDK-8209113: Use WeakReference for lastFontStrike for created Fonts
  - JDK-8210147: adjust some WSAGetLastError usages in windows network coding
  - JDK-8211049: Second parameter of "initialize" method is not used
  - JDK-8211163: UNIX version of Java_java_io_Console_echo does not return a clean boolean
  - JDK-8211714: Need to update vm_version.cpp to recognise VS2017 minor versions
  - JDK-8214862: assert(proj != __null) at compile.cpp:3251
  - JDK-8216283: Allow shorter method sampling interval than 10 ms
  - JDK-8217606: LdapContext#reconnect always opens a new connection
  - JDK-8217647: JFR: recordings on 32-bit systems unreadable
  - JDK-8217878: ENVELOPING XML signature no longer works in JDK 11
  - JDK-8218629: XML Digital Signature throws NAMESPACE_ERR exception on OpenJDK 11, works 8/9/10
  - JDK-8219566: JFR did not collect call stacks when MaxJavaStackTraceDepth is set to zero
  - JDK-8219919: RuntimeStub name lost with PrintFrameConverterAssembly
  - JDK-8220165: Encryption using GCM results in RuntimeException- input length out of bound
  - JDK-8220313: [TESTBUG] Update base image for Docker testing to OL 7.6
  - JDK-8220555: JFR tool shows potentially misleading message when it cannot access a file
  - JDK-8220674: [TESTBUG] MetricsMemoryTester failcount test in docker container only works with debug JVMs
  - JDK-8221569: JFR tool produces incorrect output when both --categories and --events are specified
  - JDK-8222079: Don't use memset to initialize fields decode_env constructor in disassembler.cpp
  - JDK-8224217: RecordingInfo should use textual representation of path
  - JDK-8225695: 32-bit build failures after JDK-8080462 (Update SunPKCS11 provider with PKCS11 v2.40 support)
  - JDK-8226575: OperatingSystemMXBean should be made container aware
  - JDK-8226697: Several tests which need the @key headful keyword are missing it.
  - JDK-8226809: Circular reference in printed stack trace is not correctly indented & ambiguous
  - JDK-8228835: Memory leak in PKCS11 provider when using AES GCM
  - JDK-8229378: jdwp library loader in linker_md.c quietly truncates on buffer overflow
  - JDK-8230303: JDB hangs when running monitor command
  - JDK-8230711: ConnectionGraph::unique_java_object(Node* N) return NULL if n is not in the CG
  - JDK-8231213: Migrate SimpleDateFormatConstTest to JDK Repo
  - JDK-8231779: crash HeapWord*ParallelScavengeHeap::failed_mem_allocate
  - JDK-8233097: Fontmetrics for large Fonts has zero width
  - JDK-8233621: Mismatch in jsse.enableMFLNExtension property name
  - JDK-8234617: C1: Incorrect result of field load due to missing narrowing conversion
  - JDK-8235243: handle VS2017 15.9 and VS2019 in abstract_vm_version
  - JDK-8235325: build failure on Linux after 8235243
  - JDK-8235687: Contents/MacOS/libjli.dylib cannot be a symlink
  - JDK-8236645: JDK 8u231 introduces a regression with incompatible handling of XML messages
  - JDK-8237951: CTW: C2 compilation fails with "malformed control flow"
  - JDK-8238225: Issues reported after replacing symlink at Contents/MacOS/libjli.dylib with binary
  - JDK-8238380: java.base/unix/native/libjava/childproc.c "multiple definition" link errors with GCC10
  - JDK-8238386: (sctp) jdk.sctp/unix/native/libsctp/SctpNet.c "multiple definition" link errors with GCC10
  - JDK-8238388: libj2gss/NativeFunc.o "multiple definition" link errors with GCC10
  - JDK-8238898: Missing hash characters for header on license file
  - JDK-8239385: KerberosTicket client name refers wrongly to sAMAccountName in AD
  - JDK-8239819: XToolkit: Misread of screen information memory
  - JDK-8240295: hs_err elapsed time in seconds is not accurate enough
  - JDK-8240676: Meet not symmetric failure when running lucene on jdk8
  - JDK-8241888: Mirror jdk.security.allowNonCaAnchor system property with a security one
  - JDK-8242498: Invalid "sun.awt.TimedWindowEvent" object leads to JVM crash
  - JDK-8242556: Cannot load RSASSA-PSS public key with non-null params from byte array
  - JDK-8243138: Enhance BaseLdapServer to support starttls extended request
  - JDK-8243320: Add SSL root certificates to Oracle Root CA program
  - JDK-8243321: Add Entrust root CA - G4 to Oracle Root CA program
  - JDK-8243489: Thread CPU Load event may contain wrong data for CPU time under certain conditions
  - JDK-8244151: Update MUSCLE PC/SC-Lite headers to the latest release 1.8.26
  - JDK-8244818: Java2D Queue Flusher crash while moving application window to external monitor
  - JDK-8245467: Remove 8u TLSv1.2 implementation files
  - JDK-8245469: Remove DTLS protocol implementation
  - JDK-8245470: Fix JDK8 compatibility issues
  - JDK-8245471: Revert JDK-8148188
  - JDK-8245472: Backport JDK-8038893 to JDK8
  - JDK-8245473: OCSP stapling support
  - JDK-8245474: Add TLS_KRB5 cipher suites support according to RFC-2712
  - JDK-8245476: Disable TLSv1.3 protocol in the ClientHello message by default
  - JDK-8245477: Adjust TLS tests location
  - JDK-8245653: Remove 8u TLS tests
  - JDK-8245681: Add TLSv1.3 regression test from 11.0.7
  - JDK-8246193: Possible NPE in ENC-PA-REP search in AS-REQ
  - JDK-8246310: Clean commented-out code about ModuleEntry andPackageEntry in JFR
  - JDK-8246384: Enable JFR by default on supported architectures for October 2020 release
  - JDK-8248643: Remove extra leading space in JDK-8240295 8u backport
  - JDK-8248851: CMS: Missing memory fences between free chunk check and klass read
  - JDK-8249158: THREAD_START and THREAD_END event posted in primordial phase
  - JDK-8249610: Make sun.security.krb5.Config.getBooleanObject(String... keys) method public
  - JDK-8249677: Regression in 8u after JDK-8237117: Better ForkJoinPool behavior
  - JDK-8250546: Expect changed behaviour reported in JDK-8249846
  - JDK-8250627: Use -XX:+/-UseContainerSupport for enabling/disabling Java container metrics
  - JDK-8250755: Better cleanup for jdk/test/javax/imageio/plugins/shared/CanWriteSequence.java
  - JDK-8250875: Incorrect parameter type for update_number in JDK_Version::jdk_update
  - JDK-8251117: Cannot check P11Key size in P11Cipher and P11AEADCipher
  - JDK-8251120: [8u] HotSpot build assumes ENABLE_JFR is set to either true or false
  - JDK-8251341: Minimal Java specification change
  - JDK-8251478: Backport TLSv1.3 regression tests to JDK8u
  - JDK-8251546: 8u backport of JDK-8194298 breaks AIX and Solaris builds
  - JDK-8252084: Minimal VM fails to bootcycle: undefined symbol: AgeTableTracer::is_tenuring_distribution_event_enabled
  - JDK-8252573: 8u: Windows build failed after 8222079 backport
  - JDK-8252886: [TESTBUG] sun/security/ec/TestEC.java : Compilation failed
  - JDK-8254673: Call to JvmtiExport::post_vm_start() was removed by the fix for JDK-8249158
  - JDK-8254937: Revert JDK-8148854 for 8u272

Notes on individual issues:
===========================

core-svc/java.lang.management:

JDK-8236876: OperatingSystemMXBean Methods Inside a Container Return Container Specific Data
============================================================================================
When executing in a container, or other virtualized operating
environment, the following `OperatingSystemMXBean` methods in this
release return container specific information, if
available. Otherwise, they return host specific data:

* getFreePhysicalMemorySize()
* getTotalPhysicalMemorySize()
* getFreeSwapSpaceSize()
* getTotalSwapSpaceSize()
* getSystemCpuLoad()

security-libs/java.security:

JDK-8250756: Added Entrust Root Certification Authority - G4 certificate
========================================================================
The Entrust root certificate has been added to the cacerts truststore:

Alias Name: entrustrootcag4
Distinguished Name: CN=Entrust Root Certification Authority - G4, OU="(c) 2015 Entrust,  Inc. - for authorized use only", OU=See www.entrust.net/legal-terms, O="Entrust, Inc.", C=US

JDK-8250860: Added 3 SSL Corporation Root CA Certificates
=========================================================
The following root certificates have been added to the cacerts truststore for the SSL Corporation:

Alias Name: sslrootrsaca
Distinguished Name: CN=SSL.com Root Certification Authority RSA, O=SSL Corporation, L=Houston, ST=Texas, C=US

Alias Name: sslrootevrsaca
Distinguished Name: CN=SSL.com EV Root Certification Authority RSA R2, O=SSL Corporation, L=Houston, ST=Texas, C=US

Alias Name: sslrooteccca
Distinguished Name: CN=SSL.com Root Certification Authority ECC, O=SSL Corporation, L=Houston, ST=Texas, C=US

security-libs/javax.crypto:pkcs11:

JDK-8221441: SunPKCS11 Provider Upgraded with Support for PKCS#11 v2.40
=======================================================================
The SunPKCS11 provider has been updated with support for PKCS#11
v2.40. This version adds support for more algorithms such as the
AES/GCM/NoPadding cipher, DSA signatures using SHA-2 family of message
digests, and RSASSA-PSS signatures when the corresponding PKCS11
mechanisms are supported by the underlying PKCS11 library.

security-libs/javax.security:

JDK-8242059: Support for canonicalize in krb5.conf
==================================================
The 'canonicalize' flag in the [krb5.conf file][0] is now supported by
the JDK Kerberos implementation. When set to *true*, RFC 6806 [1] name
canonicalization is requested by clients in TGT requests to KDC
services (AS protocol). Otherwise, and by default, it is not
requested.

The new default behavior is different from previous releases where
name canonicalization was always requested by clients in TGT requests
to KDC services (provided that support for RFC 6806[1] was not
explicitly disabled with the *sun.security.krb5.disableReferrals*
system or security properties).

[0]: https://web.mit.edu/kerberos/krb5-devel/doc/admin/conf_files/krb5_conf.html
[1]: https://tools.ietf.org/html/rfc6806

security-libs/javax.xml.crypto:

JDK-8202891: Updated xmldsig Implementation to Apache Santuario 2.1.1
=====================================================================
The XMLDSig provider implementation in the `java.xml.crypto` module has been updated to version 2.1.1 of Apache Santuario.

New features include:

1. Support for the SHA-224 and SHA-3 DigestMethod algorithms specified
in RFC 6931.
2. Support for the HMAC-SHA224, RSA-SHA224, ECDSA-SHA224, and
RSASSA-PSS family of SignatureMethod algorithms specified in RFC 6931.

JDK-8238185: New OpenJDK-specific JDK 8 Updates System Property to fallback to legacy Base64 Encoding format
============================================================================================================
The upgrade to the Apache Santuario libraries (see above) introduced
an issue where XML signature using Base64 encoding resulted in
appending `&#xd` or `&#13` to the encoded output. This behavioural
change was made in the Apache Santuario codebase to comply with RFC
2045. The Santuario team has adopted a position of keeping their
libraries compliant with RFC 2045.

Earlier versions of OpenJDK 8 using the legacy encoder returns encoded
data in a format without `&#xd` or `&#13`.

Therefore a new system property, specific to the 8 update stream,
`com.sun.org.apache.xml.internal.security.lineFeedOnly` is made
available to fall back to the legacy Base64 encoded format.

Users can set this flag in one of two ways:

1. -Dcom.sun.org.apache.xml.internal.security.lineFeedOnly=true

2. System.setProperty("com.sun.org.apache.xml.internal.security.lineFeedOnly", "true")

This new system property is disabled by default. It has no effect on
default behaviour nor when
`com.sun.org.apache.xml.internal.security.ignoreLineBreaks` property
is set.

Later JDK family versions will only support the recommended property:

`com.sun.org.apache.xml.internal.security.ignoreLineBreaks`

JDK-8254177: US/Pacific-New Zone name removed as part of tzdata2020b
====================================================================
Following JDK's update to tzdata2020b, the long-obsolete files
pacificnew and systemv have been removed. As a result, the
"US/Pacific-New" zone name declared in the pacificnew data file is no
longer available for use.

Information regarding the update can be viewed at
https://mm.icann.org/pipermail/tz-announce/2020-October/000059.html

New in release OpenJDK 8u265 (2020-07-27):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/openjdk8u265
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u265.txt

* Bug fixes
  - JDK-8249677: Regression in 8u after JDK-8237117: Better ForkJoinPool behavior
  - JDK-8250546: Expect changed behaviour reported in JDK-8249846

New in release OpenJDK 8u262 (2020-07-14):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/oj8u262
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u262.txt

* New features
  - JDK-8223147: JFR Backport
* Security fixes
  - JDK-8028431, CVE-2020-14579: NullPointerException in DerValue.equals(DerValue)
  - JDK-8028591, CVE-2020-14578: NegativeArraySizeException in sun.security.util.DerInputStream.getUnalignedBitString()
  - JDK-8230613: Better ASCII conversions
  - JDK-8231800: Better listing of arrays
  - JDK-8232014: Expand DTD support
  - JDK-8233255: Better Swing Buttons
  - JDK-8234032: Improve basic calendar services
  - JDK-8234042: Better factory production of certificates
  - JDK-8234418: Better parsing with CertificateFactory
  - JDK-8234836: Improve serialization handling
  - JDK-8236191: Enhance OID processing
  - JDK-8237117, CVE-2020-14556: Better ForkJoinPool behavior
  - JDK-8237592, CVE-2020-14577: Enhance certificate verification
  - JDK-8238002, CVE-2020-14581: Better matrix operations
  - JDK-8238804: Enhance key handling process
  - JDK-8238842: AIOOBE in GIFImageReader.initializeStringTable
  - JDK-8238843: Enhanced font handing
  - JDK-8238920, CVE-2020-14583: Better Buffer support
  - JDK-8238925: Enhance WAV file playback
  - JDK-8240119, CVE-2020-14593: Less Affine Transformations
  - JDK-8240482: Improved WAV file playback
  - JDK-8241379: Update JCEKS support
  - JDK-8241522: Manifest improved jar headers redux
  - JDK-8242136, CVE-2020-14621: Better XML namespace handling
* Other changes
  - JDK-4949105: Access Bridge lacks html tags parsing
  - JDK-7147060: com/sun/org/apache/xml/internal/security/transforms/ClassLoaderTest.java doesn't run in agentvm mode
  - JDK-8003209: JFR events for network utilization
  - JDK-8030680: 292 cleanup from default method code assessment
  - JDK-8035633: TEST_BUG: java/net/NetworkInterface/Equals.java and some tests failed on windows intermittently
  - JDK-8037866: Replace the Fun class in tests with lambdas
  - JDK-8041626: Shutdown tracing event
  - JDK-8041915: Move 8 awt tests to OpenJDK regression tests tree
  - JDK-8067796: (process) Process.waitFor(timeout, unit) doesn't throw NPE if timeout is less than, or equal to zero when unit == null
  - JDK-8076475: Misuses of strncpy/strncat
  - JDK-8130737: AffineTransformOp can't handle child raster with non-zero x-offset
  - JDK-8141056: Erroneous assignment in HeapRegionSet.cpp
  - JDK-8146612: C2: Precedence edges specification violated
  - JDK-8148886: SEGV in sun.java2d.marlin.Renderer._endRendering
  - JDK-8149338: JVM Crash caused by Marlin renderer not handling NaN coordinates
  - JDK-8150986: serviceability/sa/jmap-hprof/JMapHProfLargeHeapTest.java failing because expects HPROF JAVA PROFILE 1.0.1 file format
  - JDK-8151582: (ch) test java/nio/channels/AsyncCloseAndInterrupt.java failing due to "Connection succeeded"
  - JDK-8165675: Trace event for thread park has incorrect unit for timeout
  - JDK-8171934: ObjectSizeCalculator.getEffectiveMemoryLayoutSpecification() does not recognize OpenJDK's HotSpot VM
  - JDK-8172559: [PIT][TEST_BUG] Move @test to be 1st annotation in java/awt/image/Raster/TestChildRasterOp.java
  - JDK-8176182: 4 security tests are not run
  - JDK-8178374: Problematic ByteBuffer handling in CipherSpi.bufferCrypt method
  - JDK-8178910: Problemlist sample tests
  - JDK-8181841: A TSA server returns timestamp with precision higher than milliseconds
  - JDK-8183925: Decouple crash protection from watcher thread
  - JDK-8191393: Random crashes during cfree+0x1c
  - JDK-8195817: JFR.stop should require name of recording
  - JDK-8195818: JFR.start should increase autogenerated name by one
  - JDK-8195819: Remove recording=x from jcmd JFR.check output
  - JDK-8196969: JTreg Failure: serviceability/sa/ClhsdbJstack.java causes NPE
  - JDK-8199712: Flight Recorder
  - JDK-8202578: Revisit location for class unload events
  - JDK-8202835: jfr/event/os/TestSystemProcess.java fails on missing events
  - JDK-8203287: Zero fails to build after JDK-8199712 (Flight Recorder)
  - JDK-8203346: JFR: Inconsistent signature of jfr_add_string_constant
  - JDK-8203664: JFR start failure after AppCDS archive created with JFR StartFlightRecording
  - JDK-8203921: JFR thread sampling is missing fixes from JDK-8194552
  - JDK-8203929: Limit amount of data for JFR.dump
  - JDK-8205516: JFR tool
  - JDK-8207392: [PPC64] Implement JFR profiling
  - JDK-8207829: FlightRecorderMXBeanImpl is leaking the first classloader which calls it
  - JDK-8209960: -Xlog:jfr* doesn't work with the JFR
  - JDK-8210024: JFR calls virtual is_Java_thread from ~Thread()
  - JDK-8210776: Upgrade X Window System 6.8.2 to the latest XWD 1.0.7
  - JDK-8211239: Build fails without JFR: empty JFR events signatures mismatch
  - JDK-8212232: Wrong metadata for the configuration of the cutoff for old object sample events
  - JDK-8213015: Inconsistent settings between JFR.configure and -XX:FlightRecorderOptions
  - JDK-8213421: Line number information for execution samples always 0
  - JDK-8213617: JFR should record the PID of the recorded process
  - JDK-8213734: SAXParser.parse(File, ..) does not close resources when Exception occurs.
  - JDK-8213914: [TESTBUG] Several JFR VM events are not covered by tests
  - JDK-8213917: [TESTBUG] Shutdown JFR event is not covered by test
  - JDK-8213966: The ZGC JFR events should be marked as experimental
  - JDK-8214542: JFR: Old Object Sample event slow on a deep heap in debug builds
  - JDK-8214750: Unnecessary <p> tags in jfr classes
  - JDK-8214896: JFR Tool left files behind
  - JDK-8214906: [TESTBUG] jfr/event/sampling/TestNative.java fails with UnsatisfiedLinkError
  - JDK-8214925: JFR tool fails to execute
  - JDK-8215175: Inconsistencies in JFR event metadata
  - JDK-8215237: jdk.jfr.Recording javadoc does not compile
  - JDK-8215284: Reduce noise induced by periodic task getFileSize()
  - JDK-8215355: Object monitor deadlock with no threads holding the monitor (using jemalloc 5.1)
  - JDK-8215362: JFR GTest JfrTestNetworkUtilization fails
  - JDK-8215771: The jfr tool should pretty print reference chains
  - JDK-8216064: -XX:StartFlightRecording:settings= doesn't work properly
  - JDK-8216486: Possibility of integer overflow in JfrThreadSampler::run()
  - JDK-8216528: test/jdk/java/rmi/transport/runtimeThreadInheritanceLeak/RuntimeThreadInheritanceLeak.java failing with Xcomp
  - JDK-8216559: [JFR] Native libraries not correctly parsed from /proc/self/maps
  - JDK-8216578: Remove unused/obsolete method in JFR code
  - JDK-8216995: Clean up JFR command line processing
  - JDK-8217744: [TESTBUG] JFR TestShutdownEvent fails on some systems due to process surviving SIGINT
  - JDK-8217748: [TESTBUG] Exclude TestSig test case from JFR TestShutdownEvent
  - JDK-8218935: Make jfr strncpy uses GCC 8.x friendly
  - JDK-8220293: Deadlock in JFR string pool
  - JDK-8223689: Add JFR Thread Sampling Support
  - JDK-8223690: Add JFR BiasedLock Event Support
  - JDK-8223691: Add JFR G1 Region Type Change Event Support
  - JDK-8223692: Add JFR G1 Heap Summary Event Support
  - JDK-8224172: assert(jfr_is_event_enabled(id)) failed: invariant
  - JDK-8224475: JTextPane does not show images in HTML rendering
  - JDK-8225068: Remove DocuSign root certificate that is expiring in May 2020
  - JDK-8225069: Remove Comodo root certificate that is expiring in May 2020
  - JDK-8226253: JAWS reports wrong number of radio buttons when buttons are hidden.
  - JDK-8226779: [TESTBUG] Test JFR API from Java agent
  - JDK-8226892: ActionListeners on JRadioButtons don't get notified when selection is changed with arrow keys
  - JDK-8227011: Starting a JFR recording in response to JVMTI VMInit and / or Java agent premain corrupts memory
  - JDK-8227269: Slow class loading when running with JDWP
  - JDK-8227605: Kitchensink fails "assert((((klass)->trace_id() & (JfrTraceIdEpoch::leakp_in_use_this_epoch_bit())) != 0)) failed: invariant"
  - JDK-8229366: JFR backport allows unchecked writing to memory
  - JDK-8229401: Fix JFR code cache test failures
  - JDK-8229708: JFR backport code does not initialize
  - JDK-8229873: 8229401 broke jdk8u-jfr-incubator
  - JDK-8229888: (zipfs) Updating an existing zip file does not preserve original permissions
  - JDK-8229899: Make java.io.File.isInvalid() less racy
  - JDK-8230448: [test] JFRSecurityTestSuite.java is failing on Windows
  - JDK-8230597: Update GIFlib library to the 5.2.1
  - JDK-8230707: JFR related tests are failing
  - JDK-8230769: BufImg_SetupICM add ReleasePrimitiveArrayCritical call in early return
  - JDK-8230782: Robot.createScreenCapture() fails if ?awt.robot.gtk? is set to false
  - JDK-8230856: Java_java_net_NetworkInterface_getByName0 on unix misses ReleaseStringUTFChars in early return
  - JDK-8230926: [macosx] Two apostrophes are entered instead of one with "U.S. International - PC" layout
  - JDK-8230947: TestLookForUntestedEvents.java is failing after JDK-8230707
  - JDK-8231995: two jtreg tests failed after 8229366 is fixed
  - JDK-8233197: Invert JvmtiExport::post_vm_initialized() and Jfr:on_vm_start() start-up order for correct option parsing
  - JDK-8233623: Add classpath exception to copyright in EventHandlerProxyCreator.java file
  - JDK-8233880: Support compilers with multi-digit major version numbers
  - JDK-8236002: CSR for JFR backport suggests not leaving out the package-info
  - JDK-8236008: Some backup files were accidentally left in the hotspot tree
  - JDK-8236074: Missed package-info
  - JDK-8236174: Should update javadoc since tags
  - JDK-8236996: Incorrect Roboto font rendering on Windows with subpixel antialiasing
  - JDK-8238076: Fix OpenJDK 7 Bootstrap Broken by JFR Backport
  - JDK-8238452: Keytool generates wrong expiration date if validity is set to 2050/01/01
  - JDK-8238555: Allow Initialization of SunPKCS11 with NSS when there are external FIPS modules in the NSSDB
  - JDK-8238589: Necessary code cleanup in JFR for JDK8u
  - JDK-8238590: Enable JFR by default during compilation in 8u
  - JDK-8239055: Wrong implementation of VMState.hasListener
  - JDK-8239476: JDK-8238589 broke windows build by moving OrderedPair
  - JDK-8239479: minimal1 and zero builds are failing
  - JDK-8239852: java/util/concurrent tests fail with -XX:+VerifyGraphEdges: assert(!VerifyGraphEdges) failed: verification should have failed
  - JDK-8239867: correct over use of INCLUDE_JFR macro
  - JDK-8240375: Disable JFR by default for July 2020 release
  - JDK-8240576: JVM crashes after transformation in C2 IdealLoopTree::merge_many_backedges
  - JDK-8241444: Metaspace::_class_vsm not initialized if compressed class pointers are disabled
  - JDK-8241638: launcher time metrics always report 1 on Linux when _JAVA_LAUNCHER_DEBUG set
  - JDK-8241750: x86_32 build failure after JDK-8227269
  - JDK-8241902: AIX Build broken after integration of JDK-8223147 (JFR Backport)
  - JDK-8242788: Non-PCH build is broken after JDK-8191393
  - JDK-8242883: Incomplete backport of JDK-8078268: backport test part
  - JDK-8243059: Build fails when --with-vendor-name contains a comma
  - JDK-8243474: [TESTBUG] removed three tests of 0 bytes
  - JDK-8243539: Copyright info (Year) should be updated for fix of 8241638
  - JDK-8243541: (tz) Upgrade time-zone data to tzdata2020a
  - JDK-8244407: JVM crashes after transformation in C2 IdealLoopTree::split_fall_in
  - JDK-8244461: [JDK 8u] Build fails with glibc 2.32
  - JDK-8244548: JDK 8u: sun.misc.Version.jdkUpdateVersion() returns wrong result
  - JDK-8244777: ClassLoaderStats VM Op uses constant hash value
  - JDK-8244843: JapanEraNameCompatTest fails
  - JDK-8245167: Top package in method profiling shows null in JMC
  - JDK-8246223: Windows build fails after JDK-8227269
  - JDK-8246703: [TESTBUG] Add test for JDK-8233197
  - JDK-8248399: Build installs jfr binary when JFR is disabled
  - JDK-8248715: New JavaTimeSupplementary localisation for 'in' installed in wrong package

Notes on individual issues:
===========================

hotspot/jfr:

JDK-8240687: JDK Flight Recorder Integrated to OpenJDK 8u
=========================================================

OpenJDK 8u now contains the backport of JEP 328: Flight Recorder
(https://openjdk.java.net/jeps/328) from later versions of OpenJDK.

JFR is a low-overhead framework to collect and provide data helpful to
troubleshoot the performance of the OpenJDK runtime and of Java
applications. It consists of a new API to define custom events under
the jdk.jfr namespace and a JMX interface to interact with the
framework. The recording can also be initiated with the application
startup using the -XX:+FlightRecorder flag or via jcmd. JFR replaces
the +XX:EnableTracing feature introduced in JEP 167, providing a more
efficient way to retrieve the same information. For compatibility
reasons, +XX:EnableTracing is still accepted, however no data will be
printed.

While JFR is not built by default upstream, it is included in Red Hat
binaries for supported architectures (x86_64, AArch64 & PowerPC 64)

hotspot/runtime:

JDK-8205622: JFR Start Failure After AppCDS Archive Created with JFR StartFlightRecording
=========================================================================================

JFR will be disabled with a warning message if it is enabled during
CDS dumping. The user will see the following warning message:

OpenJDK 64-Bit Server VM warning: JFR will be disabled during CDS dumping

if JFR is enabled during CDS dumping such as in the following command
line:

$ java -Xshare:dump -XX:StartFlightRecording=dumponexit=true

security-libs/java.security:

JDK-8244167: Removal of Comodo Root CA Certificate
==================================================

The following expired Comodo root CA certificate was removed from the
`cacerts` keystore: + alias name "addtrustclass1ca [jdk]"

Distinguished Name: CN=AddTrust Class 1 CA Root, OU=AddTrust TTP Network, O=AddTrust AB, C=SE

JDK-8244166: Removal of DocuSign Root CA Certificate
====================================================

The following expired DocuSign root CA certificate was removed from
 the `cacerts` keystore: + alias name "keynectisrootca [jdk]"

Distinguished Name: CN=KEYNECTIS ROOT CA, OU=ROOT, O=KEYNECTIS, C=FR

security-libs/javax.crypto:pkcs11:

JDK-8240191: Allow SunPKCS11 initialization with NSS when external FIPS modules are present in the Security Modules Database
============================================================================================================================

The SunPKCS11 security provider can now be initialized with NSS when
FIPS-enabled external modules are configured in the Security Modules
Database (NSSDB). Prior to this change, the SunPKCS11 provider would
throw a RuntimeException with the message: "FIPS flag set for
non-internal module" when such a library was configured for NSS in
non-FIPS mode.

This change allows the JDK to work properly with recent NSS releases
on GNU/Linux operating systems when the system-wide FIPS policy is
turned on.

Further information can be found in JDK-8238555.

New in release OpenJDK 8u252 (2020-04-14):
===========================================
Live versions of these release notes can be found at:
  * https://bitly.com/oj8u252
  * https://builds.shipilev.net/backports-monitor/release-notes-openjdk8u252.txt

* Security fixes
  - JDK-8223898, CVE-2020-2754: Forward references to Nashorn
  - JDK-8223904, CVE-2020-2755: Improve Nashorn matching
  - JDK-8224541, CVE-2020-2756: Better mapping of serial ENUMs
  - JDK-8224549, CVE-2020-2757: Less Blocking Array Queues
  - JDK-8225603: Enhancement for big integers
  - JDK-8227542: Manifest improved jar headers
  - JDK-8231415, CVE-2020-2773: Better signatures in XML
  - JDK-8233250: Better X11 rendering
  - JDK-8233410: Better Build Scripting
  - JDK-8234027: Better JCEKS key support
  - JDK-8234408, CVE-2020-2781: Improve TLS session handling
  - JDK-8234825, CVE-2020-2800: Better Headings for HTTP Servers
  - JDK-8234841, CVE-2020-2803: Enhance buffering of byte buffers
  - JDK-8235274, CVE-2020-2805: Enhance typing of methods
  - JDK-8236201, CVE-2020-2830: Better Scanner conversions
  - JDK-8238960: linux-i586 builds are inconsistent as the newly build jdk is not able to reserve enough space for object heap
* Other changes
  - JDK-8005819: Support cross-realm MSSFU
  - JDK-8022263: use same Clang warnings on BSD as on Linux
  - JDK-8038631: Create wrapper for awt.Robot with additional functionality
  - JDK-8047212: runtime/ParallelClassLoading/bootstrap/random/inner-complex assert(ObjectSynchronizer::verify_objmon_isinpool(inf)) failed: monitor is invalid
  - JDK-8055283: Expand ResourceHashtable with C_HEAP allocation, removal and some unit tests
  - JDK-8068184: Fix for JDK-8032832 caused a deadlock
  - JDK-8079693: Add support for ECDSA P-384 and P-521 curves to XML Signature
  - JDK-8132130: some docs cleanup
  - JDK-8135318: CMS wrong max_eden_size for check_gc_overhead_limit
  - JDK-8144445: Maximum size checking in Marlin ArrayCache utility methods is not optimal
  - JDK-8144446: Automate the Marlin crash test
  - JDK-8144526: Remove Marlin logging use of deleted internal API
  - JDK-8144630: Use PrivilegedAction to create Thread in Marlin RendererStats
  - JDK-8144654: Improve Marlin logging
  - JDK-8144718: Pisces / Marlin Strokers may generate invalid curves with huge coordinates and round joins
  - JDK-8166976: TestCipherPBECons has wrong @run line
  - JDK-8167409: Invalid value passed to critical JNI function
  - JDK-8181872: C1: possible overflow when strength reducing integer multiply by constant
  - JDK-8187078: -XX:+VerifyOops finds numerous problems when running JPRT
  - JDK-8191227: issues with unsafe handle resolution
  - JDK-8197441: Signature#initSign/initVerify for an invalid private/public key fails with ClassCastException for SunPKCS11 provider
  - JDK-8204152: SignedObject throws NullPointerException for null keys with an initialized Signature object
  - JDK-8215756: Memory leaks in the AWT on macOS
  - JDK-8216472: (se) Stack overflow during selection operation leads to crash (win)
  - JDK-8219244: NMT: Change ThreadSafepointState's allocation type from mtInternal to mtThread
  - JDK-8219597: (bf) Heap buffer state changes could provoke unexpected exceptions
  - JDK-8225128: Add exception for expiring DocuSign root to VerifyCACerts test
  - JDK-8225130: Add exception for expiring Comodo roots to VerifyCACerts test
  - JDK-8229022: BufferedReader performance can be improved by using StringBuilder
  - JDK-8229345: Memory leak due to vtable stubs not being shared on SPARC
  - JDK-8229872: (fs) Increase buffer size used with getmntent
  - JDK-8230235: Rendering HTML with empty img attribute and documentBaseKey cause Exception
  - JDK-8231430: C2: Memory stomp in max_array_length() for T_ILLEGAL type
  - JDK-8235744: PIT: test/jdk/javax/swing/text/html/TestJLabelWithHTMLText.java times out in linux-x64
  - JDK-8235904: Infinite loop when rendering huge lines
  - JDK-8236179: C1 register allocation error with T_ADDRESS
  - JDK-8237368: Problem with NullPointerException in RMI TCPEndpoint.read
  - JDK-8240521: Revert backport of 8231584: Deadlock with ClassLoader.findLibrary and System.loadLibrary call
  - JDK-8241296: Segfault in JNIHandleBlock::oops_do()
  - JDK-8241307: Marlin renderer should not be the default in 8u252

Notes on individual issues:
===========================

hotspot/svc:

JDK-8174881: Binary format for HPROF updated 
============================================

When dumping the heap in binary format, HPROF format 1.0.2 is always
used now. Previously, format 1.0.1 was used for heaps smaller than
2GB. HPROF format 1.0.2 is also used by jhsdb jmap for the
serviceability agent.

security-libs/java.security:

JDK-8229518: Added Support for PKCS#1 v2.2 Algorithms Including RSASSA-PSS Signature
====================================================================================

The SunRsaSign and SunJCE providers have been enhanced with support
for more algorithms defined in PKCS#1 v2.2, such as RSASSA-PSS
signature and OAEP using FIPS 180-4 digest algorithms. New
constructors and methods have been added to relevant JCA/JCE classes
under the `java.security.spec` and `javax.crypto.spec` packages for
supporting additional RSASSA-PSS parameters.

security-libs/javax.crypto:

JDK-8205471: RSASSA-PSS Signature Support Added to SunMSCAPI
============================================================

The RSASSA-PSS signature algorithm support has been added to the SunMSCAPI provider.

security-libs/javax.security:

JDK-8227564: Allow SASL Mechanisms to Be Restricted
===================================================

A security property named `jdk.sasl.disabledMechanisms` has been added
that can be used to disable SASL mechanisms. Any disabled mechanism
will be ignored if it is specified in the `mechanisms` argument of
`Sasl.createSaslClient` or the `mechanism` argument of
`Sasl.createSaslServer`. The default value for this security property
is empty, which means that no mechanisms are disabled out-of-the-box.

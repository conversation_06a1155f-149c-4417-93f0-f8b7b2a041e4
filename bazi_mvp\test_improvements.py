#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.calendar_converter import CalendarConverter
from modules.user_input import UserInputModule
from modules.output_generator import OutputGenerator

def test_calendar_converter():
    """测试阳历阴历转换功能"""
    print("=" * 50)
    print("测试阳历阴历转换功能")
    print("=" * 50)
    
    cc = CalendarConverter()
    
    # 测试几个日期
    test_dates = [
        (1990, 5, 15),
        (2000, 1, 1),
        (1995, 8, 20),
        (2023, 12, 25)
    ]
    
    for year, month, day in test_dates:
        result = cc.solar_to_lunar(year, month, day)
        print(f"\n阳历: {year}年{month}月{day}日")
        if result:
            print(f"阴历: {result['lunar_date_full']}")
            print(f"精度: {result['conversion_accuracy']}")
        else:
            print("转换失败")
    
    print("\n✅ 阳历阴历转换测试完成")

def test_time_parsing():
    """测试时间解析功能"""
    print("\n" + "=" * 50)
    print("测试时间解析功能")
    print("=" * 50)
    
    ui = UserInputModule()
    
    # 测试时间输入
    test_times = [
        "14:30",
        "下午2点30分",
        "晚上8点",
        "凌晨3点",
        "上午",
        "下午2-4点"
    ]
    
    for time_input in test_times:
        result = ui._parse_time_input(time_input)
        print(f"\n输入: {time_input}")
        if result:
            print(f"解析结果: {result.get('time_description', '未知')}")
            if result.get('hour') is not None:
                print(f"24小时制: {result['hour']}:{result['minute']:02d}")
            print(f"精度: {result.get('time_certainty', '未知')}")
        else:
            print("解析失败")
    
    print("\n✅ 时间解析测试完成")

def test_summary_generation():
    """测试总结生成功能"""
    print("\n" + "=" * 50)
    print("测试总结生成功能")
    print("=" * 50)
    
    og = OutputGenerator()
    
    # 模拟数据
    mock_basic_info = {
        'name': '测试用户',
        'gender': '男',
        'birth_time': '1990年5月15日 14:30'
    }
    
    mock_bazi_info = {
        'year_pillar': '庚午',
        'month_pillar': '辛巳',
        'day_pillar': '甲子',
        'hour_pillar': '辛未'
    }
    
    mock_wuxing_analysis = {
        'day_master_element': '甲木',
        'strength': '偏弱',
        'useful_god': '水木'
    }
    
    mock_analysis_content = {
        'personality': '性格温和，具有很强的责任心和同情心，善于与人沟通，具有领导才能。',
        'career': '适合从事教育、咨询、管理等工作，事业发展稳步上升。',
        'relationship': '感情运势良好，容易遇到合适的伴侣，婚姻生活和谐。',
        'health': '身体健康状况良好，注意肝胆和神经系统的保养。'
    }
    
    summary = og._generate_summary(mock_basic_info, mock_bazi_info, 
                                  mock_wuxing_analysis, mock_analysis_content)
    
    print("生成的总结:")
    print(f"命理概况: {summary['basic_profile']['core_description']}")
    print(f"性格总结: {summary['personality_summary']}")
    print(f"运势概况: {summary['fortune_overview']}")
    print(f"整体评分: {summary['overall_rating']['scores']['overall']}分")
    
    print("\n✅ 总结生成测试完成")

def main():
    """主测试函数"""
    print("🔮 生辰八字算命系统 - 改进功能测试")
    print("=" * 60)
    
    try:
        # 测试阳历阴历转换
        test_calendar_converter()
        
        # 测试时间解析
        test_time_parsing()
        
        # 测试总结生成
        test_summary_generation()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！系统改进功能正常工作")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

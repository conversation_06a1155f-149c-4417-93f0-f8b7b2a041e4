#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字计算模块
核心的八字排盘和分析计算
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional

class BaziCalculator:
    def __init__(self):
        """初始化八字计算器"""
        self.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        self.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 五行属性
        self.wuxing_map = {
            '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
            '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水',
            '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土',
            '巳': '火', '午': '火', '未': '土', '申': '金', '酉': '金',
            '戌': '土', '亥': '水'
        }
        
        # 阴阳属性
        self.yinyang_map = {
            '甲': '阳', '乙': '阴', '丙': '阳', '丁': '阴', '戊': '阳',
            '己': '阴', '庚': '阳', '辛': '阴', '壬': '阳', '癸': '阴',
            '子': '阳', '丑': '阴', '寅': '阳', '卯': '阴', '辰': '阳',
            '巳': '阴', '午': '阳', '未': '阴', '申': '阳', '酉': '阴',
            '戌': '阳', '亥': '阴'
        }
        
        # 地支藏干
        self.dizhi_canggan = {
            '子': ['癸'], '丑': ['己', '癸', '辛'], '寅': ['甲', '丙', '戊'],
            '卯': ['乙'], '辰': ['戊', '乙', '癸'], '巳': ['丙', '戊', '庚'],
            '午': ['丁', '己'], '未': ['己', '丁', '乙'], '申': ['庚', '壬', '戊'],
            '酉': ['辛'], '戌': ['戊', '辛', '丁'], '亥': ['壬', '甲']
        }
        
        # 五行生克关系
        self.sheng_map = {'木': '火', '火': '土', '土': '金', '金': '水', '水': '木'}
        self.ke_map = {'木': '土', '火': '金', '土': '水', '金': '木', '水': '火'}
        
        # 十神关系映射
        self.shishen_map = {
            ('same', 'same'): '比肩',
            ('same', 'diff'): '劫财',
            ('sheng', 'same'): '食神',
            ('sheng', 'diff'): '伤官',
            ('ke', 'same'): '偏财',
            ('ke', 'diff'): '正财',
            ('bei_ke', 'same'): '七杀',
            ('bei_ke', 'diff'): '正官',
            ('bei_sheng', 'same'): '偏印',
            ('bei_sheng', 'diff'): '正印'
        }
    
    def calculate(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """主计算函数"""
        print("正在进行八字排盘计算...")
        
        # 处理时间未知的情况
        if processed_data.get('time_analysis_needed', False):
            return self._calculate_time_range_analysis(processed_data)
        
        # 正常计算流程
        return self._calculate_normal_bazi(processed_data)
    
    def _calculate_normal_bazi(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """正常的八字计算"""
        # 使用真太阳时或标准时间
        if processed_data.get('solar_datetime'):
            calc_datetime = processed_data['solar_datetime']
        else:
            calc_datetime = processed_data['birth_datetime']
        
        # 计算四柱
        year_pillar = self._get_year_pillar(calc_datetime.year)
        month_pillar = self._get_month_pillar(calc_datetime.year, calc_datetime.month, calc_datetime.day)
        day_pillar = self._get_day_pillar(calc_datetime)
        hour_pillar = self._get_hour_pillar(day_pillar[0], calc_datetime.hour)
        
        # 构建八字数据
        bazi_data = {
            'year_pillar': year_pillar,
            'month_pillar': month_pillar,
            'day_pillar': day_pillar,
            'hour_pillar': hour_pillar
        }
        
        # 五行分析
        wuxing_analysis = self._analyze_wuxing(bazi_data)
        
        # 十神分析
        shishen_analysis = self._analyze_shishen(bazi_data)
        
        # 格局分析
        pattern_analysis = self._analyze_pattern(bazi_data, wuxing_analysis)
        
        # 大运计算
        dayun_analysis = self._calculate_dayun(bazi_data, processed_data)
        
        return {
            'bazi_pillars': bazi_data,
            'wuxing_analysis': wuxing_analysis,
            'shishen_analysis': shishen_analysis,
            'pattern_analysis': pattern_analysis,
            'dayun_analysis': dayun_analysis,
            'calculation_metadata': {
                'calculation_time': datetime.now().isoformat(),
                'time_used': 'solar_time' if processed_data.get('solar_datetime') else 'standard_time',
                'accuracy_level': self._assess_calculation_accuracy(processed_data)
            }
        }
    
    def _calculate_time_range_analysis(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """时间未知时的范围分析"""
        print("时间未知，正在进行12时辰范围分析...")
        
        time_analyses = []
        base_date = datetime(
            processed_data['birth_year'],
            processed_data['birth_month'],
            processed_data['birth_day']
        )
        
        # 计算12个时辰的可能结果
        for hour in [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]:  # 各时辰代表时间
            temp_data = processed_data.copy()
            temp_data['birth_datetime'] = base_date.replace(hour=hour)
            temp_data['time_analysis_needed'] = False
            
            # 计算该时辰的八字
            hour_result = self._calculate_normal_bazi(temp_data)
            
            time_analyses.append({
                'hour': hour,
                'shichen': self._get_shichen_name(hour),
                'bazi_result': hour_result,
                'key_characteristics': self._extract_key_characteristics(hour_result)
            })
        
        return {
            'analysis_type': 'time_range',
            'time_analyses': time_analyses,
            'recommendation': self._recommend_most_likely_time(time_analyses),
            'calculation_metadata': {
                'calculation_time': datetime.now().isoformat(),
                'analysis_type': 'time_range_analysis',
                'accuracy_level': '时间范围分析'
            }
        }
    
    def _get_year_pillar(self, year: int) -> Tuple[str, str]:
        """计算年柱"""
        # 以1984年甲子为基准
        base_year = 1984
        offset = (year - base_year) % 60
        
        tg_index = offset % 10
        dz_index = offset % 12
        
        return (self.tiangan[tg_index], self.dizhi[dz_index])
    
    def _get_month_pillar(self, year: int, month: int, day: int) -> Tuple[str, str]:
        """计算月柱"""
        # 简化的月柱计算，实际需要考虑节气
        # 这里使用近似方法
        
        # 月地支（从寅月开始）
        month_dizhi = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑']
        dz = month_dizhi[month - 1]
        
        # 月天干根据年天干推算
        year_tg = self._get_year_pillar(year)[0]
        year_tg_index = self.tiangan.index(year_tg)
        
        # 甲己年起丙寅，乙庚年起戊寅...
        month_tg_base = [2, 4, 6, 8, 0]  # 对应丙、戊、庚、壬、甲
        month_tg_index = (month_tg_base[year_tg_index % 5] + month - 1) % 10
        
        tg = self.tiangan[month_tg_index]
        
        return (tg, dz)
    
    def _get_day_pillar(self, date: datetime) -> Tuple[str, str]:
        """计算日柱"""
        # 使用简化的日柱计算方法
        # 以2000年1月1日甲辰日为基准
        base_date = datetime(2000, 1, 1)
        days_diff = (date - base_date).days
        
        # 60甲子循环
        cycle_position = days_diff % 60
        
        tg_index = cycle_position % 10
        dz_index = cycle_position % 12
        
        return (self.tiangan[tg_index], self.dizhi[dz_index])
    
    def _get_hour_pillar(self, day_tg: str, hour: int) -> Tuple[str, str]:
        """计算时柱"""
        # 时辰地支
        hour_dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 确定时辰
        if hour == 23 or hour == 0:
            dz_index = 0  # 子时
        else:
            dz_index = (hour + 1) // 2
        
        dz = hour_dizhi[dz_index]
        
        # 时天干根据日天干推算
        day_tg_index = self.tiangan.index(day_tg)
        
        # 甲己日起甲子，乙庚日起丙子...
        hour_tg_base = [0, 2, 4, 6, 8]  # 对应甲、丙、戊、庚、壬
        hour_tg_index = (hour_tg_base[day_tg_index % 5] + dz_index) % 10
        
        tg = self.tiangan[hour_tg_index]
        
        return (tg, dz)
    
    def _analyze_wuxing(self, bazi_data: Dict[str, Any]) -> Dict[str, Any]:
        """五行分析"""
        # 统计五行分布
        wuxing_count = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}
        
        # 统计天干地支的五行
        for pillar_name, (tg, dz) in bazi_data.items():
            wuxing_count[self.wuxing_map[tg]] += 1
            wuxing_count[self.wuxing_map[dz]] += 1
        
        # 日主五行
        day_master_element = self.wuxing_map[bazi_data['day_pillar'][0]]
        
        # 计算强弱（简化算法）
        strength_score = self._calculate_strength_score(bazi_data, wuxing_count, day_master_element)
        
        # 确定用神忌神
        useful_god, avoid_god = self._determine_useful_god(strength_score, day_master_element, wuxing_count)
        
        return {
            'distribution': wuxing_count,
            'day_master_element': day_master_element,
            'strength_score': strength_score,
            'strength_level': self._score_to_strength_level(strength_score),
            'useful_god': useful_god,
            'avoid_god': avoid_god
        }
    
    def _analyze_shishen(self, bazi_data: Dict[str, Any]) -> Dict[str, Any]:
        """十神分析"""
        day_master = bazi_data['day_pillar'][0]
        shishen_count = {}
        shishen_details = {}
        
        for pillar_name, (tg, dz) in bazi_data.items():
            if pillar_name != 'day_pillar':  # 日主不算十神
                # 计算天干十神
                tg_shishen = self._get_shishen_relation(day_master, tg)
                if tg_shishen:
                    shishen_count[tg_shishen] = shishen_count.get(tg_shishen, 0) + 1
                    if tg_shishen not in shishen_details:
                        shishen_details[tg_shishen] = []
                    shishen_details[tg_shishen].append(f"{pillar_name}_天干_{tg}")
        
        return {
            'distribution': shishen_count,
            'details': shishen_details,
            'dominant_shishen': max(shishen_count.items(), key=lambda x: x[1])[0] if shishen_count else None
        }
    
    def _analyze_pattern(self, bazi_data: Dict[str, Any], wuxing_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """格局分析（简化版）"""
        # 这里实现简化的格局判断
        month_pillar = bazi_data['month_pillar']
        day_master = bazi_data['day_pillar'][0]
        
        # 根据月令确定基本格局
        month_tg_shishen = self._get_shishen_relation(day_master, month_pillar[0])
        
        if month_tg_shishen in ['正官', '七杀']:
            pattern_type = '官杀格'
        elif month_tg_shishen in ['正财', '偏财']:
            pattern_type = '财格'
        elif month_tg_shishen in ['食神', '伤官']:
            pattern_type = '食伤格'
        elif month_tg_shishen in ['正印', '偏印']:
            pattern_type = '印格'
        else:
            pattern_type = '比劫格'
        
        return {
            'pattern_type': pattern_type,
            'pattern_strength': '中等',  # 简化处理
            'pattern_quality': '成格'    # 简化处理
        }
    
    def _calculate_dayun(self, bazi_data: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """大运计算（简化版）"""
        # 这里实现简化的大运计算
        gender = processed_data.get('gender', '男')
        birth_year = processed_data['birth_year']
        
        # 简化的起运年龄计算
        start_age = 8 if gender == '男' else 7
        
        dayun_list = []
        month_pillar = bazi_data['month_pillar']
        
        # 计算10步大运
        for i in range(10):
            age_start = start_age + i * 10
            age_end = age_start + 9
            year_start = birth_year + age_start
            year_end = birth_year + age_end
            
            dayun_list.append({
                'step': i + 1,
                'age_range': f"{age_start}-{age_end}岁",
                'year_range': f"{year_start}-{year_end}年",
                'pillar': f"第{i+1}步大运",  # 简化处理
                'description': f"{age_start}岁起运"
            })
        
        return {
            'start_age': start_age,
            'dayun_list': dayun_list[:5]  # 只显示前5步
        }
    
    def _get_shishen_relation(self, day_master: str, target_gan: str) -> Optional[str]:
        """获取十神关系"""
        if day_master == target_gan:
            return None  # 日主本身
        
        day_element = self.wuxing_map[day_master]
        target_element = self.wuxing_map[target_gan]
        
        # 判断五行关系
        if day_element == target_element:
            relation = 'same'
        elif self.sheng_map.get(day_element) == target_element:
            relation = 'sheng'
        elif self.ke_map.get(day_element) == target_element:
            relation = 'ke'
        elif self.ke_map.get(target_element) == day_element:
            relation = 'bei_ke'
        elif self.sheng_map.get(target_element) == day_element:
            relation = 'bei_sheng'
        else:
            return None
        
        # 判断阴阳关系
        day_yinyang = self.yinyang_map[day_master]
        target_yinyang = self.yinyang_map[target_gan]
        yinyang_relation = 'same' if day_yinyang == target_yinyang else 'diff'
        
        return self.shishen_map.get((relation, yinyang_relation))
    
    def _calculate_strength_score(self, bazi_data: Dict[str, Any], wuxing_count: Dict[str, int], day_master_element: str) -> float:
        """计算日主强弱得分"""
        score = 0
        
        # 同类得分
        score += wuxing_count[day_master_element] * 2
        
        # 生我得分
        for element, count in wuxing_count.items():
            if self.sheng_map.get(element) == day_master_element:
                score += count * 1
        
        # 克我扣分
        for element, count in wuxing_count.items():
            if self.ke_map.get(element) == day_master_element:
                score -= count * 1
        
        return score
    
    def _score_to_strength_level(self, score: float) -> str:
        """分数转强弱等级"""
        if score >= 6:
            return '偏旺'
        elif score >= 4:
            return '中和'
        elif score >= 2:
            return '偏弱'
        else:
            return '太弱'
    
    def _determine_useful_god(self, strength_score: float, day_master_element: str, wuxing_count: Dict[str, int]) -> Tuple[str, str]:
        """确定用神忌神"""
        if strength_score >= 5:  # 偏旺
            # 需要克泄耗
            useful_elements = []
            if self.ke_map.get(day_master_element):
                useful_elements.append(self.ke_map[day_master_element])
            if self.sheng_map.get(day_master_element):
                useful_elements.append(self.sheng_map[day_master_element])
            
            useful_god = '、'.join(useful_elements) if useful_elements else '土'
            avoid_god = day_master_element
        else:  # 偏弱
            # 需要生扶
            useful_elements = [day_master_element]
            for element in self.wuxing_map.values():
                if self.sheng_map.get(element) == day_master_element:
                    useful_elements.append(element)
            
            useful_god = '、'.join(set(useful_elements))
            avoid_god = self.ke_map.get(day_master_element, '金')
        
        return useful_god, avoid_god
    
    def _get_shichen_name(self, hour: int) -> str:
        """获取时辰名称"""
        shichen_names = ['子时', '丑时', '寅时', '卯时', '辰时', '巳时', 
                        '午时', '未时', '申时', '酉时', '戌时', '亥时']
        
        if hour == 23 or hour == 0:
            return '子时'
        else:
            return shichen_names[(hour + 1) // 2]
    
    def _extract_key_characteristics(self, bazi_result: Dict[str, Any]) -> List[str]:
        """提取关键特征"""
        characteristics = []
        
        wuxing = bazi_result['wuxing_analysis']
        characteristics.append(f"日主{wuxing['day_master_element']}，{wuxing['strength_level']}")
        
        if 'pattern_analysis' in bazi_result:
            pattern = bazi_result['pattern_analysis']
            characteristics.append(f"{pattern['pattern_type']}")
        
        return characteristics
    
    def _recommend_most_likely_time(self, time_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """推荐最可能的时间"""
        # 简化的推荐逻辑
        return {
            'recommended_time': '中午12点',
            'reason': '时间未知时的常用默认值',
            'confidence': 0.5
        }
    
    def _assess_calculation_accuracy(self, processed_data: Dict[str, Any]) -> str:
        """评估计算准确度"""
        if processed_data.get('time_analysis_needed'):
            return '时间范围分析'
        elif processed_data.get('time_certainty') == 'exact':
            return '高精度'
        elif processed_data.get('time_certainty') == 'approximate':
            return '中等精度'
        else:
            return '标准精度'

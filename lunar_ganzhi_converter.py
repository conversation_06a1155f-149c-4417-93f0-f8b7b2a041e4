#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历干支转换器
用户输入阳历日期，输出农历和年月日柱信息
支持多个库的对比输出
"""

from datetime import datetime, date
import sys

class LunarGanzhiConverter:
    """农历干支转换器"""
    
    def __init__(self):
        self.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        self.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 检查可用的库
        self.available_libraries = self._check_available_libraries()
        
    def _check_available_libraries(self):
        """检查可用的农历库"""
        libraries = {}
        
        # 检查 zhdate
        try:
            import zhdate
            libraries['zhdate'] = zhdate
            print("✅ zhdate 库可用")
        except ImportError:
            print("❌ zhdate 库未安装")
        
        # 检查 lunardate
        try:
            from lunardate import LunarDate
            libraries['lunardate'] = LunarDate
            print("✅ lunardate 库可用")
        except ImportError:
            print("❌ lunardate 库未安装")
        
        # 检查 LunarCalendar
        try:
            from LunarCalendar import Converter, Solar
            libraries['LunarCalendar'] = {'Converter': Converter, 'Solar': Solar}
            print("✅ LunarCalendar 库可用")
        except ImportError:
            print("❌ LunarCalendar 库未安装")
        
        # 检查 sxtwl (寿星天文历)
        try:
            import sxtwl
            libraries['sxtwl'] = sxtwl
            print("✅ sxtwl 库可用 (专业天文历)")
        except ImportError:
            print("❌ sxtwl 库未安装")
        
        return libraries
    
    def calculate_ganzhi_year(self, year):
        """计算年柱干支"""
        # 以1984年甲子年为基准
        base_year = 1984
        year_diff = year - base_year
        ganzhi_index = year_diff % 60
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return self.tiangan[tg_index] + self.dizhi[dz_index]
    
    def calculate_ganzhi_month(self, year, month):
        """计算月柱干支（简化版本）"""
        # 年干对应的月干起始
        year_tg_index = (year - 1984) % 10
        month_tg_start = (year_tg_index * 2) % 10
        
        # 月支固定：寅卯辰巳午未申酉戌亥子丑
        month_dz_map = [10, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9]  # 1月=寅=2
        
        tg_index = (month_tg_start + month - 1) % 10
        dz_index = month_dz_map[month - 1]
        
        return self.tiangan[tg_index] + self.dizhi[dz_index]
    
    def calculate_ganzhi_day(self, year, month, day):
        """计算日柱干支"""
        # 使用儒略日数计算
        julian_day = date(year, month, day).toordinal() + 1721425
        
        # 甲子日的儒略日数（1900年1月31日是甲子日）
        jiazi_julian = 1924681
        
        # 计算距离甲子日的天数
        days_diff = julian_day - jiazi_julian
        
        # 60甲子循环
        ganzhi_index = days_diff % 60
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return self.tiangan[tg_index] + self.dizhi[dz_index]
    
    def calculate_ganzhi_hour(self, hour, day_ganzhi):
        """计算时柱干支"""
        # 时辰对应地支
        hour_dizhi_map = {
            23: 0, 0: 0, 1: 1,    # 子时 23-1点
            2: 1, 3: 2,           # 丑时 1-3点, 寅时 3-5点
            4: 2, 5: 3,           # 寅时, 卯时 5-7点
            6: 3, 7: 4,           # 卯时, 辰时 7-9点
            8: 4, 9: 5,           # 辰时, 巳时 9-11点
            10: 5, 11: 6,         # 巳时, 午时 11-13点
            12: 6, 13: 7,         # 午时, 未时 13-15点
            14: 7, 15: 8,         # 未时, 申时 15-17点
            16: 8, 17: 9,         # 申时, 酉时 17-19点
            18: 9, 19: 10,        # 酉时, 戌时 19-21点
            20: 10, 21: 11,       # 戌时, 亥时 21-23点
            22: 11
        }
        
        # 获取时辰地支
        hour_dz_index = hour_dizhi_map.get(hour, 0)
        
        # 根据日干推算时干
        day_tg_index = self.tiangan.index(day_ganzhi[0])
        hour_tg_index = (day_tg_index * 2 + hour_dz_index) % 10
        
        return self.tiangan[hour_tg_index] + self.dizhi[hour_dz_index]
    
    def get_lunar_info_zhdate(self, year, month, day):
        """使用zhdate获取农历信息"""
        if 'zhdate' not in self.available_libraries:
            return None
        
        try:
            zhdate = self.available_libraries['zhdate']
            dt = datetime(year, month, day)
            zh_date = zhdate.ZhDate.from_datetime(dt)
            
            return {
                'lunar_year': zh_date.lunar_year,
                'lunar_month': zh_date.lunar_month,
                'lunar_day': zh_date.lunar_day,
                'is_leap': getattr(zh_date, 'is_leap', False),
                'library': 'zhdate'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_lunar_info_lunardate(self, year, month, day):
        """使用lunardate获取农历信息"""
        if 'lunardate' not in self.available_libraries:
            return None
        
        try:
            LunarDate = self.available_libraries['lunardate']
            lunar_date = LunarDate.fromSolarDate(year, month, day)
            
            return {
                'lunar_year': lunar_date.year,
                'lunar_month': lunar_date.month,
                'lunar_day': lunar_date.day,
                'is_leap': getattr(lunar_date, 'isLeapMonth', False),
                'library': 'lunardate'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_lunar_info_lunarcalendar(self, year, month, day):
        """使用LunarCalendar获取农历信息"""
        if 'LunarCalendar' not in self.available_libraries:
            return None
        
        try:
            Converter = self.available_libraries['LunarCalendar']['Converter']
            Solar = self.available_libraries['LunarCalendar']['Solar']
            
            solar = Solar(year, month, day)
            lunar = Converter.Solar2Lunar(solar)
            
            result = {
                'lunar_year': lunar.year,
                'lunar_month': lunar.month,
                'lunar_day': lunar.day,
                'is_leap': getattr(lunar, 'isLeapMonth', False),
                'library': 'LunarCalendar'
            }
            
            # 尝试获取干支信息
            if hasattr(lunar, 'gz_year'):
                result['gz_year'] = lunar.gz_year
            if hasattr(lunar, 'gz_month'):
                result['gz_month'] = lunar.gz_month
            if hasattr(lunar, 'gz_day'):
                result['gz_day'] = lunar.gz_day
            
            return result
        except Exception as e:
            return {'error': str(e)}
    
    def get_lunar_info_sxtwl(self, year, month, day):
        """使用sxtwl获取农历和干支信息"""
        if 'sxtwl' not in self.available_libraries:
            return None
        
        try:
            sxtwl = self.available_libraries['sxtwl']
            lunar = sxtwl.Lunar()
            day_obj = lunar.getDayBySolar(year, month, day)
            
            return {
                'lunar_year': day_obj.getLunarYear(),
                'lunar_month': day_obj.getLunarMonth(),
                'lunar_day': day_obj.getLunarDay(),
                'is_leap': day_obj.isLunarLeap(),
                'gz_year': day_obj.getYearGZ(),
                'gz_month': day_obj.getMonthGZ(),
                'gz_day': day_obj.getDayGZ(),
                'library': 'sxtwl'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def convert_date(self, year, month, day, hour=12):
        """转换日期，返回所有库的结果对比"""
        print(f"\n🌟 阳历日期: {year}年{month}月{day}日 {hour}时")
        print("=" * 60)
        
        # 自实现的干支计算
        self_year_gz = self.calculate_ganzhi_year(year)
        self_month_gz = self.calculate_ganzhi_month(year, month)
        self_day_gz = self.calculate_ganzhi_day(year, month, day)
        self_hour_gz = self.calculate_ganzhi_hour(hour, self_day_gz)
        
        print(f"🔧 自实现算法:")
        print(f"   年柱: {self_year_gz}")
        print(f"   月柱: {self_month_gz}")
        print(f"   日柱: {self_day_gz}")
        print(f"   时柱: {self_hour_gz}")
        
        # 测试各个库
        libraries_to_test = [
            ('zhdate', self.get_lunar_info_zhdate),
            ('lunardate', self.get_lunar_info_lunardate),
            ('LunarCalendar', self.get_lunar_info_lunarcalendar),
            ('sxtwl', self.get_lunar_info_sxtwl)
        ]
        
        for lib_name, lib_func in libraries_to_test:
            print(f"\n📚 {lib_name} 库:")
            result = lib_func(year, month, day)
            
            if result is None:
                print("   ❌ 库不可用")
            elif 'error' in result:
                print(f"   ❌ 错误: {result['error']}")
            else:
                # 显示农历信息
                lunar_str = f"农历{result['lunar_year']}年{result['lunar_month']}月{result['lunar_day']}日"
                if result.get('is_leap'):
                    lunar_str += " (闰月)"
                print(f"   农历: {lunar_str}")
                
                # 显示干支信息（如果有）
                if 'gz_year' in result:
                    print(f"   年柱: {result['gz_year']}")
                if 'gz_month' in result:
                    print(f"   月柱: {result['gz_month']}")
                if 'gz_day' in result:
                    print(f"   日柱: {result['gz_day']}")
        
        return {
            'self_calculated': {
                'year_gz': self_year_gz,
                'month_gz': self_month_gz,
                'day_gz': self_day_gz,
                'hour_gz': self_hour_gz
            }
        }

def main():
    """主函数 - 用户交互界面"""
    print("🌙 农历干支转换器")
    print("=" * 50)
    print("支持多个农历库的对比输出")
    print("包含自实现的干支计算算法")
    print()
    
    converter = LunarGanzhiConverter()
    
    if not converter.available_libraries:
        print("⚠️ 没有检测到可用的农历库")
        print("建议安装: pip install zhdate lunardate")
        print("但仍可使用自实现的干支计算功能")
        print()
    
    while True:
        try:
            print("\n" + "="*50)
            print("请输入阳历日期 (格式: YYYY-MM-DD)")
            print("或输入 'quit' 退出程序")
            
            user_input = input("请输入日期: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            try:
                date_parts = user_input.split('-')
                if len(date_parts) != 3:
                    raise ValueError("日期格式错误")
                
                year = int(date_parts[0])
                month = int(date_parts[1])
                day = int(date_parts[2])
                
                # 验证日期有效性
                datetime(year, month, day)
                
            except ValueError as e:
                print(f"❌ 日期格式错误: {e}")
                print("请使用格式: YYYY-MM-DD (例如: 1988-03-15)")
                continue
            
            # 询问时辰
            try:
                hour_input = input("请输入时辰 (0-23，默认12): ").strip()
                hour = int(hour_input) if hour_input else 12
                
                if not 0 <= hour <= 23:
                    raise ValueError("时辰必须在0-23之间")
                    
            except ValueError:
                print("⚠️ 时辰格式错误，使用默认值12")
                hour = 12
            
            # 执行转换
            converter.convert_date(year, month, day, hour)
            
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()

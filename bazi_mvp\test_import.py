#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入是否正常
"""

try:
    print("测试导入模块...")
    
    from data.corpus_database import COUPLE_COMPATIBILITY_CORPUS
    print("✅ corpus_database 导入成功")
    
    from modules.corpus_matcher import CorpusMatcher
    print("✅ corpus_matcher 导入成功")
    
    from modules.couple_compatibility import CoupleCompatibilityCalculator
    print("✅ couple_compatibility 导入成功")
    
    print("🎉 所有模块导入成功！缩进问题已修复")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()

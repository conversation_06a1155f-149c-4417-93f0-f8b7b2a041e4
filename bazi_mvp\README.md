# 生辰八字算命系统 MVP版本

## 项目简介

这是一个基于传统命理学的现代化生辰八字算命系统MVP版本，采用模块化设计，支持命令行交互，能够处理各种复杂的输入情况并生成标准化的分析报告。

## 功能特点

### 🎯 核心功能
- **智能输入收集**: 多轮对话式信息收集，支持模糊时间输入
- **精确八字计算**: 基于传统算法的高精度八字排盘
- **丰富语料库**: 包含性格、事业、感情、健康等多维度分析内容
- **标准化输出**: 生成结构化、现代化的分析报告
- **质量保证**: 内置质量检查和改进机制

### 🛠️ 技术特点
- **模块化设计**: 各功能模块独立，易于维护和扩展
- **错误处理**: 完善的异常处理和降级机制
- **日志系统**: 详细的运行日志和性能监控
- **数据安全**: 用户隐私保护和数据脱敏

## 系统架构

```
用户输入模块 → 数据处理模块 → 八字计算模块 → 语料匹配模块 → 输出生成模块
     ↓              ↓              ↓              ↓              ↓
  多轮问答        数据标准化      精确排盘        智能匹配        报告生成
  智能验证        时区处理        五行分析        现代化转换      质量检查
  错误处理        格式转换        格局判断        语料检索        标准输出
```

## 目录结构

```
bazi_mvp/
├── main.py                 # 主程序入口
├── test_system.py          # 系统测试脚本
├── README.md              # 项目说明文档
├── modules/               # 核心模块
│   ├── __init__.py
│   ├── user_input.py      # 用户输入模块
│   ├── data_processor.py  # 数据处理模块
│   ├── bazi_calculator.py # 八字计算模块
│   ├── corpus_matcher.py  # 语料匹配模块
│   └── output_generator.py # 输出生成模块
├── data/                  # 数据文件
│   ├── __init__.py
│   └── corpus_database.py # 语料库数据
├── utils/                 # 工具模块
│   ├── __init__.py
│   └── logger.py          # 日志系统
└── logs/                  # 日志文件目录
```

## 快速开始

### 1. 运行主程序

```bash
cd bazi_mvp
python main.py
```

### 2. 运行测试

```bash
python test_system.py
```

选择测试模式：
- 选择 `1`: 使用预设示例数据快速测试
- 选择 `2`: 完整交互式测试
- 选择 `3`: 退出

## 使用说明

### 输入信息

系统会通过多轮对话收集以下信息：

1. **基本信息**
   - 姓名（可选）
   - 性别（可选）

2. **出生日期**
   - 出生年份（必需，1900-2030）
   - 出生月份（必需，1-12）
   - 出生日期（必需，1-31）

3. **出生时间**
   - 精确时间（如：14:30）
   - 模糊时间（如：下午、晚上）
   - 时间未知（系统提供处理方案）

4. **出生地点**
   - 城市名称（用于时区计算）

### 输出内容

系统生成的分析报告包含：

1. **基本信息**: 用户输入的基本资料
2. **八字排盘**: 年月日时四柱八字
3. **五行分析**: 五行分布、强弱、用神忌神
4. **性格特征**: 基于五行和十神的性格分析
5. **事业运势**: 适合的职业方向和发展建议
6. **感情婚姻**: 恋爱风格和婚姻展望
7. **健康状况**: 需要关注的健康方面
8. **人生建议**: 个性化的人生指导建议
9. **分析元数据**: 准确度、质量评估等信息

## 特殊情况处理

### 时间未知处理

当用户不知道确切出生时间时，系统提供三种处理方案：

1. **默认时间**: 使用中午12点作为默认时间
2. **范围分析**: 分析所有可能时辰的结果
3. **咨询建议**: 提供咨询家人的指导

### 数据质量保证

系统内置多层质量检查：

- **输入验证**: 确保数据格式正确和合理性
- **计算验证**: 验证八字计算的准确性
- **内容质量**: 检查分析内容的完整性和长度
- **一致性检查**: 确保相同输入产生相同输出

## 技术细节

### 八字计算算法

- **年柱计算**: 基于公历年份的天干地支推算
- **月柱计算**: 考虑节气的月柱推算
- **日柱计算**: 精确的日柱计算算法
- **时柱计算**: 基于真太阳时的时柱推算

### 语料库内容

- **性格分析**: 基于五行旺衰的性格特征描述
- **事业分析**: 根据格局类型的职业建议
- **感情分析**: 基于十神关系的感情特点
- **健康分析**: 五行对应的健康注意事项
- **现代化词典**: 传统术语的现代化解释

### 日志系统

- **用户行为日志**: 记录用户输入和操作（脱敏处理）
- **系统运行日志**: 记录各模块的执行情况
- **错误日志**: 详细的错误信息和上下文
- **性能日志**: 系统性能和响应时间统计

## 开发说明

### 扩展新功能

1. **添加新的分析维度**: 在 `corpus_database.py` 中添加新的语料内容
2. **改进计算算法**: 修改 `bazi_calculator.py` 中的相关算法
3. **优化用户交互**: 改进 `user_input.py` 中的对话逻辑
4. **增强输出格式**: 扩展 `output_generator.py` 的报告模板

### 性能优化

- 使用缓存机制减少重复计算
- 优化语料匹配算法
- 实现异步处理提高响应速度
- 添加数据库支持大规模数据

## 注意事项

1. **准确性说明**: 本系统基于传统命理学理论，分析结果仅供参考
2. **隐私保护**: 系统会对用户输入进行脱敏处理
3. **数据安全**: 不会永久存储用户的个人信息
4. **使用限制**: 请理性对待分析结果，不要过分依赖

## 版本信息

- **当前版本**: MVP-1.0
- **开发状态**: 原型验证阶段
- **Python版本**: 3.7+
- **依赖库**: 仅使用Python标准库

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目仓库: [GitHub链接]
- 技术支持: [邮箱地址]
- 文档更新: [文档链接]

---

**免责声明**: 本系统提供的分析结果仅供娱乐和参考，不应作为人生重大决策的唯一依据。请理性对待命理分析，结合实际情况做出判断。

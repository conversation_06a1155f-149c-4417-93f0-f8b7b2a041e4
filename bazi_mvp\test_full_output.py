#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整输出显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator
from modules.corpus_matcher import CorpusMatcher
from modules.output_generator import OutputGenerator

def display_result(report):
    """显示最终结果"""
    print("\n" + "=" * 60)
    print("🎯 您的八字分析报告")
    print("=" * 60)
    
    # 基本信息
    print(f"\n📋 基本信息:")
    print(f"   姓名: {report.get('name', '未提供')}")
    print(f"   性别: {report.get('gender', '未提供')}")
    print(f"   出生时间: {report.get('birth_time', '未提供')}")
    print(f"   出生地点: {report.get('birth_location', '未提供')}")
    
    # 八字信息
    bazi_info = report.get('bazi_info', {})
    print(f"\n🎴 八字排盘:")
    print(f"   年柱: {bazi_info.get('year_pillar', 'N/A')}")
    print(f"   月柱: {bazi_info.get('month_pillar', 'N/A')}")
    print(f"   日柱: {bazi_info.get('day_pillar', 'N/A')}")
    print(f"   时柱: {bazi_info.get('hour_pillar', 'N/A')}")
    
    # 五行分析
    wuxing_info = report.get('wuxing_analysis', {})
    print(f"\n🌟 五行分析:")
    distribution = wuxing_info.get('distribution', {})
    for element, count in distribution.items():
        print(f"   {element}: {count}个")
    print(f"   日主强弱: {wuxing_info.get('strength', 'N/A')}")
    print(f"   用神: {wuxing_info.get('useful_god', 'N/A')}")
    
    # 分析内容
    analysis = report.get('analysis', {})
    
    print(f"\n👤 性格特征:")
    personality = analysis.get('personality', '暂无分析')
    print(f"   {personality}")
    
    print(f"\n💼 事业运势:")
    career = analysis.get('career', '暂无分析')
    print(f"   {career}")
    
    print(f"\n💕 感情婚姻:")
    relationship = analysis.get('relationship', '暂无分析')
    print(f"   {relationship}")
    
    print(f"\n🏥 健康状况:")
    health = analysis.get('health', '暂无分析')
    print(f"   {health}")
    
    # 建议
    suggestions = report.get('suggestions', [])
    if suggestions:
        print(f"\n💡 人生建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
    
    # 系统信息
    metadata = report.get('metadata', {})
    print(f"\n📊 分析信息:")
    print(f"   计算准确度: {metadata.get('accuracy', 'N/A')}")
    print(f"   分析时间: {metadata.get('analysis_time', 'N/A')}")
    print(f"   系统版本: {metadata.get('version', 'MVP-1.0')}")
    
    print("\n" + "=" * 60)
    print("✨ 分析完成！感谢使用八字算命系统")
    print("=" * 60)

def test_full_output():
    """测试完整输出"""
    print("🧪 测试完整输出显示...")
    
    # 示例数据
    sample_data = {
        'name': '李明',
        'gender': '男',
        'year': 1988,
        'month': 8,
        'day': 8,
        'hour': 10,
        'minute': 30,
        'birth_location': '上海市',
        'time_certainty': 'exact'
    }
    
    # 初始化模块
    data_processor = DataProcessor()
    bazi_calculator = BaziCalculator()
    corpus_matcher = CorpusMatcher()
    output_generator = OutputGenerator()
    
    try:
        # 完整流程
        print("1. 数据处理...")
        processed_data = data_processor.process(sample_data)
        
        print("2. 八字计算...")
        calculation_result = bazi_calculator.calculate(processed_data)
        
        print("3. 语料匹配...")
        matched_content = corpus_matcher.match(calculation_result)
        
        print("4. 输出生成...")
        final_report = output_generator.generate(calculation_result, matched_content, processed_data)
        
        print("5. 显示结果...")
        display_result(final_report)
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_output()

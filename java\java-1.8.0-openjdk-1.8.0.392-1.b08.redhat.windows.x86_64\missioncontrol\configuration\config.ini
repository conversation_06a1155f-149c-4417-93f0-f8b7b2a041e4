#This configuration file was written by: org.eclipse.equinox.internal.frameworkadmin.equinox.EquinoxFwConfigFileParser
#Wed Oct 11 15:11:11 PDT 2023
org.eclipse.update.reconcile=false
eclipse.p2.profile=DefaultProfile
osgi.instance.area.default=@user.home/AppData/Local/RedHat/java-1.8.0-openjdk-1.8.0.392-1.b08.redhat.windows.x86_64/missioncontrol
osgi.framework=file\:plugins/org.eclipse.osgi_3.15.0.v20190830-1434.jar
osgi.signedcontent.support=true
osgi.bundles=reference\:file\:org.apache.felix.scr_2.1.14.v20190123-1619.jar@1\:start,reference\:file\:org.eclipse.equinox.simpleconfigurator_1.3.300.v20190716-0825.jar@1\:start
org.eclipse.equinox.simpleconfigurator.configUrl=file\:org.eclipse.equinox.simpleconfigurator/bundles.info
eclipse.product=org.openjdk.jmc.rcp.application.product
osgi.splashPath=platform\:/base/plugins/org.openjdk.jmc.rcp.application
osgi.framework.extensions=reference\:file\:org.eclipse.osgi.compatibility.state_1.1.600.v20190814-1451.jar,reference\:file\:org.openjdk.jmc.osgi.extension_7.1.1.202310112209.jar
eclipse.p2.data.area=@config.dir/../p2
eclipse.home.location=$osgi.install.area$
eclipse.p2.unsignedPolicy=prompt
osgi.bundles.defaultStartLevel=4
eclipse.application=org.openjdk.jmc.rcp.application.app

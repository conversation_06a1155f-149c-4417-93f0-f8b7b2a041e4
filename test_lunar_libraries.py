#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试各种农历库的干支计算功能
专门寻找能够计算年月日柱的库
"""

from datetime import datetime, date

def test_borax_library():
    """测试borax库的农历功能"""
    print("🔍 测试 borax 库")
    print("-" * 40)
    
    try:
        from borax.calendars.lunardate import LunarDate
        from borax.calendars.festivals2 import get_festival
        
        # 测试农历转换
        solar_date = date(1988, 3, 15)
        lunar_date = LunarDate.from_solar_date(solar_date)
        
        print(f"阳历: {solar_date}")
        print(f"农历: {lunar_date}")
        print(f"农历详细: 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
        
        # 检查是否有干支相关方法
        print("\n检查干支相关方法:")
        ganzhi_attrs = ['ganzhi', 'gz_year', 'gz_month', 'gz_day', 'year_ganzhi', 'month_ganzhi', 'day_ganzhi']
        
        for attr in ganzhi_attrs:
            if hasattr(lunar_date, attr):
                try:
                    value = getattr(lunar_date, attr)
                    if callable(value):
                        result = value()
                        print(f"  ✅ {attr}(): {result}")
                    else:
                        print(f"  ✅ {attr}: {value}")
                except Exception as e:
                    print(f"  ❌ {attr}: 调用失败 - {e}")
            else:
                print(f"  ❌ {attr}: 不存在")
        
        # 检查所有可用方法
        print("\n所有可用方法和属性:")
        for attr in dir(lunar_date):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar_date, attr)
                    if callable(value):
                        print(f"  方法: {attr}()")
                    else:
                        print(f"  属性: {attr} = {value}")
                except:
                    pass
        
        return True
        
    except ImportError:
        print("❌ borax 库未安装")
        return False
    except Exception as e:
        print(f"❌ borax 测试失败: {e}")
        return False

def test_lunar_python():
    """测试lunar-python库"""
    print("\n🔍 测试 lunar-python 库")
    print("-" * 40)
    
    try:
        import lunar_python
        
        print("✅ lunar-python 库可用")
        
        # 检查可用的类和方法
        print("可用的类和方法:")
        for attr in dir(lunar_python):
            if not attr.startswith('_'):
                print(f"  {attr}")
        
        # 尝试创建实例
        if hasattr(lunar_python, 'Lunar'):
            lunar = lunar_python.Lunar()
            print(f"创建Lunar实例成功: {lunar}")
        
        return True
        
    except ImportError:
        print("❌ lunar-python 库未安装")
        return False
    except Exception as e:
        print(f"❌ lunar-python 测试失败: {e}")
        return False

def test_chinese_calendar():
    """测试chinese-calendar库"""
    print("\n🔍 测试 chinese-calendar 库")
    print("-" * 40)
    
    try:
        import chinese_calendar as cc
        
        print("✅ chinese-calendar 库可用")
        
        # 测试基本功能
        test_date = date(1988, 3, 15)
        is_holiday = cc.is_holiday(test_date)
        is_workday = cc.is_workday(test_date)
        
        print(f"日期: {test_date}")
        print(f"是否节假日: {is_holiday}")
        print(f"是否工作日: {is_workday}")
        
        # 检查是否有农历或干支功能
        print("\n检查农历/干支功能:")
        lunar_attrs = ['lunar', 'ganzhi', 'to_lunar', 'get_lunar']
        
        for attr in lunar_attrs:
            if hasattr(cc, attr):
                print(f"  ✅ 有 {attr} 功能")
            else:
                print(f"  ❌ 无 {attr} 功能")
        
        return True
        
    except ImportError:
        print("❌ chinese-calendar 库未安装")
        return False
    except Exception as e:
        print(f"❌ chinese-calendar 测试失败: {e}")
        return False

def test_existing_libraries():
    """测试已安装的库"""
    print("\n🔍 重新测试已安装的库")
    print("-" * 40)
    
    # 测试LunarCalendar
    try:
        from LunarCalendar import Converter, Solar, Lunar
        
        print("✅ LunarCalendar 库可用")
        
        solar = Solar(1988, 3, 15)
        lunar = Converter.Solar2Lunar(solar)
        
        print(f"阳历: {solar.year}-{solar.month}-{solar.day}")
        print(f"农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查干支属性
        ganzhi_attrs = ['gz_year', 'gz_month', 'gz_day', 'ganzhi_year', 'ganzhi_month', 'ganzhi_day']
        
        print("\n检查干支属性:")
        for attr in ganzhi_attrs:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    print(f"  ✅ {attr}: {value}")
                except Exception as e:
                    print(f"  ❌ {attr}: 获取失败 - {e}")
            else:
                print(f"  ❌ {attr}: 不存在")
        
        # 显示所有属性
        print("\n所有可用属性:")
        for attr in dir(lunar):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        
        return True
        
    except ImportError:
        print("❌ LunarCalendar 库未安装")
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
    
    return False

def install_and_test_libraries():
    """尝试安装并测试各种库"""
    print("🚀 尝试安装和测试农历库")
    print("=" * 60)
    
    libraries_to_try = [
        'borax',
        'lunar-python', 
        'chinese-calendar',
        'pylunar',
        'lunar-calendar-python',
        'zhdate',
        'lunardate'
    ]
    
    for lib in libraries_to_try:
        print(f"\n📦 尝试安装 {lib}...")
        try:
            import subprocess
            result = subprocess.run(['pip', 'install', lib], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {lib} 安装成功")
            else:
                print(f"❌ {lib} 安装失败: {result.stderr}")
        except Exception as e:
            print(f"❌ {lib} 安装异常: {e}")

def main():
    """主测试函数"""
    print("🌙 农历干支库全面测试")
    print("=" * 60)
    
    # 首先测试已有的库
    test_existing_libraries()
    
    # 测试其他库
    test_borax_library()
    test_lunar_python()
    test_chinese_calendar()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print("正在寻找能够计算年月日柱的专业农历库...")

if __name__ == "__main__":
    main()

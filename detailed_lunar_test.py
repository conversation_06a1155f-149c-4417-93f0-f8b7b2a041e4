#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试LunarCalendar库的干支功能
专门寻找年月日柱计算方法
"""

from datetime import datetime, date

def test_lunarcalendar_ganzhi():
    """详细测试LunarCalendar库的干支功能"""
    print("🔍 详细测试 LunarCalendar 库的干支功能")
    print("=" * 60)
    
    try:
        from LunarCalendar import Converter, Solar, Lunar
        
        print("✅ LunarCalendar 库导入成功")
        
        # 测试标准日期
        test_dates = [
            (1988, 3, 15, "甲辰年测试 - 期望日柱:甲子"),
            (1990, 7, 22, "庚午年测试 - 期望日柱:丁卯"), 
            (2024, 1, 1, "2024年元旦 - 期望日柱:庚寅")
        ]
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {desc}")
            print(f"阳历: {year}-{month:02d}-{day:02d}")
            print("-" * 50)
            
            try:
                # 创建阳历对象
                solar = Solar(year, month, day)
                
                # 转换为农历
                lunar = Converter.Solar2Lunar(solar)
                
                print(f"农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
                
                # 检查所有属性和方法
                print("\n🔍 所有属性:")
                attributes = []
                for attr in dir(lunar):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(lunar, attr)
                            if not callable(value):
                                attributes.append((attr, value))
                                print(f"  {attr}: {value}")
                        except Exception as e:
                            print(f"  {attr}: 获取失败 - {e}")
                
                print("\n🔍 所有方法:")
                methods = []
                for attr in dir(lunar):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(lunar, attr)
                            if callable(value):
                                methods.append(attr)
                                print(f"  {attr}()")
                        except:
                            pass
                
                # 特别测试可能的干支方法
                print("\n🎯 测试可能的干支方法:")
                possible_ganzhi_methods = [
                    'gz_year', 'gz_month', 'gz_day', 'gz_hour',
                    'ganzhi_year', 'ganzhi_month', 'ganzhi_day', 'ganzhi_hour',
                    'year_ganzhi', 'month_ganzhi', 'day_ganzhi', 'hour_ganzhi',
                    'getYearGanZhi', 'getMonthGanZhi', 'getDayGanZhi', 'getHourGanZhi',
                    'yearGanZhi', 'monthGanZhi', 'dayGanZhi', 'hourGanZhi'
                ]
                
                found_ganzhi = False
                for method in possible_ganzhi_methods:
                    if hasattr(lunar, method):
                        try:
                            attr = getattr(lunar, method)
                            if callable(attr):
                                result = attr()
                                print(f"  ✅ {method}(): {result}")
                                found_ganzhi = True
                            else:
                                print(f"  ✅ {method}: {attr}")
                                found_ganzhi = True
                        except Exception as e:
                            print(f"  ❌ {method}: 调用失败 - {e}")
                
                if not found_ganzhi:
                    print("  ❌ 未找到干支相关方法")
                
                # 尝试通过Solar对象获取干支
                print("\n🔍 测试Solar对象的干支功能:")
                for attr in dir(solar):
                    if 'gz' in attr.lower() or 'ganzhi' in attr.lower():
                        try:
                            value = getattr(solar, attr)
                            if callable(value):
                                result = value()
                                print(f"  ✅ solar.{attr}(): {result}")
                            else:
                                print(f"  ✅ solar.{attr}: {value}")
                        except Exception as e:
                            print(f"  ❌ solar.{attr}: 失败 - {e}")
                
            except Exception as e:
                print(f"❌ 处理日期失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except ImportError as e:
        print(f"❌ LunarCalendar 库未安装: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_converter_methods():
    """测试Converter类的方法"""
    print("\n🔍 测试 Converter 类的方法")
    print("=" * 50)
    
    try:
        from LunarCalendar import Converter
        
        print("Converter 类的所有方法:")
        for attr in dir(Converter):
            if not attr.startswith('_'):
                print(f"  {attr}")
        
        # 尝试直接调用可能的干支方法
        test_date = (1988, 3, 15)
        print(f"\n测试日期: {test_date}")
        
        possible_methods = [
            'getGanZhi', 'getYearGanZhi', 'getMonthGanZhi', 'getDayGanZhi',
            'calculateGanZhi', 'ganZhi', 'ganzhi'
        ]
        
        for method in possible_methods:
            if hasattr(Converter, method):
                try:
                    func = getattr(Converter, method)
                    if callable(func):
                        # 尝试不同的参数组合
                        try:
                            result = func(*test_date)
                            print(f"  ✅ {method}({test_date}): {result}")
                        except:
                            try:
                                result = func(test_date[0], test_date[1], test_date[2])
                                print(f"  ✅ {method}(year, month, day): {result}")
                            except Exception as e:
                                print(f"  ❌ {method}: 调用失败 - {e}")
                except Exception as e:
                    print(f"  ❌ {method}: 获取失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Converter 测试失败: {e}")
        return False

def create_simple_converter():
    """创建简单的转换器脚本"""
    print("\n📝 创建简单转换器")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LunarCalendar 简单转换器
"""

from LunarCalendar import Converter, Solar

def convert_and_show_all(year, month, day):
    """转换并显示所有可用信息"""
    print(f"\\n🌟 转换日期: {year}-{month:02d}-{day:02d}")
    print("=" * 40)
    
    try:
        solar = Solar(year, month, day)
        lunar = Converter.Solar2Lunar(solar)
        
        print(f"农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        print("\\n所有属性:")
        for attr in sorted(dir(lunar)):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        
        print("\\n所有方法:")
        for attr in sorted(dir(lunar)):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if callable(value):
                        print(f"  {attr}()")
                except:
                    pass
        
        return lunar
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

if __name__ == "__main__":
    # 测试几个标准日期
    test_dates = [
        (1988, 3, 15),
        (1990, 7, 22),
        (2024, 1, 1)
    ]
    
    for date_tuple in test_dates:
        convert_and_show_all(*date_tuple)
'''
    
    try:
        with open('simple_lunar_converter.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✅ 简单转换器已创建: simple_lunar_converter.py")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🌙 LunarCalendar 库干支功能详细测试")
    print("=" * 70)
    
    # 测试库的干支功能
    if test_lunarcalendar_ganzhi():
        print("\n" + "="*70)
        
        # 测试Converter方法
        test_converter_methods()
        
        # 创建简单转换器
        create_simple_converter()
    
    print("\n🎯 测试完成！")
    print("如果找到了干支方法，将在上面显示。")
    print("如果没有找到，说明这个库可能不支持干支计算。")

if __name__ == "__main__":
    main()

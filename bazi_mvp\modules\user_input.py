#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户输入模块
处理用户输入的生辰八字信息，支持多轮对话和智能纠错
"""

import re
from datetime import datetime
from typing import Dict, Optional, Any, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.calendar_converter import CalendarConverter

class UserInputModule:
    def __init__(self):
        """初始化用户输入模块"""
        self.collected_data = {}
        self.validation_rules = self._init_validation_rules()
        self.time_patterns = self._init_time_patterns()
        self.calendar_converter = CalendarConverter()
    
    def _init_validation_rules(self) -> Dict:
        """初始化验证规则"""
        return {
            'year': {
                'pattern': r'^(19|20)\d{2}$',
                'range': (1900, 2030),
                'message': '请输入1900-2030年之间的年份，如：1990'
            },
            'month': {
                'pattern': r'^(1[0-2]|[1-9])$',
                'range': (1, 12),
                'message': '请输入1-12之间的月份，如：5'
            },
            'day': {
                'pattern': r'^(3[01]|[12][0-9]|[1-9])$',
                'range': (1, 31),
                'message': '请输入1-31之间的日期，如：15'
            },
            'hour': {
                'pattern': r'^(2[0-3]|1[0-9]|[0-9])$',
                'range': (0, 23),
                'message': '请输入0-23之间的小时，如：14'
            },
            'minute': {
                'pattern': r'^([0-5][0-9]|[0-9])$',
                'range': (0, 59),
                'message': '请输入0-59之间的分钟，如：30'
            }
        }
    
    def _init_time_patterns(self) -> Dict:
        """初始化时间表达模式"""
        return {
            '凌晨': {'range': (0, 6), 'default': 2, 'description': '凌晨0-6点'},
            '早上': {'range': (6, 9), 'default': 7, 'description': '早上6-9点'},
            '上午': {'range': (9, 12), 'default': 10, 'description': '上午9-12点'},
            '中午': {'range': (11, 13), 'default': 12, 'description': '中午11-13点'},
            '下午': {'range': (13, 18), 'default': 15, 'description': '下午13-18点'},
            '傍晚': {'range': (17, 19), 'default': 18, 'description': '傍晚17-19点'},
            '晚上': {'range': (19, 22), 'default': 20, 'description': '晚上19-22点'},
            '深夜': {'range': (22, 24), 'default': 23, 'description': '深夜22-24点'},
            '夜里': {'range': (22, 6), 'default': 1, 'description': '夜里22点-次日6点'},
            '半夜': {'range': (23, 3), 'default': 1, 'description': '半夜23点-次日3点'},
            '清晨': {'range': (5, 8), 'default': 6, 'description': '清晨5-8点'},
            '午后': {'range': (14, 17), 'default': 15, 'description': '午后14-17点'}
        }
    
    def collect_birth_info(self) -> Optional[Dict[str, Any]]:
        """收集生辰信息的主流程"""
        print("我将通过几个简单的问题来收集您的生辰信息。")
        print("如果某些信息不确定，我会提供相应的处理方案。\n")
        
        # 收集基本信息
        name = self._collect_name()
        gender = self._collect_gender()
        
        # 收集出生日期
        birth_date = self._collect_birth_date()
        if not birth_date:
            return None
        
        # 收集出生时间
        birth_time = self._collect_birth_time()
        
        # 收集出生地点
        birth_location = self._collect_birth_location()
        
        # 确认信息
        final_data = {
            'name': name,
            'gender': gender,
            'birth_location': birth_location,
            **birth_date,
            **birth_time
        }
        
        if self._confirm_information(final_data):
            return final_data
        else:
            print("信息确认失败，请重新开始。")
            return None
    
    def _collect_name(self) -> str:
        """收集姓名"""
        while True:
            name = input("请输入您的姓名（可选，直接回车跳过）: ").strip()
            if not name:
                return "未提供"
            if len(name) <= 20:
                return name
            print("姓名长度不能超过20个字符，请重新输入。")
    
    def _collect_gender(self) -> str:
        """收集性别"""
        while True:
            gender_input = input("请输入您的性别（男/女，可选）: ").strip()
            if not gender_input:
                return "未提供"
            if gender_input in ['男', '女', 'M', 'F', 'male', 'female']:
                return '男' if gender_input in ['男', 'M', 'male'] else '女'
            print("请输入'男'或'女'")
    
    def _collect_birth_date(self) -> Optional[Dict[str, int]]:
        """收集出生日期"""
        print("\n📅 收集出生日期信息")
        print("⚠️  请注意：请输入阳历（公历）日期，系统会自动转换为阴历供您确认")

        # 收集年份
        year = self._collect_with_validation('year', "请输入您的出生年份（阳历，如：1990）: ")
        if year is None:
            return None

        # 收集月份
        month = self._collect_with_validation('month', "请输入您的出生月份（阳历，如：5）: ")
        if month is None:
            return None

        # 收集日期
        day = self._collect_with_validation('day', "请输入您的出生日期（阳历，如：15）: ")
        if day is None:
            return None

        # 验证日期有效性
        try:
            datetime(year, month, day)

            # 转换为阴历并确认
            lunar_info = self.calendar_converter.solar_to_lunar(year, month, day)
            if lunar_info:
                print(f"\n🌙 阳历转阴历结果：")
                print(f"   阳历：{year}年{month}月{day}日")
                print(f"   阴历：{lunar_info['lunar_date_full']}")
                print(f"   转换精度：{lunar_info['conversion_accuracy']}")

                while True:
                    confirm = input("\n阴历日期是否正确？(y/n/s跳过): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '对']:
                        return {
                            'year': year, 'month': month, 'day': day,
                            'lunar_info': lunar_info
                        }
                    elif confirm in ['n', 'no', '否', '不对']:
                        print("请重新输入阳历日期")
                        return self._collect_birth_date()
                    elif confirm in ['s', 'skip', '跳过']:
                        return {'year': year, 'month': month, 'day': day}
                    else:
                        print("请输入 y(确认)、n(重新输入) 或 s(跳过)")
            else:
                print("⚠️  阴历转换失败，将使用阳历日期继续")
                return {'year': year, 'month': month, 'day': day}

        except ValueError:
            print(f"❌ 日期 {year}-{month}-{day} 不存在，请重新输入。")
            return self._collect_birth_date()
    
    def _collect_birth_time(self) -> Dict[str, Any]:
        """收集出生时间"""
        print("\n⏰ 收集出生时间信息")
        print("💡 支持多种输入格式：")
        print("   • 24小时制：14:30、2:15、23:45")
        print("   • 12小时制：下午2点30分、晚上8点、凌晨3点")
        print("   • 模糊时间：上午、下午、晚上、凌晨等")
        print("   • 时间区间：下午2-4点、晚上7-9点")
        print("   • 如果不确定，可以直接回车选择处理方案")

        while True:
            time_input = input("\n请输入您的出生时间: ").strip()

            if not time_input:
                return self._handle_unknown_time()

            # 尝试解析时间
            parsed_time = self._parse_time_input(time_input)
            if parsed_time:
                # 显示解析结果供确认
                self._display_time_parsing_result(parsed_time)

                while True:
                    confirm = input("时间解析是否正确？(y/n/r重新输入): ").strip().lower()
                    if confirm in ['y', 'yes', '是', '对']:
                        return parsed_time
                    elif confirm in ['n', 'no', '否', '不对']:
                        print("请重新输入时间")
                        break
                    elif confirm in ['r', 'retry', '重新输入']:
                        break
                    else:
                        print("请输入 y(确认)、n(不对) 或 r(重新输入)")

                if confirm in ['n', 'no', '否', '不对', 'r', 'retry', '重新输入']:
                    continue
            else:
                print("❌ 时间格式不正确，请重新输入。")
                self._show_time_input_examples()
    
    def _collect_birth_location(self) -> str:
        """收集出生地点"""
        print("\n📍 收集出生地点信息")
        location = input("请输入您的出生地点（如：北京市、上海市浦东新区）: ").strip()
        return location if location else "未提供"
    
    def _collect_with_validation(self, field: str, prompt: str, max_retries: int = 3) -> Optional[int]:
        """带验证的数据收集"""
        rule = self.validation_rules[field]
        
        for _ in range(max_retries):
            user_input = input(prompt).strip()
            
            if not user_input:
                print(f"❌ {field}不能为空")
                continue
            
            # 正则验证
            if not re.match(rule['pattern'], user_input):
                print(f"❌ {rule['message']}")
                continue
            
            # 范围验证
            value = int(user_input)
            if rule['range'][0] <= value <= rule['range'][1]:
                return value
            else:
                print(f"❌ {rule['message']}")
        
        print(f"❌ 输入{field}失败次数过多，程序退出")
        return None
    
    def _parse_time_input(self, time_input: str) -> Optional[Dict[str, Any]]:
        """解析时间输入"""
        # 标准化输入
        time_input = time_input.replace('：', ':').replace('点', ':').replace('时', ':')
        time_input = time_input.replace('分', '').replace('半', ':30')

        # 尝试解析时间区间（如：下午2-4点）
        range_result = self._parse_time_range(time_input)
        if range_result:
            return range_result

        # 尝试标准时间格式 HH:MM
        time_match = re.match(r'^(\d{1,2}):?(\d{0,2})$', time_input)
        if time_match:
            hour = int(time_match.group(1))
            minute = int(time_match.group(2)) if time_match.group(2) else 0

            if 0 <= hour <= 23 and 0 <= minute <= 59:
                return {
                    'hour': hour,
                    'minute': minute,
                    'time_certainty': 'exact',
                    'time_description': f"{hour}:{minute:02d}"
                }

        # 尝试12小时制格式（如：下午2:30）
        twelve_hour_result = self._parse_twelve_hour_format(time_input)
        if twelve_hour_result:
            return twelve_hour_result

        # 尝试模糊时间表达
        for pattern, time_info in self.time_patterns.items():
            if pattern in time_input:
                return {
                    'hour': time_info['default'],
                    'minute': 0,
                    'time_certainty': 'approximate',
                    'time_description': f"{pattern}（约{time_info['default']}点）",
                    'time_range': time_info['range']
                }

        return None

    def _parse_time_range(self, time_input: str) -> Optional[Dict[str, Any]]:
        """解析时间区间输入（如：下午2-4点）"""
        # 匹配时间区间模式
        range_patterns = [
            r'(\w+)(\d{1,2})-(\d{1,2})',  # 下午2-4
            r'(\d{1,2})-(\d{1,2})点',     # 2-4点
            r'(\w+)(\d{1,2})到(\d{1,2})', # 下午2到4
        ]

        for pattern in range_patterns:
            match = re.search(pattern, time_input)
            if match:
                if len(match.groups()) == 3:
                    period = match.group(1) if not match.group(1).isdigit() else ''
                    start_hour = int(match.group(2))
                    end_hour = int(match.group(3))
                else:
                    period = ''
                    start_hour = int(match.group(1))
                    end_hour = int(match.group(2))

                # 调整12小时制
                if period:
                    start_hour, end_hour = self._adjust_twelve_hour(start_hour, end_hour, period)

                if 0 <= start_hour <= 23 and 0 <= end_hour <= 23:
                    mid_hour = (start_hour + end_hour) // 2
                    return {
                        'hour': mid_hour,
                        'minute': 0,
                        'time_certainty': 'range',
                        'time_description': f"{period}{start_hour}-{end_hour}点（约{mid_hour}点）",
                        'time_range': (start_hour, end_hour)
                    }

        return None

    def _parse_twelve_hour_format(self, time_input: str) -> Optional[Dict[str, Any]]:
        """解析12小时制格式"""
        # 匹配模式：下午2:30、晚上8点等
        patterns = [
            r'(\w+)(\d{1,2}):(\d{1,2})',  # 下午2:30
            r'(\w+)(\d{1,2})',            # 下午2
        ]

        for pattern in patterns:
            match = re.search(pattern, time_input)
            if match:
                period = match.group(1)
                hour = int(match.group(2))
                minute = int(match.group(3)) if len(match.groups()) >= 3 and match.group(3) else 0

                # 根据时间段调整小时
                adjusted_hour = self._convert_to_24_hour(hour, period)

                if adjusted_hour is not None and 0 <= minute <= 59:
                    return {
                        'hour': adjusted_hour,
                        'minute': minute,
                        'time_certainty': 'approximate',
                        'time_description': f"{period}{hour}:{minute:02d}（{adjusted_hour}:{minute:02d}）"
                    }

        return None

    def _adjust_twelve_hour(self, start_hour: int, end_hour: int, period: str) -> Tuple[int, int]:
        """调整12小时制的时间区间"""
        if period in ['下午', '晚上', '傍晚'] and start_hour < 12:
            start_hour += 12
        if period in ['下午', '晚上', '傍晚'] and end_hour < 12:
            end_hour += 12

        return start_hour, end_hour

    def _convert_to_24_hour(self, hour: int, period: str) -> Optional[int]:
        """将12小时制转换为24小时制"""
        if period in ['凌晨', '早上', '上午']:
            return hour if hour < 12 else hour
        elif period in ['中午']:
            return 12 if hour == 12 else hour
        elif period in ['下午', '傍晚', '晚上']:
            return hour + 12 if hour < 12 else hour
        elif period in ['深夜', '半夜']:
            return hour if hour > 12 else hour + 12

        return None

    def _display_time_parsing_result(self, parsed_time: Dict[str, Any]):
        """显示时间解析结果"""
        print(f"\n✅ 时间解析结果：")
        print(f"   解析时间：{parsed_time.get('time_description', '未知')}")
        if parsed_time.get('hour') is not None:
            print(f"   24小时制：{parsed_time['hour']}:{parsed_time['minute']:02d}")
        print(f"   时间精度：{parsed_time.get('time_certainty', '未知')}")
        if 'time_range' in parsed_time:
            time_range = parsed_time['time_range']
            print(f"   时间范围：{time_range[0]}:00-{time_range[1]}:00")

    def _show_time_input_examples(self):
        """显示时间输入示例"""
        print("\n💡 时间输入示例：")
        print("   24小时制：14:30、2:15、23:45")
        print("   模糊时间：上午、下午、晚上、凌晨")
        print("   具体描述：下午2点30分、晚上8点、凌晨3点")
        print("   时间区间：下午2-4点、晚上7-9点")

    def _handle_unknown_time(self) -> Dict[str, Any]:
        """处理不知道出生时间的情况"""
        print("\n⚠️  您没有提供出生时间信息")
        print("这会影响分析的准确性，我们提供以下处理方案：")
        print("1. 使用中午12点作为默认时间（准确度较低）")
        print("2. 分析所有可能的时辰（提供范围分析）")
        print("3. 退出程序，咨询家人后再来")
        
        while True:
            choice = input("请选择处理方案（1/2/3）: ").strip()
            
            if choice == '1':
                return {
                    'hour': 12,
                    'minute': 0,
                    'time_certainty': 'default',
                    'time_description': '默认中午时间'
                }
            elif choice == '2':
                return {
                    'hour': None,
                    'minute': None,
                    'time_certainty': 'unknown',
                    'time_description': '时间未知，需要范围分析'
                }
            elif choice == '3':
                return None
            else:
                print("请输入1、2或3")
    
    def _confirm_information(self, data: Dict[str, Any]) -> bool:
        """确认收集的信息"""
        print("\n" + "=" * 50)
        print("📋 请确认您的生辰信息：")
        print("=" * 50)

        print(f"👤 姓名: {data['name']}")
        print(f"⚧  性别: {data['gender']}")

        # 显示阳历日期
        print(f"📅 阳历生日: {data['year']}年{data['month']}月{data['day']}日")

        # 显示阴历日期（如果有）
        if 'lunar_info' in data and data['lunar_info']:
            lunar_info = data['lunar_info']
            print(f"🌙 阴历生日: {lunar_info['lunar_date_full']}")
            print(f"   转换精度: {lunar_info['conversion_accuracy']}")

        # 显示出生时间
        if data.get('hour') is not None:
            print(f"⏰ 出生时间: {data['hour']}:{data['minute']:02d}")
            if 'time_description' in data:
                print(f"   时间描述: {data['time_description']}")
        else:
            print(f"⏰ 出生时间: {data.get('time_description', '未知')}")

        print(f"📍 出生地点: {data['birth_location']}")
        print(f"🎯 时间精度: {data.get('time_certainty', 'unknown')}")

        # 显示时间范围（如果有）
        if 'time_range' in data:
            time_range = data['time_range']
            print(f"   时间范围: {time_range[0]}:00-{time_range[1]}:00")

        print("=" * 50)

        while True:
            confirm = input("以上信息是否正确？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '对']:
                return True
            elif confirm in ['n', 'no', '否', '不对']:
                return False
            else:
                print("请输入 y(确认) 或 n(重新输入)")

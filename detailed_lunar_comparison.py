#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的农历库对比测试
对比不同农历库的功能和输出差异
"""

from datetime import datetime, date
import sys

def test_library_features():
    """测试各个库的功能特性"""
    print("🌟 农历库功能特性对比")
    print("=" * 60)
    
    # 测试日期
    test_dates = [
        (1988, 3, 15, "甲辰年 - 戊辰月 - 甲子日"),
        (1990, 7, 22, "庚午年 - 癸未月 - 丁卯日"),
        (2024, 1, 1, "甲辰年 - 乙丑月 - 庚寅日"),
        (2024, 2, 10, "甲辰年春节"),
        (2024, 6, 10, "甲辰年端午"),
    ]
    
    print("📅 测试日期列表:")
    for year, month, day, desc in test_dates:
        print(f"  {year}-{month:02d}-{day:02d} ({desc})")
    
    print("\n" + "=" * 60)
    
    # 1. 测试 zhdate
    print("1️⃣ zhdate 库测试:")
    print("-" * 40)
    
    try:
        import zhdate
        print("✅ zhdate 库可用")
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {year}-{month:02d}-{day:02d} ({desc}):")
            try:
                dt = datetime(year, month, day)
                zh_date = zhdate.ZhDate.from_datetime(dt)
                
                print(f"  农历: {zh_date}")
                print(f"  详细: 农历{zh_date.lunar_year}年{zh_date.lunar_month}月{zh_date.lunar_day}日")
                
                # 尝试获取更多信息
                try:
                    print(f"  年干支: {zh_date.gz_year()}")
                    print(f"  月干支: {zh_date.gz_month()}")
                    print(f"  日干支: {zh_date.gz_day()}")
                except AttributeError as e:
                    print(f"  干支信息: 方法不存在 - {e}")
                except Exception as e:
                    print(f"  干支信息: 获取失败 - {e}")
                
                # 检查其他属性
                attrs = ['lunar_year', 'lunar_month', 'lunar_day', 'is_leap']
                for attr in attrs:
                    if hasattr(zh_date, attr):
                        print(f"  {attr}: {getattr(zh_date, attr)}")
                
            except Exception as e:
                print(f"  ❌ 转换失败: {e}")
        
    except ImportError:
        print("❌ zhdate 库未安装")
    except Exception as e:
        print(f"❌ zhdate 库错误: {e}")
    
    print("\n" + "=" * 60)
    
    # 2. 测试 lunardate
    print("2️⃣ lunardate 库测试:")
    print("-" * 40)
    
    try:
        from lunardate import LunarDate
        print("✅ lunardate 库可用")
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {year}-{month:02d}-{day:02d} ({desc}):")
            try:
                lunar_date = LunarDate.fromSolarDate(year, month, day)
                
                print(f"  农历: 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
                
                # 尝试获取干支信息
                try:
                    print(f"  年干支: {lunar_date.gz_year()}")
                    print(f"  月干支: {lunar_date.gz_month()}")
                    print(f"  日干支: {lunar_date.gz_day()}")
                except AttributeError as e:
                    print(f"  干支信息: 方法不存在 - {e}")
                except Exception as e:
                    print(f"  干支信息: 获取失败 - {e}")
                
                # 检查其他属性和方法
                attrs = ['year', 'month', 'day', 'isLeapMonth']
                for attr in attrs:
                    if hasattr(lunar_date, attr):
                        value = getattr(lunar_date, attr)
                        if callable(value):
                            try:
                                value = value()
                            except:
                                value = "调用失败"
                        print(f"  {attr}: {value}")
                
                # 尝试转回阳历
                try:
                    solar_date = lunar_date.toSolarDate()
                    print(f"  转回阳历: {solar_date}")
                except Exception as e:
                    print(f"  转回阳历失败: {e}")
                
            except Exception as e:
                print(f"  ❌ 转换失败: {e}")
        
    except ImportError:
        print("❌ lunardate 库未安装")
    except Exception as e:
        print(f"❌ lunardate 库错误: {e}")
    
    print("\n" + "=" * 60)
    
    # 3. 测试 LunarCalendar
    print("3️⃣ LunarCalendar 库测试:")
    print("-" * 40)
    
    try:
        from LunarCalendar import Converter, Solar, Lunar
        print("✅ LunarCalendar 库可用")
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {year}-{month:02d}-{day:02d} ({desc}):")
            try:
                solar = Solar(year, month, day)
                lunar = Converter.Solar2Lunar(solar)
                
                print(f"  农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
                
                # 尝试获取干支信息
                gz_attrs = ['gz_year', 'gz_month', 'gz_day']
                for attr in gz_attrs:
                    if hasattr(lunar, attr):
                        print(f"  {attr}: {getattr(lunar, attr)}")
                
                # 检查其他属性
                other_attrs = ['year', 'month', 'day', 'isLeapMonth']
                for attr in other_attrs:
                    if hasattr(lunar, attr):
                        print(f"  {attr}: {getattr(lunar, attr)}")
                
                # 尝试转回阳历
                try:
                    solar_back = Converter.Lunar2Solar(lunar)
                    print(f"  转回阳历: {solar_back.year}-{solar_back.month:02d}-{solar_back.day:02d}")
                except Exception as e:
                    print(f"  转回阳历失败: {e}")
                
            except Exception as e:
                print(f"  ❌ 转换失败: {e}")
        
    except ImportError:
        print("❌ LunarCalendar 库未安装")
    except Exception as e:
        print(f"❌ LunarCalendar 库错误: {e}")
    
    print("\n" + "=" * 60)
    
    # 4. 测试 chinese-calendar
    print("4️⃣ chinese-calendar 库测试:")
    print("-" * 40)
    
    try:
        import chinese_calendar as cc
        print("✅ chinese-calendar 库可用")
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {year}-{month:02d}-{day:02d} ({desc}):")
            try:
                test_date = date(year, month, day)
                
                is_holiday = cc.is_holiday(test_date)
                is_workday = cc.is_workday(test_date)
                
                print(f"  节假日: {'是' if is_holiday else '否'}")
                print(f"  工作日: {'是' if is_workday else '否'}")
                
                # 尝试获取节假日名称
                try:
                    holiday_name = cc.get_holiday_detail(test_date)
                    if holiday_name:
                        print(f"  节日名称: {holiday_name}")
                except:
                    pass
                
            except Exception as e:
                print(f"  ❌ 查询失败: {e}")
        
    except ImportError:
        print("❌ chinese-calendar 库未安装")
    except Exception as e:
        print(f"❌ chinese-calendar 库错误: {e}")

def compare_accuracy():
    """对比不同库的准确性"""
    print("\n🎯 准确性对比测试")
    print("=" * 60)
    
    # 已知的标准答案（可以通过权威农历网站验证）
    standard_cases = [
        {
            'solar': (1988, 3, 15),
            'expected_lunar': (1988, 1, 28),
            'expected_gz_day': '甲子',
            'description': '甲辰年正月廿八'
        },
        {
            'solar': (1990, 7, 22),
            'expected_lunar': (1990, 6, 1),
            'expected_gz_day': '丁卯',
            'description': '庚午年六月初一'
        },
        {
            'solar': (2024, 1, 1),
            'expected_lunar': (2023, 11, 20),
            'expected_gz_day': '庚寅',
            'description': '癸卯年十一月二十'
        }
    ]
    
    libraries = ['zhdate', 'lunardate', 'LunarCalendar']
    
    for case in standard_cases:
        solar_date = case['solar']
        expected_lunar = case['expected_lunar']
        expected_gz_day = case['expected_gz_day']
        desc = case['description']
        
        print(f"\n📅 测试案例: {solar_date[0]}-{solar_date[1]:02d}-{solar_date[2]:02d}")
        print(f"   期望结果: {desc} (日柱: {expected_gz_day})")
        print("   " + "-" * 50)
        
        for lib_name in libraries:
            try:
                if lib_name == 'zhdate':
                    import zhdate
                    dt = datetime(*solar_date)
                    zh_date = zhdate.ZhDate.from_datetime(dt)
                    actual_lunar = (zh_date.lunar_year, zh_date.lunar_month, zh_date.lunar_day)
                    try:
                        actual_gz_day = zh_date.gz_day()
                    except:
                        actual_gz_day = "获取失败"
                    
                elif lib_name == 'lunardate':
                    from lunardate import LunarDate
                    lunar_date = LunarDate.fromSolarDate(*solar_date)
                    actual_lunar = (lunar_date.year, lunar_date.month, lunar_date.day)
                    try:
                        actual_gz_day = lunar_date.gz_day()
                    except:
                        actual_gz_day = "获取失败"
                    
                elif lib_name == 'LunarCalendar':
                    from LunarCalendar import Converter, Solar
                    solar = Solar(*solar_date)
                    lunar = Converter.Solar2Lunar(solar)
                    actual_lunar = (lunar.year, lunar.month, lunar.day)
                    try:
                        actual_gz_day = lunar.gz_day
                    except:
                        actual_gz_day = "获取失败"
                
                # 检查准确性
                lunar_correct = actual_lunar == expected_lunar
                gz_correct = actual_gz_day == expected_gz_day
                
                print(f"   {lib_name:15}: 农历{actual_lunar[0]}年{actual_lunar[1]}月{actual_lunar[2]}日 日柱:{actual_gz_day}")
                print(f"   {'':15}  农历{'✅' if lunar_correct else '❌'} 日柱{'✅' if gz_correct else '❌'}")
                
            except ImportError:
                print(f"   {lib_name:15}: 库未安装")
            except Exception as e:
                print(f"   {lib_name:15}: 错误 - {e}")

def main():
    """主函数"""
    print("🌙 农历库详细对比测试")
    print("=" * 60)
    
    # 功能特性测试
    test_library_features()
    
    # 准确性对比
    compare_accuracy()
    
    # 总结建议
    print("\n" + "=" * 60)
    print("📊 总结和建议:")
    print("=" * 60)
    
    print("🎯 功能对比:")
    print("  • zhdate: 简单易用，基础农历转换")
    print("  • lunardate: 功能较全，支持干支计算")
    print("  • LunarCalendar: 功能最全，支持节气、节日等")
    print("  • chinese-calendar: 专注节假日查询")
    
    print("\n💡 使用建议:")
    print("  • 八字计算: 推荐 lunardate 或 LunarCalendar")
    print("  • 简单转换: 推荐 zhdate")
    print("  • 节假日查询: 推荐 chinese-calendar")
    print("  • 全功能需求: 推荐 LunarCalendar")

if __name__ == "__main__":
    main()

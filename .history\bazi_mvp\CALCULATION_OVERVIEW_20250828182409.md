# 生辰八字与合婚计算逻辑总览

## 1. 总览
- **单人八字计算**：输入标准化 → 真太阳时/时间精度处理 → 四柱排盘 → 五行分析 → 十神分析 → 格局判断 → 大运（简化） → 精度与元数据。
- **合婚计算**：分别计算双方八字要素 → 四柱配对（天干/地支关系打分）→ 五行互补与平衡 → 日柱专项配对（加权）→ 十神配对 → 综合加权评分与建议。

---

## 2. 单人八字计算流程（核心逻辑）

### 2.1 输入标准化与时间处理（`DataProcessor.process`）
- 基本信息：姓名、性别、阳历生日、出生地。
- 时间精度：
  - 确切时间 → `birth_datetime`；
  - 模糊/区间 → 取中位近似（标记 `time_certainty=approximate`）；
  - 完全未知 → `time_analysis_needed=True`。
- 真太阳时（简化估算）：城市经度估算 + 简化均时差 → `solar_datetime`、`solar_hour`。
- 处理元数据：`data_quality` 等级、`warnings` 列表。

### 2.2 计算分支判断（`BaziCalculator.calculate`）
- `time_analysis_needed=True` → 进入“12时辰范围分析”。
- 否则进入“标准八字分析”。

### 2.3 标准八字分析（`_calculate_normal_bazi`）
- 使用时间：优先 `solar_datetime`，否则 `birth_datetime`。
- 四柱排盘：
  - 年柱（`_get_year_pillar`）：以1984甲子为基准的60甲子循环（简化）。
  - 月柱（`_get_month_pillar`）：按年干起月干、寅月起地支（未精确节气切换，简化）。
  - 日柱（`_get_day_pillar`）：以2000-01-01为甲辰日基准的循环（简化）。
  - 时柱（`_get_hour_pillar`）：依据日干起甲子，按时辰映射。
- 五行分析（`_analyze_wuxing`）：统计天干/地支五行数量；确定日主五行；强弱评分与等级；用神/忌神（简化规则）。
- 十神分析（`_analyze_shishen`）：以日干为参照，映射三柱天干的十神分布与主导十神。
- 格局判断（`_analyze_pattern`，简化）：依据月干对应十神粗分（官杀/财/食伤/印/比劫）。
- 大运（`_calculate_dayun`，简化）：男8/女7起运；给出若干步大运的时间段与描述（未排真干支）。
- 计算元数据：时间来源（solar/standard）、精度等级（高/中/标准/时间范围）。

### 2.4 出生时间未知：范围分析（`_calculate_time_range_analysis`）
- 以生日+12个代表时（1,3,5,...,23点）分别做标准分析。
- 提取每个时辰“关键特征”（日主五行+强弱、格局），汇总共同特征。
- 输出“推荐时间”（简化规则）与各时辰概览、元数据。

### 2.5 输出装配（供理解）
- 语料匹配（`CorpusMatcher.match`）：基于五行/十神/格局映射性格、事业、感情、健康与建议，并做现代化术语替换。
- 报告生成（`OutputGenerator.generate`）：报告装配、质量检查与自动补强、概要评分与摘要展示。

---

## 3. 合婚计算流程（核心逻辑）

### 3.1 数据准备（`main.py` 调用）
- 对男女双方分别执行“单人八字”处理与计算。
- 调用 `_extract_bazi_for_compatibility` 提炼字段：
  - `year/month/day/hour_pillar`（各为干支2字）、`day_master_element`、`main_shishen`、`name`。

### 3.2 四柱配对（`CoupleCompatibilityCalculator._analyze_pillars`）
- 年/月/日/时各柱：
  - 天干关系（`_analyze_tiangan_relation`）：优先合化配对（甲己、乙庚、丙辛、丁壬、戊癸）；否则按五行关系（同类/我生/生我/我克/克我/中性）评分。
  - 地支关系（`_analyze_dizhi_relation`）：优先六合/三合/相冲/相害；否则按五行关系评分。
  - 柱分 = 干支关系分均值；柱权重：年0.2、月0.25、日0.35、时0.2。

### 3.3 五行配合（`_analyze_wuxing_compatibility`）
- 提取双方四柱五行分布。
- 互补度：一方缺失某行而另一方具备加分；数量接近加分（简化）。
- 主五行关系：双方日主五行的生克关系评分。
- 平衡度：合并分布评估偏旺/偏弱，得到平衡分、主导/薄弱五行。

### 3.4 日柱专项配对（`_analyze_day_pillar`）
- 对日干/日支分别打分；
- 特殊规则（`_check_day_pillar_special_rules`）：同日柱高分；简化纳音五行关系；
- 最终日柱分 = 干支加权分与特殊分的综合，并生成文字分析。

### 3.5 十神配对（`_analyze_shishen_compatibility`，简化）
- 以双方主导十神组合查表评分与说明（示例性规则）。

### 3.6 综合评分与等级
- 加权汇总（`_calculate_overall_score`）：
  - 四柱权重（见3.2），
  - 五行配合均分×0.25，
  - 日柱专项×0.35，
  - 十神×0.15。
- 评分档位（`_get_compatibility_level`）：90+天作之合、80+非常匹配、70+比较匹配、60+一般、50+需磨合、否则不太匹配。
- 输出建议与详细说明（分档建议 + 五行关系定制化 + 四柱与五行详细分析）。

---

## 4. 关键输入/输出结构（简要）
- 单人输入（标准化后）：`birth_datetime/solar_datetime`、`time_certainty`、`birth_location`、`gender`、`lunar_info?`。
- 单人输出（计算核心）：`bazi_pillars`、`wuxing_analysis`、`shishen_analysis`、`pattern_analysis`、`dayun_analysis`、`calculation_metadata`；或 `time_range` 分析结构。
- 合婚输入（提要）：双方四柱干支、日主五行、主导十神、姓名。
- 合婚输出（计算核心）：`overall_score`、`compatibility_level`、`pillar_analysis`、`wuxing_analysis`、`day_pillar_analysis`、`shishen_analysis`、`suggestions`、`detailed_analysis`。

---

## 5. 精度与已知简化
- 月柱未严格按节气交接时刻切换；日柱基准法简化；大运为示意性。
- 真太阳时为估算模型；阴历转换存在查表/近似，输出带 `conversion_accuracy` 标注。
- 通过 `processing_metadata.data_quality` 与 `calculation_metadata.accuracy_level` 进行透明化精度说明。 
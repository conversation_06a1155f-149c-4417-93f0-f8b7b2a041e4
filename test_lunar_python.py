#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试lunar_python库的干支功能
这个库专门支持年月日柱计算
"""

from datetime import datetime

def test_lunar_python_ganzhi():
    """测试lunar_python库的干支功能"""
    print("🔍 测试 lunar_python 库的干支功能")
    print("=" * 60)
    
    try:
        from lunar_python import Lunar, Solar
        
        print("✅ lunar_python 库导入成功")
        
        # 测试标准日期
        test_dates = [
            (1988, 3, 15, "甲辰年测试 - 期望日柱:甲子"),
            (1990, 7, 22, "庚午年测试 - 期望日柱:丁卯"), 
            (2024, 1, 1, "2024年元旦 - 期望日柱:庚寅")
        ]
        
        for year, month, day, desc in test_dates:
            print(f"\n📅 {desc}")
            print(f"阳历: {year}-{month:02d}-{day:02d}")
            print("-" * 50)
            
            try:
                # 方法1: 通过阳历创建
                solar = Solar.fromYmd(year, month, day)
                lunar = solar.getLunar()
                
                print(f"阳历对象: {solar.toFullString()}")
                print(f"农历对象: {lunar.toFullString()}")
                
                # 获取八字信息
                print(f"\n🎯 八字信息:")
                eightChar = lunar.getEightChar()
                print(f"八字对象: {eightChar}")
                
                # 获取年月日时柱
                print(f"年柱: {eightChar.getYear()}")
                print(f"月柱: {eightChar.getMonth()}")
                print(f"日柱: {eightChar.getDay()}")
                print(f"时柱: {eightChar.getTime()}")
                
                # 获取干支详细信息
                print(f"\n🔍 干支详细:")
                print(f"年干支: {eightChar.getYearGanZhi()}")
                print(f"月干支: {eightChar.getMonthGanZhi()}")
                print(f"日干支: {eightChar.getDayGanZhi()}")
                print(f"时干支: {eightChar.getTimeGanZhi()}")
                
                # 获取五行信息
                print(f"\n🌟 五行信息:")
                print(f"年柱五行: {eightChar.getYearWuXing()}")
                print(f"月柱五行: {eightChar.getMonthWuXing()}")
                print(f"日柱五行: {eightChar.getDayWuXing()}")
                print(f"时柱五行: {eightChar.getTimeWuXing()}")
                
                # 获取十神信息
                print(f"\n⭐ 十神信息:")
                print(f"年柱十神: {eightChar.getYearShiShenGan()}")
                print(f"月柱十神: {eightChar.getMonthShiShenGan()}")
                print(f"日柱十神: {eightChar.getDayShiShenGan()}")
                print(f"时柱十神: {eightChar.getTimeShiShenGan()}")
                
            except Exception as e:
                print(f"❌ 处理日期失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except ImportError as e:
        print(f"❌ lunar_python 库未安装: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lunar_methods():
    """测试Lunar对象的所有方法"""
    print("\n🔍 测试 Lunar 对象的所有方法")
    print("=" * 50)
    
    try:
        from lunar_python import Solar
        
        # 创建测试对象
        solar = Solar.fromYmd(1988, 3, 15)
        lunar = solar.getLunar()
        
        print("Lunar对象的所有方法:")
        methods = [attr for attr in dir(lunar) if not attr.startswith('_') and callable(getattr(lunar, attr))]
        
        for method in sorted(methods):
            print(f"  {method}()")
        
        print("\n测试一些重要方法:")
        
        # 测试干支相关方法
        ganzhi_methods = [
            'getYearGanZhi', 'getMonthGanZhi', 'getDayGanZhi', 'getTimeGanZhi',
            'getYearGan', 'getYearZhi', 'getMonthGan', 'getMonthZhi',
            'getDayGan', 'getDayZhi', 'getTimeGan', 'getTimeZhi'
        ]
        
        for method in ganzhi_methods:
            if hasattr(lunar, method):
                try:
                    func = getattr(lunar, method)
                    result = func()
                    print(f"  ✅ {method}(): {result}")
                except Exception as e:
                    print(f"  ❌ {method}(): 失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_lunar_converter():
    """创建基于lunar_python的转换器"""
    print("\n📝 创建lunar_python转换器")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于lunar_python库的农历干支转换器
支持完整的年月日柱计算
"""

from lunar_python import Solar

def convert_date_with_ganzhi(year, month, day, hour=12):
    """转换日期并显示完整的干支信息"""
    print(f"\\n🌟 阳历日期: {year}年{month}月{day}日 {hour}时")
    print("=" * 60)
    
    try:
        # 创建阳历对象
        solar = Solar.fromYmd(year, month, day)
        
        # 转换为农历
        lunar = solar.getLunar()
        
        print(f"📅 农历: {lunar.toString()}")
        print(f"详细: {lunar.toFullString()}")
        
        # 获取八字
        eightChar = lunar.getEightChar()
        
        print(f"\\n🎯 四柱八字:")
        print(f"年柱: {eightChar.getYear()} ({eightChar.getYearGanZhi()})")
        print(f"月柱: {eightChar.getMonth()} ({eightChar.getMonthGanZhi()})")
        print(f"日柱: {eightChar.getDay()} ({eightChar.getDayGanZhi()})")
        print(f"时柱: {eightChar.getTime()} ({eightChar.getTimeGanZhi()})")
        
        print(f"\\n🌟 五行:")
        print(f"年柱五行: {eightChar.getYearWuXing()}")
        print(f"月柱五行: {eightChar.getMonthWuXing()}")
        print(f"日柱五行: {eightChar.getDayWuXing()}")
        print(f"时柱五行: {eightChar.getTimeWuXing()}")
        
        print(f"\\n⭐ 十神:")
        print(f"年柱十神: {eightChar.getYearShiShenGan()}")
        print(f"月柱十神: {eightChar.getMonthShiShenGan()}")
        print(f"日柱十神: {eightChar.getDayShiShenGan()}")
        print(f"时柱十神: {eightChar.getTimeShiShenGan()}")
        
        # 返回关键信息
        return {
            'lunar_date': lunar.toString(),
            'year_pillar': eightChar.getYearGanZhi(),
            'month_pillar': eightChar.getMonthGanZhi(),
            'day_pillar': eightChar.getDayGanZhi(),
            'time_pillar': eightChar.getTimeGanZhi(),
            'year_wuxing': eightChar.getYearWuXing(),
            'month_wuxing': eightChar.getMonthWuXing(),
            'day_wuxing': eightChar.getDayWuXing(),
            'time_wuxing': eightChar.getTimeWuXing()
        }
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

def main():
    """主函数"""
    print("🌙 lunar_python 农历干支转换器")
    print("=" * 50)
    print("支持完整的年月日时柱计算")
    print()
    
    # 演示标准案例
    demo_cases = [
        (1988, 3, 15, "甲辰年测试"),
        (1990, 7, 22, "庚午年测试"),
        (2024, 1, 1, "2024年元旦")
    ]
    
    for year, month, day, desc in demo_cases:
        print(f"\\n📅 {desc}")
        convert_date_with_ganzhi(year, month, day)
        print("\\n" + "="*60)
    
    # 交互模式
    while True:
        try:
            user_input = input("\\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            date_parts = user_input.split('-')
            if len(date_parts) != 3:
                print("❌ 格式错误，请使用 YYYY-MM-DD")
                continue
            
            year, month, day = map(int, date_parts)
            
            # 询问时辰
            hour_input = input("请输入时辰 (0-23，默认12): ").strip()
            hour = int(hour_input) if hour_input else 12
            
            convert_date_with_ganzhi(year, month, day, hour)
            
        except ValueError:
            print("❌ 日期格式错误")
        except KeyboardInterrupt:
            print("\\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('lunar_python_converter.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✅ lunar_python转换器已创建: lunar_python_converter.py")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🌙 lunar_python 库完整测试")
    print("=" * 70)
    
    # 测试库的干支功能
    if test_lunar_python_ganzhi():
        print("\n" + "="*70)
        
        # 测试所有方法
        test_lunar_methods()
        
        # 创建转换器
        create_lunar_converter()
    
    print("\n🎯 测试完成！")
    print("如果成功，lunar_python库应该能提供完整的年月日柱计算功能。")

if __name__ == "__main__":
    main()

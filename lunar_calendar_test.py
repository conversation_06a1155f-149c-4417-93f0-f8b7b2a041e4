#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历库对比测试
测试不同农历库的功能和输出
"""

import subprocess
import sys
from datetime import datetime, date

def install_package(package_name):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def test_lunar_calendar():
    """测试 LunarCalendar 库"""
    print("\n🌙 测试 LunarCalendar 库")
    print("=" * 50)
    
    try:
        from LunarCalendar import Converter, Solar, Lunar
        
        # 测试日期
        test_dates = [
            (1988, 3, 15),  # 甲辰年
            (1990, 7, 22),  # 庚午年
            (2024, 1, 1),   # 甲辰年
            (2024, 12, 31), # 甲辰年
        ]
        
        print("阳历 → 农历转换：")
        for year, month, day in test_dates:
            try:
                solar = Solar(year, month, day)
                lunar = Converter.Solar2Lunar(solar)
                
                print(f"  {year}-{month:02d}-{day:02d} → ", end="")
                print(f"农历{lunar.year}年{lunar.month}月{lunar.day}日")
                
                # 获取天干地支
                if hasattr(lunar, 'gz_year'):
                    print(f"    天干地支: {lunar.gz_year}")
                
            except Exception as e:
                print(f"  {year}-{month:02d}-{day:02d} → 转换失败: {e}")
        
        print("\n农历 → 阳历转换：")
        lunar_dates = [
            (2024, 1, 1),   # 农历正月初一
            (2024, 5, 5),   # 农历五月初五（端午）
            (2024, 8, 15),  # 农历八月十五（中秋）
        ]
        
        for year, month, day in lunar_dates:
            try:
                lunar = Lunar(year, month, day)
                solar = Converter.Lunar2Solar(lunar)
                
                print(f"  农历{year}年{month}月{day}日 → ", end="")
                print(f"{solar.year}-{solar.month:02d}-{solar.day:02d}")
                
            except Exception as e:
                print(f"  农历{year}年{month}月{day}日 → 转换失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ LunarCalendar 库未安装或导入失败")
        return False
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
        return False

def test_lunardate():
    """测试 lunardate 库"""
    print("\n🌙 测试 lunardate 库")
    print("=" * 50)
    
    try:
        from lunardate import LunarDate
        
        # 测试日期
        test_dates = [
            (1988, 3, 15),  # 甲辰年
            (1990, 7, 22),  # 庚午年
            (2024, 1, 1),   # 甲辰年
            (2024, 12, 31), # 甲辰年
        ]
        
        print("阳历 → 农历转换：")
        for year, month, day in test_dates:
            try:
                solar_date = date(year, month, day)
                lunar_date = LunarDate.fromSolarDate(year, month, day)
                
                print(f"  {year}-{month:02d}-{day:02d} → ", end="")
                print(f"农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
                
                # 获取更多信息
                if hasattr(lunar_date, 'gz_year'):
                    print(f"    年干支: {lunar_date.gz_year()}")
                if hasattr(lunar_date, 'gz_month'):
                    print(f"    月干支: {lunar_date.gz_month()}")
                if hasattr(lunar_date, 'gz_day'):
                    print(f"    日干支: {lunar_date.gz_day()}")
                
            except Exception as e:
                print(f"  {year}-{month:02d}-{day:02d} → 转换失败: {e}")
        
        print("\n农历 → 阳历转换：")
        lunar_dates = [
            (2024, 1, 1),   # 农历正月初一
            (2024, 5, 5),   # 农历五月初五（端午）
            (2024, 8, 15),  # 农历八月十五（中秋）
        ]
        
        for year, month, day in lunar_dates:
            try:
                lunar_date = LunarDate(year, month, day)
                solar_date = lunar_date.toSolarDate()
                
                print(f"  农历{year}年{month}月{day}日 → ", end="")
                print(f"{solar_date.year}-{solar_date.month:02d}-{solar_date.day:02d}")
                
            except Exception as e:
                print(f"  农历{year}年{month}月{day}日 → 转换失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ lunardate 库未安装或导入失败")
        return False
    except Exception as e:
        print(f"❌ lunardate 测试失败: {e}")
        return False

def test_chinese_calendar():
    """测试 chinese-calendar 库（如果可用）"""
    print("\n🌙 测试 chinese-calendar 库")
    print("=" * 50)
    
    try:
        import chinese_calendar as cc
        
        # 测试日期
        test_dates = [
            date(2024, 1, 1),
            date(2024, 2, 10),  # 春节
            date(2024, 5, 1),   # 劳动节
            date(2024, 10, 1),  # 国庆节
        ]
        
        print("节假日查询：")
        for test_date in test_dates:
            try:
                is_holiday = cc.is_holiday(test_date)
                is_workday = cc.is_workday(test_date)
                
                print(f"  {test_date} → ", end="")
                if is_holiday:
                    print("节假日")
                elif is_workday:
                    print("工作日")
                else:
                    print("周末")
                
            except Exception as e:
                print(f"  {test_date} → 查询失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ chinese-calendar 库未安装或导入失败")
        return False
    except Exception as e:
        print(f"❌ chinese-calendar 测试失败: {e}")
        return False

def compare_libraries():
    """对比不同库的输出"""
    print("\n🔍 对比不同库的输出")
    print("=" * 50)
    
    test_date = (1988, 3, 15)  # 测试日期
    print(f"测试日期: {test_date[0]}-{test_date[1]:02d}-{test_date[2]:02d}")
    print()
    
    # LunarCalendar
    try:
        from LunarCalendar import Converter, Solar
        solar = Solar(*test_date)
        lunar = Converter.Solar2Lunar(solar)
        print(f"LunarCalendar: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        if hasattr(lunar, 'gz_year'):
            print(f"  天干地支: {lunar.gz_year}")
    except:
        print("LunarCalendar: 无法获取结果")
    
    # lunardate
    try:
        from lunardate import LunarDate
        lunar_date = LunarDate.fromSolarDate(*test_date)
        print(f"lunardate: 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
        if hasattr(lunar_date, 'gz_year'):
            print(f"  年干支: {lunar_date.gz_year()}")
        if hasattr(lunar_date, 'gz_month'):
            print(f"  月干支: {lunar_date.gz_month()}")
        if hasattr(lunar_date, 'gz_day'):
            print(f"  日干支: {lunar_date.gz_day()}")
    except:
        print("lunardate: 无法获取结果")

def main():
    """主函数"""
    print("🌟 农历库安装和对比测试")
    print("=" * 60)
    
    # 尝试安装库
    print("📦 安装农历相关库...")
    libraries = ["LunarCalendar", "lunardate", "chinese-calendar"]
    
    for lib in libraries:
        install_package(lib)
    
    print("\n" + "=" * 60)
    
    # 测试各个库
    results = []
    
    results.append(("LunarCalendar", test_lunar_calendar()))
    results.append(("lunardate", test_lunardate()))
    results.append(("chinese-calendar", test_chinese_calendar()))
    
    # 对比输出
    compare_libraries()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for lib_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {lib_name}: {status}")
    
    print("\n💡 推荐使用:")
    successful_libs = [name for name, success in results if success]
    if successful_libs:
        print(f"  建议使用: {', '.join(successful_libs)}")
        if "lunardate" in successful_libs:
            print("  lunardate 提供了完整的干支信息，适合八字计算")
        if "LunarCalendar" in successful_libs:
            print("  LunarCalendar 功能全面，支持节气和节日")
    else:
        print("  所有库都安装失败，请检查网络连接")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的合婚分析器
基于扩充的语料库提供差异化的合婚分析
"""

import random
from typing import Dict, List, Any, Tuple
from .corpus.marriage_analysis_corpus import (
    RIZHU_COMBINATION_ANALYSIS,
    SHENGXIAO_COMBINATION_ANALYSIS,
    WUXING_STRENGTH_COMPATIBILITY,
    AGE_DIFFERENCE_ANALYSIS,
    CAREER_COMBINATION_ANALYSIS,
    DETAILED_MARRIAGE_ADVICE
)
from .core_logic_mapping import TIANGAN_WUXING, DIZHI_WUXING

class EnhancedMarriageAnalyzer:
    """增强的合婚分析器，提供差异化的专业分析"""
    
    def __init__(self):
        self.analysis_cache = {}
    
    def get_comprehensive_analysis(self, male_bazi: Dict[str, Any], 
                                 female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取全面的合婚分析
        
        Args:
            male_bazi: 男方八字信息
            female_bazi: 女方八字信息
            
        Returns:
            详细的合婚分析结果
        """
        # 提取关键信息
        male_day = male_bazi.get('day_pillar', '')
        female_day = female_bazi.get('day_pillar', '')
        male_year = male_bazi.get('year_pillar', '')
        female_year = female_bazi.get('year_pillar', '')
        
        # 生成分析结果
        analysis = {
            'rizhu_analysis': self._analyze_rizhu_combination(male_day, female_day),
            'shengxiao_analysis': self._analyze_shengxiao_combination(male_year, female_year),
            'wuxing_strength_analysis': self._analyze_wuxing_strength(male_bazi, female_bazi),
            'age_analysis': self._analyze_age_difference(male_bazi, female_bazi),
            'career_analysis': self._analyze_career_combination(male_bazi, female_bazi),
            'detailed_advice': self._generate_detailed_advice(male_bazi, female_bazi),
            'compatibility_summary': self._generate_compatibility_summary(male_bazi, female_bazi)
        }
        
        return analysis
    
    def _analyze_rizhu_combination(self, male_day: str, female_day: str) -> Dict[str, Any]:
        """分析日柱组合"""
        combination_key = (male_day, female_day)
        reverse_key = (female_day, male_day)
        
        # 查找日柱组合分析
        if combination_key in RIZHU_COMBINATION_ANALYSIS:
            return RIZHU_COMBINATION_ANALYSIS[combination_key]
        elif reverse_key in RIZHU_COMBINATION_ANALYSIS:
            # 如果是反向组合，调整描述
            analysis = RIZHU_COMBINATION_ANALYSIS[reverse_key].copy()
            return self._adjust_gender_perspective(analysis)
        else:
            # 生成基础分析
            return self._generate_basic_rizhu_analysis(male_day, female_day)
    
    def _analyze_shengxiao_combination(self, male_year: str, female_year: str) -> Dict[str, Any]:
        """分析生肖组合"""
        male_shengxiao = self._get_shengxiao_from_year(male_year)
        female_shengxiao = self._get_shengxiao_from_year(female_year)
        
        combination_key = (male_shengxiao, female_shengxiao)
        reverse_key = (female_shengxiao, male_shengxiao)
        
        if combination_key in SHENGXIAO_COMBINATION_ANALYSIS:
            return SHENGXIAO_COMBINATION_ANALYSIS[combination_key]
        elif reverse_key in SHENGXIAO_COMBINATION_ANALYSIS:
            return SHENGXIAO_COMBINATION_ANALYSIS[reverse_key]
        else:
            return self._generate_basic_shengxiao_analysis(male_shengxiao, female_shengxiao)
    
    def _analyze_wuxing_strength(self, male_bazi: Dict[str, Any], 
                               female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析五行强弱配对"""
        male_element = self._get_main_element(male_bazi)
        female_element = self._get_main_element(female_bazi)
        
        # 简化的强弱判断（实际应该更复杂）
        male_strength = self._estimate_element_strength(male_bazi, male_element)
        female_strength = self._estimate_element_strength(female_bazi, female_element)
        
        # 生成强弱组合键
        if male_strength > female_strength:
            combination_key = f"{male_element}强_{female_element}弱"
        elif female_strength > male_strength:
            combination_key = f"{female_element}强_{male_element}弱"
        else:
            combination_key = f"{male_element}_{female_element}平衡"
        
        if combination_key in WUXING_STRENGTH_COMPATIBILITY:
            return WUXING_STRENGTH_COMPATIBILITY[combination_key]
        else:
            return self._generate_basic_wuxing_strength_analysis(male_element, female_element)
    
    def _analyze_age_difference(self, male_bazi: Dict[str, Any], 
                              female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析年龄差异"""
        # 简化的年龄差异分析（实际需要根据出生年份计算）
        male_birth = male_bazi.get('birth_date', '')
        female_birth = female_bazi.get('birth_date', '')
        
        # 这里简化处理，实际应该解析具体年份
        if '1988' in male_birth and '1990' in female_birth:
            age_diff_key = '男大女小_3到5岁'
        elif '1990' in male_birth and '1988' in female_birth:
            age_diff_key = '女大男小_3到5岁'
        else:
            age_diff_key = '同龄配对'
        
        if age_diff_key in AGE_DIFFERENCE_ANALYSIS:
            return AGE_DIFFERENCE_ANALYSIS[age_diff_key]
        else:
            return {'compatibility_features': '年龄配对需要具体分析'}
    
    def _analyze_career_combination(self, male_bazi: Dict[str, Any], 
                                  female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析职业组合"""
        male_career = male_bazi.get('career', '未知')
        female_career = female_bazi.get('career', '未知')
        
        career_key = f"{male_career}_{female_career}"
        reverse_key = f"{female_career}_{male_career}"
        
        if career_key in CAREER_COMBINATION_ANALYSIS:
            return CAREER_COMBINATION_ANALYSIS[career_key]
        elif reverse_key in CAREER_COMBINATION_ANALYSIS:
            return CAREER_COMBINATION_ANALYSIS[reverse_key]
        else:
            return self._generate_basic_career_analysis(male_career, female_career)
    
    def _generate_detailed_advice(self, male_bazi: Dict[str, Any], 
                                female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细建议"""
        # 根据综合分析确定兼容性等级
        compatibility_level = self._determine_compatibility_level(male_bazi, female_bazi)
        
        advice = {}
        for category in ['沟通交流', '财务管理', '家庭建设']:
            if category in DETAILED_MARRIAGE_ADVICE:
                category_advice = DETAILED_MARRIAGE_ADVICE[category]
                if compatibility_level in category_advice:
                    advice[category] = random.choice(category_advice[compatibility_level])
                else:
                    advice[category] = "需要根据具体情况制定建议"
        
        return advice
    
    def _generate_compatibility_summary(self, male_bazi: Dict[str, Any], 
                                      female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """生成兼容性总结"""
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')
        
        # 综合各项分析得出总结
        summary = {
            'overall_assessment': f'{male_name}与{female_name}的八字配对具有独特的特点',
            'key_strengths': [],
            'potential_challenges': [],
            'development_suggestions': []
        }
        
        # 根据各项分析添加具体内容
        rizhu_analysis = self._analyze_rizhu_combination(
            male_bazi.get('day_pillar', ''), 
            female_bazi.get('day_pillar', '')
        )
        
        if 'compatibility_score' in rizhu_analysis:
            score = rizhu_analysis['compatibility_score']
            if score >= 85:
                summary['key_strengths'].append('日柱配对和谐，性格互补性强')
            elif score >= 75:
                summary['key_strengths'].append('日柱配对良好，有发展潜力')
            else:
                summary['potential_challenges'].append('日柱配对需要更多磨合')
        
        return summary
    
    def _get_shengxiao_from_year(self, year_pillar: str) -> str:
        """从年柱获取生肖"""
        if len(year_pillar) >= 2:
            dizhi = year_pillar[1]
            shengxiao_map = {
                '子': '鼠', '丑': '牛', '寅': '虎', '卯': '兔',
                '辰': '龙', '巳': '蛇', '午': '马', '未': '羊',
                '申': '猴', '酉': '鸡', '戌': '狗', '亥': '猪'
            }
            return shengxiao_map.get(dizhi, '未知')
        return '未知'
    
    def _get_main_element(self, bazi_info: Dict[str, Any]) -> str:
        """获取主要五行元素"""
        day_pillar = bazi_info.get('day_pillar', '')
        if len(day_pillar) >= 1:
            return TIANGAN_WUXING.get(day_pillar[0], '土')
        return '土'
    
    def _estimate_element_strength(self, bazi_info: Dict[str, Any], element: str) -> int:
        """估算五行强度（简化版本）"""
        # 这里是简化的强度计算，实际应该更复杂
        strength = 50  # 基础强度
        
        # 根据月令等因素调整（简化处理）
        month_pillar = bazi_info.get('month_pillar', '')
        if len(month_pillar) >= 2:
            month_element = DIZHI_WUXING.get(month_pillar[1], '土')
            if month_element == element:
                strength += 20  # 得月令
        
        return strength
    
    def _determine_compatibility_level(self, male_bazi: Dict[str, Any], 
                                     female_bazi: Dict[str, Any]) -> str:
        """确定兼容性等级"""
        # 简化的等级判断
        rizhu_analysis = self._analyze_rizhu_combination(
            male_bazi.get('day_pillar', ''), 
            female_bazi.get('day_pillar', '')
        )
        
        if 'compatibility_score' in rizhu_analysis:
            score = rizhu_analysis['compatibility_score']
            if score >= 85:
                return 'high_compatibility'
            elif score >= 70:
                return 'medium_compatibility'
            else:
                return 'low_compatibility'
        
        return 'medium_compatibility'
    
    def _adjust_gender_perspective(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """调整性别视角"""
        # 简单的文本替换，实际应该更智能
        adjusted = analysis.copy()
        for key, value in adjusted.items():
            if isinstance(value, str):
                # 这里可以添加更复杂的性别视角调整逻辑
                adjusted[key] = value
        return adjusted
    
    def _generate_basic_rizhu_analysis(self, male_day: str, female_day: str) -> Dict[str, Any]:
        """生成基础日柱分析"""
        return {
            'compatibility_score': 70,
            'relationship_dynamic': f'{male_day}与{female_day}的组合需要具体分析',
            'personality_match': '两人性格各有特点，需要相互理解',
            'communication_style': '沟通方式可能有差异，需要磨合',
            'growth_potential': '通过努力可以建立良好的关系'
        }
    
    def _generate_basic_shengxiao_analysis(self, male_shengxiao: str, female_shengxiao: str) -> Dict[str, Any]:
        """生成基础生肖分析"""
        return {
            'traditional_compatibility': f'{male_shengxiao}与{female_shengxiao}的配对需要具体分析',
            'personality_dynamics': '两人性格各有特色，需要相互适应',
            'harmony_suggestions': '建议多沟通交流，增进相互了解'
        }
    
    def _generate_basic_wuxing_strength_analysis(self, male_element: str, female_element: str) -> Dict[str, Any]:
        """生成基础五行强弱分析"""
        return {
            'balance_analysis': f'{male_element}与{female_element}的配合需要平衡',
            'relationship_dynamic': '两人在五行上各有特点',
            'growth_direction': '建议发挥各自优势，相互补充'
        }
    
    def _generate_basic_career_analysis(self, male_career: str, female_career: str) -> Dict[str, Any]:
        """生成基础职业分析"""
        return {
            'professional_compatibility': f'{male_career}与{female_career}的职业组合有其特点',
            'mutual_support': '可以在工作上相互支持和理解',
            'development_potential': '有机会在各自领域共同发展'
        }

# 全局增强合婚分析器实例
enhanced_marriage_analyzer = EnhancedMarriageAnalyzer()

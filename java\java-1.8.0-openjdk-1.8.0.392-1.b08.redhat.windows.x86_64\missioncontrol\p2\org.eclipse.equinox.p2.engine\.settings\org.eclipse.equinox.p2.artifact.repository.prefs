eclipse.preferences.version=1
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/suffix=.meta/p2-artifacts.properties
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_src_java-1.8.0-openjdk_.._.._deps_jmc-mvnrepo/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/src/java-1.8.0-openjdk/../../deps/jmc-mvnrepo/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.alert.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.alert.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.alert.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.attach.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.attach.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.attach.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.jdp.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.jdp.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.jdp.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.browser.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.browser.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.persistence.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.persistence.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.persistence.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.diagnostic.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.diagnostic.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.diagnostic.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.mbeanbrowser.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.notification.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.notification.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.notification.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.console.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.console.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.docs.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.docs.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.docs.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.feature.rcp.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.feature.rcp.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.feature.rcp.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.flightrecorder.controlpanel.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.flightrecorder.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.flightrecorder.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.flightrecorder.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.greychart.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.greychart.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.greychart.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rcp.application.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.application.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rcp.application.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rcp.intro.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rcp.intro.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rcp.intro.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rjmx.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rjmx.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rjmx.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.rjmx.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.rjmx.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.ja_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.ui.ja/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_l10n_org.openjdk.jmc.ui.zh_CN_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/l10n/org.openjdk.jmc.ui.zh_CN/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.alert_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.alert/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.attach_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.attach/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.attach_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.browser.attach/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser.jdp_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.browser.jdp/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.browser_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.browser/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.commands_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.commands/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.persistence_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.console.persistence/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.diagnostic_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.console.ui.diagnostic/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.mbeanbrowser_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.console.ui.mbeanbrowser/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui.notification_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.console.ui.notification/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.console.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.console.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.docs_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.docs/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.console_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.feature.console/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.core_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.feature.core/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.flightrecorder_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.feature.flightrecorder/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.joverflow_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.feature.joverflow/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.feature.rcp_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.feature.rcp/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.configuration_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.configuration/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.controlpanel.ui.configuration/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.controlpanel.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.controlpanel.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.flameview_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.flameview/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.rules.extensionprovider_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.rules.extensionprovider/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.flightrecorder.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.flightrecorder.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.greychart.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.greychart_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.greychart/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.jdp_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.jdp/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.joverflow.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.joverflow_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.joverflow/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.osgi.extension_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.osgi.extension/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.application_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rcp.application/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rcp.intro_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rcp.intro/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ext_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rjmx.ext/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.services.jfr_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rjmx.services.jfr/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rjmx.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.rjmx_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.rjmx/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui.common_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.ui.common/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_application_org.openjdk.jmc.ui_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/application/org.openjdk.jmc.ui/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/isSystem=false
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/suffix=p2artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/target/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/isSystem=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/name=Bundle pool
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/type=org.eclipse.equinox.p2.artifact.repository.simpleRepository
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/target/products/org.openjdk.jmc/win32/win32/x86_64/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64/version=1
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/description=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/enabled=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/isSystem=true
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/name=download cache
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/provider=
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/suffix=artifacts.xml
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/type=org.eclipse.equinox.p2.artifact.repository.simpleRepository
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/uri=file\:/C\:/cygwin64/tmp/ojdkbuild/upstream/jmc/target/products/org.openjdk.jmc/win32/win32/x86_64/p2/org.eclipse.equinox.p2.core/cache/
repositories/file\:_C\:_cygwin64_tmp_ojdkbuild_upstream_jmc_target_products_org.openjdk.jmc_win32_win32_x86_64_p2_org.eclipse.equinox.p2.core_cache/version=1.0.0
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/description=Read-only repository adapter for org.eclipse.tycho.p2.target.TargetPlatformBundlePublisher$PublishedBundlesArtifactRepository@fbd6407e
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/enabled=true
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/isSystem=false
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/provider=
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/suffix=@memory
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/type=ProviderOnlyArtifactRepository
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/uri=file\:/resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product
repositories/file\:_resolution-context-artifacts@C%253A%255Ccygwin64%255Ctmp%255Cojdkbuild%255Cupstream%255Cjmc%255Capplication%255Corg.openjdk.jmc.rcp.product/version=1.0

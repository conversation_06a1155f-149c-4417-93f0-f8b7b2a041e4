#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缩进问题
"""

# 读取文件内容
with open('data/corpus_database.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找问题行
lines = content.split('\n')
for i, line in enumerate(lines):
    if i >= 575 and i <= 585:
        print(f"Line {i+1}: '{line}' (length: {len(line)})")
        # 显示每个字符
        for j, char in enumerate(line):
            if char == ' ':
                print(f"  {j}: SPACE")
            elif char == '\t':
                print(f"  {j}: TAB")
            elif char == "'":
                print(f"  {j}: QUOTE")
                break
        print()

@echo off
echo 🌙 Java lunar-calendar 库测试脚本
echo ============================================================

set JAVA_HOME=%cd%\java\java-1.8.0-openjdk-1.8.0.392-1.b08.redhat.windows.x86_64
set JAVA_BIN=%JAVA_HOME%\bin
set LUNAR_JAR=%cd%\lunar-1.7.4.jar

echo 📍 Java路径: %JAVA_BIN%
echo 📍 Lunar JAR: %LUNAR_JAR%

echo.
echo 🔄 编译Java程序...
"%JAVA_BIN%\javac.exe" -cp "%LUNAR_JAR%" LunarCalendarTest.java

if %ERRORLEVEL% EQU 0 (
    echo ✅ 编译成功
    echo.
    echo 🚀 运行Java程序...
    echo ============================================================
    "%JAVA_BIN%\java.exe" -cp ".;%LUNAR_JAR%" LunarCalendarTest
) else (
    echo ❌ 编译失败
    echo.
    echo 🔍 检查错误信息:
    echo 1. 确保 lunar-1.7.4.jar 存在
    echo 2. 确保 LunarCalendarTest.java 语法正确
    echo 3. 确保 Java 路径正确
)

echo.
echo 按任意键退出...
pause >nul

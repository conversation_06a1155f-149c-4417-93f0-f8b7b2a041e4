#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字合婚计算模块
实现两人生辰八字的配对分析
"""

from typing import Dict, List, Any, Tuple
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data.corpus_database import COUPLE_COMPATIBILITY_CORPUS, get_random_content
from data.corpus_manager import corpus_manager

class CoupleCompatibilityCalculator:
    def __init__(self):
        """初始化八字合婚计算器"""
        self.wuxing_elements = ['木', '火', '土', '金', '水']
        self.wuxing_relations = self._init_wuxing_relations()
        self.compatibility_rules = self._init_compatibility_rules()
        self.pillar_weights = {
            'year': 0.2,   # 年柱权重 - 代表根基
            'month': 0.25, # 月柱权重 - 代表事业
            'day': 0.35,   # 日柱权重 - 代表自身（最重要）
            'hour': 0.2    # 时柱权重 - 代表子女
        }
    
    def _init_wuxing_relations(self) -> Dict[str, Dict]:
        """初始化五行生克关系"""
        return {
            'sheng': {  # 相生关系
                '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
            },
            'ke': {     # 相克关系
                '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
            },
            'scores': { # 关系评分
                'same': 80,      # 同类
                'sheng_me': 85,  # 生我
                'me_sheng': 75,  # 我生
                'ke_me': 40,     # 克我
                'me_ke': 50,     # 我克
                'neutral': 60    # 中性
            }
        }
    
    def _init_compatibility_rules(self) -> Dict[str, Any]:
        """初始化合婚规则"""
        return {
            # 天干合化
            'tiangan_he': {
                ('甲', '己'): {'element': '土', 'score': 90, 'description': '甲己合土，中正之合'},
                ('乙', '庚'): {'element': '金', 'score': 85, 'description': '乙庚合金，仁义之合'},
                ('丙', '辛'): {'element': '水', 'score': 88, 'description': '丙辛合水，威制之合'},
                ('丁', '壬'): {'element': '木', 'score': 87, 'description': '丁壬合木，淫匿之合'},
                ('戊', '癸'): {'element': '火', 'score': 86, 'description': '戊癸合火，无情之合'}
            },
            
            # 地支三合
            'dizhi_sanhe': {
                ('申', '子', '辰'): {'element': '水', 'score': 95, 'description': '申子辰三合水局'},
                ('亥', '卯', '未'): {'element': '木', 'score': 95, 'description': '亥卯未三合木局'},
                ('寅', '午', '戌'): {'element': '火', 'score': 95, 'description': '寅午戌三合火局'},
                ('巳', '酉', '丑'): {'element': '金', 'score': 95, 'description': '巳酉丑三合金局'}
            },
            
            # 地支六合
            'dizhi_liuhe': {
                ('子', '丑'): {'score': 85, 'description': '子丑合土'},
                ('寅', '亥'): {'score': 85, 'description': '寅亥合木'},
                ('卯', '戌'): {'score': 85, 'description': '卯戌合火'},
                ('辰', '酉'): {'score': 85, 'description': '辰酉合金'},
                ('巳', '申'): {'score': 85, 'description': '巳申合水'},
                ('午', '未'): {'score': 85, 'description': '午未合土'}
            },
            
            # 地支相冲
            'dizhi_chong': {
                ('子', '午'): {'score': 30, 'description': '子午相冲'},
                ('丑', '未'): {'score': 35, 'description': '丑未相冲'},
                ('寅', '申'): {'score': 30, 'description': '寅申相冲'},
                ('卯', '酉'): {'score': 30, 'description': '卯酉相冲'},
                ('辰', '戌'): {'score': 35, 'description': '辰戌相冲'},
                ('巳', '亥'): {'score': 30, 'description': '巳亥相冲'}
            },
            
            # 地支相害
            'dizhi_hai': {
                ('子', '未'): {'score': 45, 'description': '子未相害'},
                ('丑', '午'): {'score': 45, 'description': '丑午相害'},
                ('寅', '巳'): {'score': 45, 'description': '寅巳相害'},
                ('卯', '辰'): {'score': 45, 'description': '卯辰相害'},
                ('申', '亥'): {'score': 45, 'description': '申亥相害'},
                ('酉', '戌'): {'score': 45, 'description': '酉戌相害'}
            }
        }
    
    def calculate_compatibility(self, male_bazi: Dict[str, Any], 
                              female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算两人八字合婚结果
        
        Args:
            male_bazi: 男方八字信息
            female_bazi: 女方八字信息
            
        Returns:
            合婚分析结果
        """
        # 分析各柱的配对情况
        pillar_analysis = self._analyze_pillars(male_bazi, female_bazi)
        
        # 分析五行配合
        wuxing_analysis = self._analyze_wuxing_compatibility(male_bazi, female_bazi)
        
        # 分析日柱配对（最重要）
        day_pillar_analysis = self._analyze_day_pillar(male_bazi, female_bazi)
        
        # 分析十神关系
        shishen_analysis = self._analyze_shishen_compatibility(male_bazi, female_bazi)
        
        # 计算综合评分
        overall_score = self._calculate_overall_score(
            pillar_analysis, wuxing_analysis, day_pillar_analysis, shishen_analysis
        )
        
        # 生成合婚建议
        suggestions = self._generate_compatibility_suggestions(
            overall_score, pillar_analysis, wuxing_analysis, male_bazi, female_bazi
        )
        
        return {
            'overall_score': overall_score,
            'compatibility_level': self._get_compatibility_level(overall_score),
            'pillar_analysis': pillar_analysis,
            'wuxing_analysis': wuxing_analysis,
            'day_pillar_analysis': day_pillar_analysis,
            'shishen_analysis': shishen_analysis,
            'suggestions': suggestions,
            'detailed_analysis': self._generate_detailed_analysis(
                male_bazi, female_bazi, overall_score, pillar_analysis, wuxing_analysis
            )
        }
    
    def _analyze_pillars(self, male_bazi: Dict[str, Any], 
                        female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析四柱配对情况"""
        pillars = ['year', 'month', 'day', 'hour']
        pillar_results = {}
        
        for pillar in pillars:
            male_pillar = male_bazi.get(f'{pillar}_pillar', '')
            female_pillar = female_bazi.get(f'{pillar}_pillar', '')
            
            if male_pillar and female_pillar:
                male_tg, male_dz = male_pillar[0], male_pillar[1]
                female_tg, female_dz = female_pillar[0], female_pillar[1]
                
                # 分析天干关系
                tg_relation = self._analyze_tiangan_relation(male_tg, female_tg)
                
                # 分析地支关系
                dz_relation = self._analyze_dizhi_relation(male_dz, female_dz)
                
                # 计算该柱的配对分数
                pillar_score = (tg_relation['score'] + dz_relation['score']) / 2
                
                pillar_results[pillar] = {
                    'male_pillar': male_pillar,
                    'female_pillar': female_pillar,
                    'tiangan_relation': tg_relation,
                    'dizhi_relation': dz_relation,
                    'pillar_score': pillar_score,
                    'weight': self.pillar_weights[pillar]
                }
        
        return pillar_results
    
    def _analyze_tiangan_relation(self, male_tg: str, female_tg: str) -> Dict[str, Any]:
        """分析天干关系"""
        # 检查天干合化
        he_pair = (male_tg, female_tg)
        reverse_he_pair = (female_tg, male_tg)
        
        if he_pair in self.compatibility_rules['tiangan_he']:
            return {
                'type': 'he',
                'score': self.compatibility_rules['tiangan_he'][he_pair]['score'],
                'description': self.compatibility_rules['tiangan_he'][he_pair]['description']
            }
        elif reverse_he_pair in self.compatibility_rules['tiangan_he']:
            return {
                'type': 'he',
                'score': self.compatibility_rules['tiangan_he'][reverse_he_pair]['score'],
                'description': self.compatibility_rules['tiangan_he'][reverse_he_pair]['description']
            }
        
        # 如果没有合化，分析五行关系
        male_element = self._get_tiangan_element(male_tg)
        female_element = self._get_tiangan_element(female_tg)
        
        return self._analyze_element_relation(male_element, female_element)
    
    def _analyze_dizhi_relation(self, male_dz: str, female_dz: str) -> Dict[str, Any]:
        """分析地支关系"""
        dz_pair = (male_dz, female_dz)
        reverse_dz_pair = (female_dz, male_dz)
        
        # 检查六合
        if dz_pair in self.compatibility_rules['dizhi_liuhe'] or reverse_dz_pair in self.compatibility_rules['dizhi_liuhe']:
            rule = self.compatibility_rules['dizhi_liuhe'].get(dz_pair) or self.compatibility_rules['dizhi_liuhe'].get(reverse_dz_pair)
            return {
                'type': 'liuhe',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 检查相冲
        if dz_pair in self.compatibility_rules['dizhi_chong'] or reverse_dz_pair in self.compatibility_rules['dizhi_chong']:
            rule = self.compatibility_rules['dizhi_chong'].get(dz_pair) or self.compatibility_rules['dizhi_chong'].get(reverse_dz_pair)
            return {
                'type': 'chong',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 检查相害
        if dz_pair in self.compatibility_rules['dizhi_hai'] or reverse_dz_pair in self.compatibility_rules['dizhi_hai']:
            rule = self.compatibility_rules['dizhi_hai'].get(dz_pair) or self.compatibility_rules['dizhi_hai'].get(reverse_dz_pair)
            return {
                'type': 'hai',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 如果没有特殊关系，分析五行关系
        male_element = self._get_dizhi_element(male_dz)
        female_element = self._get_dizhi_element(female_dz)
        
        return self._analyze_element_relation(male_element, female_element)
    
    def _get_tiangan_element(self, tiangan: str) -> str:
        """获取天干对应的五行"""
        tiangan_elements = {
            '甲': '木', '乙': '木',
            '丙': '火', '丁': '火',
            '戊': '土', '己': '土',
            '庚': '金', '辛': '金',
            '壬': '水', '癸': '水'
        }
        return tiangan_elements.get(tiangan, '未知')
    
    def _get_dizhi_element(self, dizhi: str) -> str:
        """获取地支对应的五行"""
        dizhi_elements = {
            '子': '水', '丑': '土', '寅': '木', '卯': '木',
            '辰': '土', '巳': '火', '午': '火', '未': '土',
            '申': '金', '酉': '金', '戌': '土', '亥': '水'
        }
        return dizhi_elements.get(dizhi, '未知')
    
    def _analyze_element_relation(self, element1: str, element2: str) -> Dict[str, Any]:
        """分析两个五行元素的关系"""
        if element1 == element2:
            return {
                'type': 'same',
                'score': self.wuxing_relations['scores']['same'],
                'description': f'{element1}{element2}同类，和谐相处'
            }
        
        # 检查相生关系
        if self.wuxing_relations['sheng'].get(element1) == element2:
            return {
                'type': 'me_sheng',
                'score': self.wuxing_relations['scores']['me_sheng'],
                'description': f'{element1}生{element2}，有助益关系'
            }
        elif self.wuxing_relations['sheng'].get(element2) == element1:
            return {
                'type': 'sheng_me',
                'score': self.wuxing_relations['scores']['sheng_me'],
                'description': f'{element2}生{element1}，得到助力'
            }
        
        # 检查相克关系
        if self.wuxing_relations['ke'].get(element1) == element2:
            return {
                'type': 'me_ke',
                'score': self.wuxing_relations['scores']['me_ke'],
                'description': f'{element1}克{element2}，存在制约'
            }
        elif self.wuxing_relations['ke'].get(element2) == element1:
            return {
                'type': 'ke_me',
                'score': self.wuxing_relations['scores']['ke_me'],
                'description': f'{element2}克{element1}，受到制约'
            }
        
        return {
            'type': 'neutral',
            'score': self.wuxing_relations['scores']['neutral'],
            'description': f'{element1}与{element2}关系中性'
        }

    def _analyze_wuxing_compatibility(self, male_bazi: Dict[str, Any],
                                    female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析五行配合情况"""
        male_elements = self._extract_bazi_elements(male_bazi)
        female_elements = self._extract_bazi_elements(female_bazi)

        # 分析五行强弱互补
        complement_score = self._calculate_element_complement(male_elements, female_elements)

        # 分析主要五行关系
        male_main_element = male_bazi.get('day_master_element', '未知')
        female_main_element = female_bazi.get('day_master_element', '未知')
        main_relation = self._analyze_element_relation(male_main_element, female_main_element)

        return {
            'male_elements': male_elements,
            'female_elements': female_elements,
            'complement_score': complement_score,
            'main_element_relation': main_relation,
            'balance_analysis': self._analyze_element_balance(male_elements, female_elements)
        }

    def _extract_bazi_elements(self, bazi: Dict[str, Any]) -> Dict[str, int]:
        """提取八字中的五行分布"""
        elements = {'木': 0, '火': 0, '土': 0, '金': 0, '水': 0}

        pillars = ['year_pillar', 'month_pillar', 'day_pillar', 'hour_pillar']
        for pillar in pillars:
            if pillar in bazi and bazi[pillar]:
                tg_element = self._get_tiangan_element(bazi[pillar][0])
                dz_element = self._get_dizhi_element(bazi[pillar][1])
                elements[tg_element] += 1
                elements[dz_element] += 1

        return elements

    def _calculate_element_complement(self, male_elements: Dict[str, int],
                                    female_elements: Dict[str, int]) -> int:
        """计算五行互补程度"""
        complement_score = 0

        for element in self.wuxing_elements:
            male_count = male_elements.get(element, 0)
            female_count = female_elements.get(element, 0)

            # 如果一方缺少某五行，另一方有，则互补性好
            if male_count == 0 and female_count > 0:
                complement_score += 15
            elif female_count == 0 and male_count > 0:
                complement_score += 15
            # 如果双方都有但数量相近，也是好的
            elif abs(male_count - female_count) <= 1:
                complement_score += 10

        return min(complement_score, 100)

    def _analyze_element_balance(self, male_elements: Dict[str, int],
                               female_elements: Dict[str, int]) -> Dict[str, Any]:
        """分析五行平衡情况"""
        combined_elements = {}
        for element in self.wuxing_elements:
            combined_elements[element] = male_elements.get(element, 0) + female_elements.get(element, 0)

        total = sum(combined_elements.values())
        balance_score = 100

        # 检查是否有五行过旺或过弱
        for element, count in combined_elements.items():
            ratio = count / total if total > 0 else 0
            if ratio > 0.4:  # 某五行过旺
                balance_score -= 20
            elif ratio < 0.1:  # 某五行过弱
                balance_score -= 15

        return {
            'combined_elements': combined_elements,
            'balance_score': max(balance_score, 0),
            'dominant_element': max(combined_elements, key=combined_elements.get),
            'weak_elements': [e for e, c in combined_elements.items() if c <= 1]
        }

    def _analyze_day_pillar(self, male_bazi: Dict[str, Any],
                          female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析日柱配对（最重要的配对分析）"""
        male_day = male_bazi.get('day_pillar', '')
        female_day = female_bazi.get('day_pillar', '')

        if not male_day or not female_day:
            return {
                'male_day_pillar': male_day or '未知',
                'female_day_pillar': female_day or '未知',
                'tiangan_relation': {'score': 50, 'description': '信息不完整'},
                'dizhi_relation': {'score': 50, 'description': '信息不完整'},
                'special_score': 50,
                'final_score': 50,
                'analysis': '日柱信息不完整，无法进行详细分析'
            }

        # 分析日柱天干地支关系
        male_tg, male_dz = male_day[0], male_day[1]
        female_tg, female_dz = female_day[0], female_day[1]

        tg_relation = self._analyze_tiangan_relation(male_tg, female_tg)
        dz_relation = self._analyze_dizhi_relation(male_dz, female_dz)

        # 日柱配对特殊规则
        special_score = self._check_day_pillar_special_rules(male_day, female_day)

        # 计算日柱总分（权重更高）
        base_score = (tg_relation['score'] * 0.6 + dz_relation['score'] * 0.4)
        final_score = (base_score + special_score) / 2

        return {
            'male_day_pillar': male_day,
            'female_day_pillar': female_day,
            'tiangan_relation': tg_relation,
            'dizhi_relation': dz_relation,
            'special_score': special_score,
            'final_score': final_score,
            'analysis': self._generate_day_pillar_analysis(tg_relation, dz_relation, final_score)
        }

    def _check_day_pillar_special_rules(self, male_day: str, female_day: str) -> int:
        """检查日柱特殊配对规则"""
        # 日柱相同
        if male_day == female_day:
            return 95  # 同日柱，非常合适

        # 日柱纳音五行相生
        # 这里简化处理，实际应该查纳音表
        male_nayin = self._get_nayin_element(male_day)
        female_nayin = self._get_nayin_element(female_day)

        if male_nayin and female_nayin:
            nayin_relation = self._analyze_element_relation(male_nayin, female_nayin)
            return nayin_relation['score']

        return 60  # 默认分数

    def _get_nayin_element(self, pillar: str) -> str:
        """获取纳音五行（简化版）"""
        # 这里是简化的纳音五行对照
        nayin_dict = {
            '甲子': '金', '乙丑': '金', '丙寅': '火', '丁卯': '火',
            '戊辰': '木', '己巳': '木', '庚午': '土', '辛未': '土',
            '壬申': '金', '癸酉': '金', '甲戌': '火', '乙亥': '火'
            # 实际应该包含所有60甲子的纳音
        }
        return nayin_dict.get(pillar, '土')  # 默认返回土

    def _analyze_shishen_compatibility(self, male_bazi: Dict[str, Any],
                                     female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析十神配合情况"""
        # 这里简化处理，实际需要完整的十神分析
        male_shishen = male_bazi.get('main_shishen', '未知')
        female_shishen = female_bazi.get('main_shishen', '未知')

        # 十神配对评分表
        shishen_compatibility = {
            ('正官', '正财'): 90,
            ('七杀', '食神'): 85,
            ('正印', '伤官'): 80,
            ('偏印', '食神'): 75,
            ('比肩', '正财'): 85,
            ('劫财', '偏财'): 80
        }

        pair = (male_shishen, female_shishen)
        reverse_pair = (female_shishen, male_shishen)

        score = shishen_compatibility.get(pair) or shishen_compatibility.get(reverse_pair) or 70

        return {
            'male_shishen': male_shishen,
            'female_shishen': female_shishen,
            'compatibility_score': score,
            'description': self._get_shishen_compatibility_description(male_shishen, female_shishen, score)
        }

    def _calculate_overall_score(self, pillar_analysis: Dict, wuxing_analysis: Dict,
                               day_pillar_analysis: Dict, shishen_analysis: Dict) -> int:
        """计算综合配对分数"""
        total_score = 0
        total_weight = 0

        # 四柱分析权重计算
        for pillar, data in pillar_analysis.items():
            weight = data['weight']
            score = data['pillar_score']
            total_score += score * weight
            total_weight += weight

        # 五行配合分析
        wuxing_score = (wuxing_analysis['complement_score'] +
                       wuxing_analysis['main_element_relation']['score'] +
                       wuxing_analysis['balance_analysis']['balance_score']) / 3
        total_score += wuxing_score * 0.25
        total_weight += 0.25

        # 日柱分析（最重要）
        total_score += day_pillar_analysis['final_score'] * 0.35
        total_weight += 0.35

        # 十神分析
        total_score += shishen_analysis['compatibility_score'] * 0.15
        total_weight += 0.15

        return int(total_score / total_weight) if total_weight > 0 else 60

    def _get_compatibility_level(self, score: int) -> str:
        """根据分数获取配对等级"""
        if score >= 90:
            return '天作之合'
        elif score >= 80:
            return '非常匹配'
        elif score >= 70:
            return '比较匹配'
        elif score >= 60:
            return '一般匹配'
        elif score >= 50:
            return '需要磨合'
        else:
            return '不太匹配'

    def _get_compatibility_description(self, level: str) -> str:
        """获取配对等级的随机描述"""
        descriptions = COUPLE_COMPATIBILITY_CORPUS['compatibility_levels'].get(level, [level])
        return get_random_content(descriptions)

    def _generate_compatibility_suggestions(self, overall_score: int,
                                          pillar_analysis: Dict,
                                          wuxing_analysis: Dict,
                                          male_bazi: Dict[str, Any],
                                          female_bazi: Dict[str, Any]) -> List[str]:
        """生成合婚建议"""
        suggestions = []
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')

        # 根据分数选择建议类型
        if overall_score >= 80:
            suggestion_type = 'high_score'
        elif overall_score >= 60:
            suggestion_type = 'medium_score'
        else:
            suggestion_type = 'low_score'

        # 从语料库中随机选择建议
        base_suggestions = COUPLE_COMPATIBILITY_CORPUS['compatibility_suggestions'][suggestion_type]
        for suggestion in base_suggestions:
            suggestions.append(get_random_content([suggestion]))

        # 根据具体分析添加针对性建议
        if wuxing_analysis['balance_analysis']['balance_score'] < 60:
            suggestions.append('注意五行平衡，可以通过风水布局或饮食调理来改善双方的运势')

        # 根据主要五行关系添加具体建议
        main_relation = wuxing_analysis['main_element_relation']
        relation_type = main_relation.get('type', 'neutral')

        # 生成针对性的五行关系建议
        if relation_type == 'me_sheng':
            suggestions.append(f'{male_name}的五行能够助益{female_name}，在关系中{male_name}宜多给予支持和帮助')
        elif relation_type == 'sheng_me':
            suggestions.append(f'{female_name}的五行能够助益{male_name}，在关系中{female_name}宜多给予支持和帮助')
        elif relation_type == 'me_ke':
            suggestions.append(f'{male_name}在关系中可能比较强势，建议适当收敛，给{female_name}更多空间')
        elif relation_type == 'ke_me':
            suggestions.append(f'{female_name}在关系中可能比较强势，建议适当收敛，给{male_name}更多空间')
        elif relation_type == 'same':
            suggestions.append(f'{male_name}和{female_name}五行相同，容易产生共鸣，但也要注意避免过于相似而缺乏互补')

        return suggestions

    def _generate_detailed_analysis(self, male_bazi: Dict[str, Any],
                                  female_bazi: Dict[str, Any],
                                  overall_score: int,
                                  pillar_analysis: Dict[str, Any],
                                  wuxing_analysis: Dict[str, Any]) -> str:
        """生成详细分析报告"""
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')

        # 确定兼容性等级
        if overall_score >= 80:
            compatibility_type = 'high_compatibility'
        elif overall_score >= 60:
            compatibility_type = 'medium_compatibility'
        else:
            compatibility_type = 'low_compatibility'

        # 生成四柱详细分析
        pillar_details = self._generate_pillar_detailed_analysis(pillar_analysis, male_bazi, female_bazi)

        # 生成时间段建议
        time_period_suggestions = self._generate_time_period_suggestions(male_bazi, female_bazi, wuxing_analysis)

        # 生成具体事项建议
        specific_suggestions = self._generate_specific_suggestions(male_bazi, female_bazi, overall_score, wuxing_analysis)

        analysis = f"""
八字合婚详细分析报告

{male_name}与{female_name}的深度配对分析：

综合配对分数：{overall_score}分
配对等级：{self._get_compatibility_level(overall_score)}

【四柱详细分析】
{pillar_details}

【五行配合分析】
{self._generate_wuxing_detailed_analysis(wuxing_analysis, male_name, female_name)}

【时间发展建议】
{time_period_suggestions}

【具体事项建议】
{specific_suggestions}

【总体建议】
八字合婚是传统命理学的重要参考，但真正的幸福婚姻需要双方的共同努力。
建议在相处中多观察彼此的性格、价值观和生活习惯是否匹配。
命理分析只是提供方向指导，最终的选择还是要靠双方的真心和努力。
        """

        return analysis.strip()

    def _generate_pillar_detailed_analysis(self, pillar_analysis: Dict[str, Any],
                                         male_bazi: Dict[str, Any],
                                         female_bazi: Dict[str, Any]) -> str:
        """生成四柱详细分析"""
        pillar_names = {'year': '年柱', 'month': '月柱', 'day': '日柱', 'hour': '时柱'}
        pillar_types = {'year': 'year_pillar', 'month': 'month_pillar', 'day': 'day_pillar', 'hour': 'hour_pillar'}

        detailed_analysis = ""

        for pillar_key, pillar_name in pillar_names.items():
            if pillar_key in pillar_analysis:
                data = pillar_analysis[pillar_key]
                score = data['pillar_score']

                # 确定分析等级
                if score >= 85:
                    analysis_level = 'excellent'
                elif score >= 70:
                    analysis_level = 'good'
                elif score >= 60:
                    analysis_level = 'average'
                else:
                    analysis_level = 'challenging'

                # 获取天干地支
                male_pillar = data.get('male_pillar', '')
                female_pillar = data.get('female_pillar', '')

                if male_pillar and female_pillar and len(male_pillar) >= 2 and len(female_pillar) >= 2:
                    male_tg, male_dz = male_pillar[0], male_pillar[1]
                    female_tg, female_dz = female_pillar[0], female_pillar[1]

                    tg_desc = data['tiangan_relation']['description']
                    dz_desc = data['dizhi_relation']['description']

                    # 从语料库获取详细分析
                    pillar_corpus = COUPLE_COMPATIBILITY_CORPUS['pillar_detailed_analysis'][pillar_types[pillar_key]]
                    analysis_templates = pillar_corpus[analysis_level]

                    # 选择模板并填充变量
                    template = get_random_content(analysis_templates)
                    analysis_text = template.format(
                        male_tg=male_tg, female_tg=female_tg,
                        male_dz=male_dz, female_dz=female_dz,
                        tg_relation=tg_desc, dz_relation=dz_desc
                    )

                    detailed_analysis += f"\n{pillar_name}分析（权重{data['weight']*100:.0f}%，得分{score:.0f}分）：\n{analysis_text}\n"
                else:
                    detailed_analysis += f"\n{pillar_name}分析：信息不完整，无法进行详细分析。\n"

        return detailed_analysis

    def _generate_wuxing_detailed_analysis(self, wuxing_analysis: Dict[str, Any],
                                         male_name: str, female_name: str) -> str:
        """生成五行详细分析"""
        male_elements = wuxing_analysis.get('male_elements', {})
        female_elements = wuxing_analysis.get('female_elements', {})
        main_relation = wuxing_analysis.get('main_element_relation', {})
        balance_analysis = wuxing_analysis.get('balance_analysis', {})

        analysis = f"""
{male_name}的五行分布：{male_elements}
{female_name}的五行分布：{female_elements}

主要五行关系：{main_relation.get('description', '关系不明')}

五行互补分析：
- 互补程度：{wuxing_analysis.get('complement_score', 0)}分
- 合并后五行分布：{balance_analysis.get('combined_elements', {})}
- 主导五行：{balance_analysis.get('dominant_element', '未知')}
- 薄弱五行：{balance_analysis.get('weak_elements', [])}

五行配合建议：
根据五行生克理论，{main_relation.get('description', '你们的五行关系需要进一步观察')}。
在日常生活中，可以通过颜色、方位、饮食等方面来调节五行平衡。
        """

        return analysis.strip()

    def _generate_time_period_suggestions(self, male_bazi: Dict[str, Any],
                                        female_bazi: Dict[str, Any],
                                        wuxing_analysis: Dict[str, Any]) -> str:
        """生成基于八字的时间段建议"""
        suggestions = ""

        # 分析主要的五行关系类型
        main_relation = wuxing_analysis.get('main_element_relation', {})
        relation_type = main_relation.get('type', 'neutral')

        # 获取男女双方的日柱天干地支
        male_day = male_bazi.get('day_pillar', '')
        female_day = female_bazi.get('day_pillar', '')

        # 确定八字配对类型
        bazi_type = self._determine_bazi_compatibility_type(male_day, female_day, relation_type)

        # 从语料库获取对应的时间建议
        time_analysis = COUPLE_COMPATIBILITY_CORPUS.get('time_period_analysis_by_bazi', {})
        if bazi_type in time_analysis:
            type_data = time_analysis[bazi_type]

            suggestions += f"\n近期发展建议（1-3年）：\n{type_data['short_term']}\n\n"
            suggestions += f"中期发展规划（3-7年）：\n{type_data['medium_term']}\n\n"
            suggestions += f"长远发展展望（7年以上）：\n{type_data['long_term']}\n"
        else:
            # 默认建议
            suggestions += "\n根据你们的八字配对情况，建议在相处中多观察彼此的性格特点，"
            suggestions += "通过时间的考验来验证感情的真实性。重要的决定要慎重考虑，"
            suggestions += "给彼此足够的时间和空间来了解对方。\n"

        return suggestions

    def _determine_bazi_compatibility_type(self, male_day: str, female_day: str, relation_type: str) -> str:
        """确定八字配对类型"""
        if not male_day or not female_day or len(male_day) < 2 or len(female_day) < 2:
            return 'unknown'

        male_tg, male_dz = male_day[0], male_day[1]
        female_tg, female_dz = female_day[0], female_day[1]

        # 天干五行对应
        tg_wuxing = {
            '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
            '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
        }

        # 地支五行对应
        dz_wuxing = {
            '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
            '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
        }

        male_tg_element = tg_wuxing.get(male_tg, '未知')
        female_tg_element = tg_wuxing.get(female_tg, '未知')

        # 判断主要的五行关系类型
        if (male_tg_element == '水' and female_tg_element == '火') or \
           (male_tg_element == '火' and female_tg_element == '水'):
            return 'water_fire_conflict'
        elif (male_tg_element == '金' and female_tg_element == '木') or \
             (male_tg_element == '木' and female_tg_element == '金'):
            return 'metal_wood_conflict'
        elif (male_tg_element == '土' and female_tg_element == '水') or \
             (male_tg_element == '水' and female_tg_element == '土'):
            return 'earth_water_mix'
        elif (male_tg_element == '火' and female_tg_element == '土') or \
             (male_tg_element == '土' and female_tg_element == '火'):
            return 'fire_earth_harmony'
        elif (male_tg_element == '水' and female_tg_element == '木') or \
             (male_tg_element == '木' and female_tg_element == '水'):
            return 'water_wood_harmony'
        else:
            return 'unknown'

    def _generate_specific_suggestions(self, male_bazi: Dict[str, Any],
                                     female_bazi: Dict[str, Any],
                                     overall_score: int,
                                     wuxing_analysis: Dict[str, Any]) -> str:
        """生成基于八字的具体事项建议"""
        suggestions = ""

        # 确定兼容性等级
        if overall_score >= 80:
            compatibility_type = 'high_compatibility'
        elif overall_score >= 60:
            compatibility_type = 'medium_compatibility'
        else:
            compatibility_type = 'low_compatibility'

        # 获取男女双方的日柱
        male_day_pillar = male_bazi.get('day_pillar', '')
        female_day_pillar = female_bazi.get('day_pillar', '')

        # 添加基于日柱的个性化分析
        if male_day_pillar in COUPLE_COMPATIBILITY_CORPUS.get('day_pillar_specific_analysis', {}):
            male_analysis = COUPLE_COMPATIBILITY_CORPUS['day_pillar_specific_analysis'][male_day_pillar]
            suggestions += f"\n【{male_bazi.get('name', '男方')}的日柱特质分析】\n"
            suggestions += f"性格特点：{male_analysis['personality']}\n"
            suggestions += f"感情建议：{male_analysis['relationship']}\n\n"

        if female_day_pillar in COUPLE_COMPATIBILITY_CORPUS.get('day_pillar_specific_analysis', {}):
            female_analysis = COUPLE_COMPATIBILITY_CORPUS['day_pillar_specific_analysis'][female_day_pillar]
            suggestions += f"【{female_bazi.get('name', '女方')}的日柱特质分析】\n"
            suggestions += f"性格特点：{female_analysis['personality']}\n"
            suggestions += f"感情建议：{female_analysis['relationship']}\n\n"

        # 基于兼容性等级的具体建议
        specific_areas = ['communication', 'financial_management', 'career_development']
        area_titles = {
            'communication': '沟通交流建议',
            'financial_management': '财务管理建议',
            'career_development': '事业发展建议'
        }

        suggestions_data = COUPLE_COMPATIBILITY_CORPUS.get('specific_suggestions_by_compatibility', {})
        if compatibility_type in suggestions_data:
            for area in specific_areas:
                if area in suggestions_data[compatibility_type]:
                    title = area_titles[area]
                    content_list = suggestions_data[compatibility_type][area]

                    suggestions += f"【{title}】\n"
                    for i, content in enumerate(content_list, 1):
                        suggestions += f"{i}. {content}\n"
                    suggestions += "\n"

        # 添加基于五行关系的特殊建议
        main_relation = wuxing_analysis.get('main_element_relation', {})
        relation_type = main_relation.get('type', 'neutral')

        if relation_type == 'ke_me' or relation_type == 'me_ke':
            suggestions += "【特别提醒】\n"
            suggestions += "你们的五行关系存在相克，建议：\n"
            suggestions += "1. 在家居布置上多用中性色调，避免过于强烈的对比\n"
            suggestions += "2. 重要决定时要冷静思考，避免情绪化的争执\n"
            suggestions += "3. 可以通过共同的兴趣爱好来增进感情\n\n"
        elif relation_type == 'sheng_me' or relation_type == 'me_sheng':
            suggestions += "【特别提醒】\n"
            suggestions += "你们的五行关系相生，建议：\n"
            suggestions += "1. 充分发挥这种互补优势，在生活中相互支持\n"
            suggestions += "2. 可以考虑在事业上合作，会有很好的效果\n"
            suggestions += "3. 这种关系有利于财运，可以共同理财投资\n\n"

        return suggestions

    def _generate_day_pillar_analysis(self, tg_relation: Dict, dz_relation: Dict, score: int) -> str:
        """生成日柱分析描述"""
        # 根据分数确定分析类型
        if score >= 85:
            analysis_type = 'excellent'
        elif score >= 70:
            analysis_type = 'good'
        elif score >= 60:
            analysis_type = 'average'
        else:
            analysis_type = 'challenging'

        # 从语料库中获取随机描述
        base_analysis = COUPLE_COMPATIBILITY_CORPUS['day_pillar_analysis'][analysis_type]
        main_description = get_random_content(base_analysis)

        # 添加具体的天干地支关系描述
        detailed_description = f"{main_description} {tg_relation['description']}，{dz_relation['description']}。"

        return detailed_description

    def _get_shishen_compatibility_description(self, male_shishen: str,
                                             female_shishen: str, score: int) -> str:
        """获取十神配对描述"""
        if score >= 85:
            return f"{male_shishen}与{female_shishen}的组合非常和谐，性格互补，有利于家庭稳定。"
        elif score >= 70:
            return f"{male_shishen}与{female_shishen}的组合比较匹配，能够相互理解支持。"
        else:
            return f"{male_shishen}与{female_shishen}的组合需要磨合，建议多沟通交流。"

# /**
#  * Copyright (c) 2002-2011 IBM Corporation and others.
#  * All rights reserved.   This program and the accompanying materials
#  * are made available under the terms of the Eclipse Public License v2.0
#  * which accompanies this distribution, and is available at
#  * http://www.eclipse.org/legal/epl-v20.html
#  * 
#  * Contributors: 
#  *   IBM - Initial API and implementation
#  */

# NLS_MESSAGEFORMAT_VAR

# "featureName" property - name of the feature
featureName=EMF - Eclipse Modeling Framework Core Runtime

# "providerName" property - name of the company that provides the feature
providerName=Eclipse Modeling Project

# "description" property - description of the feature
description=The core runtime for EMF, including EMF's common utilities, Ecore, XML/XMI persistence, and the change model.

EMFUpdateSiteName=Eclipse Modeling Framework Updates

# "licenseURL" property - URL of the "Feature License"
# do not translate value - just change to point to a locale-specific HTML page
licenseURL=license.html

# "license" property - text of the "Feature Update License"
# should be plain text version of license agreement pointed to be "licenseURL"
license=\
Eclipse Foundation Software User Agreement\n\
\n\
November 22, 2017\n\
\n\
Usage Of Content\n\
\n\
THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION\n\
AND/OR OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY "CONTENT"). USE OF\n\
THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS AGREEMENT AND/OR THE\n\
TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED\n\
BELOW. BY USING THE CONTENT, YOU AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED\n\
BY THIS AGREEMENT AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE\n\
AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW. IF YOU DO NOT AGREE TO THE\n\
TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS OF ANY\n\
APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED BELOW, THEN YOU\n\
MAY NOT USE THE CONTENT.\n\
\n\
Applicable Licenses\n\
\n\
Unless otherwise indicated, all Content made available by the Eclipse Foundation\n\
is provided to you under the terms and conditions of the Eclipse Public License\n\
Version 2.0 ("EPL"). A copy of the EPL is provided with this Content and is also\n\
available at http://www.eclipse.org/legal/epl-2.0. For purposes of the EPL,\n\
"Program" will mean the Content.\n\
\n\
Content includes, but is not limited to, source code, object code, documentation\n\
and other files maintained in the Eclipse Foundation source code repository\n\
("Repository") in software modules ("Modules") and made available as\n\
downloadable archives ("Downloads").\n\
\n\
-   Content may be structured and packaged into modules to facilitate\n\
    delivering, extending, and upgrading the Content. Typical modules may\n\
    include plug-ins ("Plug-ins"), plug-in fragments ("Fragments"), and\n\
    features ("Features").\n\
-   Each Plug-in or Fragment may be packaged as a sub-directory or JAR\n\
    (Java\u2122 ARchive) in a directory named "plugins".\n\
-   A Feature is a bundle of one or more Plug-ins and/or Fragments and\n\
    associated material. Each Feature may be packaged as a sub-directory in a\n\
    directory named "features". Within a Feature, files named "feature.xml" may\n\
    contain a list of the names and version numbers of the Plug-ins and/or\n\
    Fragments associated with that Feature.\n\
-   Features may also include other Features ("Included Features"). Within a\n\
    Feature, files named "feature.xml" may contain a list of the names and\n\
    version numbers of Included Features.\n\
\n\
The terms and conditions governing Plug-ins and Fragments should be contained in\n\
files named "about.html" ("Abouts"). The terms and conditions governing Features\n\
and Included Features should be contained in files named "license.html"\n\
("Feature Licenses"). Abouts and Feature Licenses may be located in any\n\
directory of a Download or Module including, but not limited to the following\n\
locations:\n\
\n\
-   The top-level (root) directory\n\
-   Plug-in and Fragment directories\n\
-   Inside Plug-ins and Fragments packaged as JARs\n\
-   Sub-directories of the directory named "src" of certain Plug-ins\n\
-   Feature directories\n\
\n\
Note: if a Feature made available by the Eclipse Foundation is installed using\n\
the Provisioning Technology (as defined below), you must agree to a license\n\
("Feature Update License") during the installation process. If the Feature\n\
contains Included Features, the Feature Update License should either provide you\n\
with the terms and conditions governing the Included Features or inform you\n\
where you can locate them. Feature Update Licenses may be found in the "license"\n\
property of files named "feature.properties" found within a Feature. Such\n\
Abouts, Feature Licenses, and Feature Update Licenses contain the terms and\n\
conditions (or references to such terms and conditions) that govern your use of\n\
the associated Content in that directory.\n\
\n\
THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER TO THE EPL\n\
OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS. SOME OF THESE\n\
OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):\n\
\n\
-   Eclipse Public License Version 1.0 (available at\n\
    http://www.eclipse.org/legal/epl-v10.html)\n\
-   Eclipse Distribution License Version 1.0 (available at\n\
    http://www.eclipse.org/licenses/edl-v1.0.html)\n\
-   Common Public License Version 1.0 (available at\n\
    http://www.eclipse.org/legal/cpl-v10.html)\n\
-   Apache Software License 1.1 (available at\n\
    http://www.apache.org/licenses/LICENSE)\n\
-   Apache Software License 2.0 (available at\n\
    http://www.apache.org/licenses/LICENSE-2.0)\n\
-   Mozilla Public License Version 1.1 (available at\n\
    http://www.mozilla.org/MPL/MPL-1.1.html)\n\
\n\
IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR TO\n\
USE OF THE CONTENT. If no About, Feature License, or Feature Update License is\n\
provided, please contact the Eclipse Foundation to determine what terms and\n\
conditions govern that particular Content.\n\
\n\
Use of Provisioning Technology\n\
\n\
The Eclipse Foundation makes available provisioning software, examples of which\n\
include, but are not limited to, p2 and the Eclipse Update Manager\n\
("Provisioning Technology") for the purpose of allowing users to install\n\
software, documentation, information and/or other materials (collectively\n\
"Installable Software"). This capability is provided with the intent of allowing\n\
such users to install, extend and update Eclipse-based products. Information\n\
about packaging Installable Software is available at\n\
http://eclipse.org/equinox/p2/repository_packaging.html ("Specification").\n\
\n\
You may use Provisioning Technology to allow other parties to install\n\
Installable Software. You shall be responsible for enabling the applicable\n\
license agreements relating to the Installable Software to be presented to, and\n\
accepted by, the users of the Provisioning Technology in accordance with the\n\
Specification. By using Provisioning Technology in such a manner and making it\n\
available in accordance with the Specification, you further acknowledge your\n\
agreement to, and the acquisition of all necessary rights to permit the\n\
following:\n\
\n\
1.  A series of actions may occur ("Provisioning Process") in which a user may\n\
    execute the Provisioning Technology on a machine ("Target Machine") with the\n\
    intent of installing, extending or updating the functionality of an\n\
    Eclipse-based product.\n\
2.  During the Provisioning Process, the Provisioning Technology may cause third\n\
    party Installable Software or a portion thereof to be accessed and copied to\n\
    the Target Machine.\n\
3.  Pursuant to the Specification, you will provide to the user the terms and\n\
    conditions that govern the use of the Installable Software ("Installable\n\
    Software Agreement") and such Installable Software Agreement shall be\n\
    accessed from the Target Machine in accordance with the Specification. Such\n\
    Installable Software Agreement must inform the user of the terms and\n\
    conditions that govern the Installable Software and must solicit acceptance\n\
    by the end user in the manner prescribed in such Installable\n\
    Software Agreement. Upon such indication of agreement by the user, the\n\
    provisioning Technology will complete installation of the\n\
    Installable Software.\n\
\n\
Cryptography\n\
\n\
Content may contain encryption software. The country in which you are currently\n\
may have restrictions on the import, possession, and use, and/or re-export to\n\
another country, of encryption software. BEFORE using any encryption software,\n\
please check the country's laws, regulations and policies concerning the import,\n\
possession, or use, and re-export of encryption software, to see if this is\n\
permitted.\n\
\n\
Java and all Java-based trademarks are trademarks of Oracle Corporation in the\n\
United States, other countries, or both.\n
########### end of license property ##########################################

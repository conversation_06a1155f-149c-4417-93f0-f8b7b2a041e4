#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出生成模块
生成标准化的八字分析报告
"""

from datetime import datetime
from typing import Dict, Any, List, Optional

class OutputGenerator:
    def __init__(self):
        """初始化输出生成器"""
        self.report_template = self._init_report_template()
        self.quality_standards = self._init_quality_standards()
    
    def _init_report_template(self) -> Dict[str, Any]:
        """初始化报告模板"""
        return {
            'header': {
                'title': '八字命理分析报告',
                'subtitle': '基于传统命理学的现代化解读',
                'version': 'MVP-1.0'
            },
            'sections': [
                'basic_info',
                'bazi_info', 
                'wuxing_analysis',
                'personality_analysis',
                'career_analysis',
                'relationship_analysis',
                'health_analysis',
                'life_suggestions',
                'metadata'
            ]
        }
    
    def _init_quality_standards(self) -> Dict[str, Any]:
        """初始化质量标准"""
        return {
            'min_content_length': {
                'personality': 100,
                'career': 100,
                'relationship': 100,
                'health': 80
            },
            'max_content_length': {
                'personality': 500,
                'career': 500,
                'relationship': 500,
                'health': 400
            },
            'required_elements': [
                'basic_info',
                'bazi_info',
                'analysis'
            ]
        }
    
    def generate(self, calculation_result: Dict[str, Any], 
                matched_content: Dict[str, Any], 
                user_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终报告"""
        print("正在生成分析报告...")
        
        # 构建基本信息
        basic_info = self._build_basic_info(user_data)
        
        # 构建八字信息
        bazi_info = self._build_bazi_info(calculation_result)
        
        # 构建五行分析
        wuxing_analysis = self._build_wuxing_analysis(calculation_result)
        
        # 构建分析内容
        analysis_content = self._build_analysis_content(matched_content)
        
        # 构建建议内容
        suggestions = self._build_suggestions(matched_content)
        
        # 构建元数据
        metadata = self._build_metadata(calculation_result, user_data)
        
        # 组装最终报告
        final_report = {
            'name': basic_info.get('name', '未提供'),
            'gender': basic_info.get('gender', '未提供'),
            'birth_time': basic_info.get('birth_time', '未提供'),
            'birth_location': basic_info.get('birth_location', '未提供'),
            'bazi_info': bazi_info,
            'wuxing_analysis': wuxing_analysis,
            'analysis': analysis_content,
            'suggestions': suggestions,
            'metadata': metadata
        }
        
        # 质量检查
        quality_report = self._check_quality(final_report)
        final_report['quality_report'] = quality_report
        
        # 如果质量不达标，进行改进
        if not quality_report['is_acceptable']:
            final_report = self._improve_report_quality(final_report, quality_report)
        
        print("✅ 分析报告生成完成")
        return final_report
    
    def _build_basic_info(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建基本信息"""
        birth_time_str = "未知"
        
        if user_data.get('birth_hour') is not None:
            year = user_data.get('birth_year') or user_data.get('year')
            month = user_data.get('birth_month') or user_data.get('month')
            day = user_data.get('birth_day') or user_data.get('day')
            birth_time_str = f"{year}年{month}月{day}日 {user_data['birth_hour']}:{user_data.get('birth_minute', 0):02d}"
        else:
            year = user_data.get('birth_year') or user_data.get('year')
            month = user_data.get('birth_month') or user_data.get('month')
            day = user_data.get('birth_day') or user_data.get('day')
            birth_time_str = f"{year}年{month}月{day}日 {user_data.get('time_description', '时间未知')}"
        
        return {
            'name': user_data.get('name', '未提供'),
            'gender': user_data.get('gender', '未提供'),
            'birth_time': birth_time_str,
            'birth_location': user_data.get('birth_location', '未提供'),
            'time_certainty': user_data.get('time_certainty', 'unknown')
        }
    
    def _build_bazi_info(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建八字信息"""
        if calculation_result.get('analysis_type') == 'time_range':
            return {
                'analysis_type': '时间范围分析',
                'note': '由于出生时间不确定，以下为可能的八字范围分析',
                'year_pillar': '需要确定时间',
                'month_pillar': '需要确定时间',
                'day_pillar': '需要确定时间',
                'hour_pillar': '需要确定时间'
            }
        
        bazi_pillars = calculation_result.get('bazi_pillars', {})
        
        return {
            'year_pillar': ''.join(bazi_pillars.get('year_pillar', ('', ''))),
            'month_pillar': ''.join(bazi_pillars.get('month_pillar', ('', ''))),
            'day_pillar': ''.join(bazi_pillars.get('day_pillar', ('', ''))),
            'hour_pillar': ''.join(bazi_pillars.get('hour_pillar', ('', ''))),
            'analysis_type': '标准八字分析'
        }
    
    def _build_wuxing_analysis(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建五行分析"""
        if calculation_result.get('analysis_type') == 'time_range':
            return {
                'distribution': {'木': '?', '火': '?', '土': '?', '金': '?', '水': '?'},
                'strength': '需要确定出生时间',
                'useful_god': '需要确定出生时间',
                'note': '五行分析需要准确的出生时间'
            }
        
        wuxing_analysis = calculation_result.get('wuxing_analysis', {})
        
        return {
            'distribution': wuxing_analysis.get('distribution', {}),
            'strength': wuxing_analysis.get('strength_level', '未知'),
            'useful_god': wuxing_analysis.get('useful_god', '未知'),
            'avoid_god': wuxing_analysis.get('avoid_god', '未知'),
            'day_master_element': wuxing_analysis.get('day_master_element', '未知')
        }
    
    def _build_analysis_content(self, matched_content: Dict[str, Any]) -> Dict[str, Any]:
        """构建分析内容"""
        return {
            'personality': self._format_content(
                matched_content.get('personality', '暂无性格分析'),
                'personality'
            ),
            'career': self._format_content(
                matched_content.get('career', '暂无事业分析'),
                'career'
            ),
            'relationship': self._format_content(
                matched_content.get('relationship', '暂无感情分析'),
                'relationship'
            ),
            'health': self._format_content(
                matched_content.get('health', '暂无健康分析'),
                'health'
            )
        }
    
    def _build_suggestions(self, matched_content: Dict[str, Any]) -> List[str]:
        """构建建议内容"""
        suggestions = matched_content.get('suggestions', [])
        
        # 确保至少有3条建议
        if len(suggestions) < 3:
            default_suggestions = [
                "保持积极乐观的心态，相信自己的能力",
                "注重学习和自我提升，不断完善自己",
                "珍惜家人和朋友，维护良好的人际关系"
            ]
            suggestions.extend(default_suggestions[:3-len(suggestions)])
        
        return suggestions[:8]  # 最多8条建议
    
    def _build_metadata(self, calculation_result: Dict[str, Any], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建元数据"""
        calc_metadata = calculation_result.get('calculation_metadata', {})
        processing_metadata = user_data.get('processing_metadata', {})
        
        return {
            'analysis_time': datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'),
            'version': 'MVP-1.0',
            'accuracy': self._calculate_overall_accuracy(calc_metadata, processing_metadata),
            'data_quality': processing_metadata.get('data_quality', '未知'),
            'warnings': processing_metadata.get('warnings', []),
            'calculation_method': calc_metadata.get('time_used', '标准时间'),
            'analysis_type': calculation_result.get('analysis_type', 'normal')
        }
    
    def _format_content(self, content: str, content_type: str) -> str:
        """格式化内容"""
        if not content or content.strip() == '':
            return f"暂无{content_type}分析内容"
        
        # 确保内容长度符合标准
        min_length = self.quality_standards['min_content_length'].get(content_type, 50)
        max_length = self.quality_standards['max_content_length'].get(content_type, 300)
        
        if len(content) < min_length:
            # 内容太短，添加通用补充
            content += self._get_generic_supplement(content_type)
        elif len(content) > max_length:
            # 内容太长，进行截取
            content = content[:max_length-3] + "..."
        
        return content
    
    def _get_generic_supplement(self, content_type: str) -> str:
        """获取通用补充内容"""
        supplements = {
            'personality': "\n\n每个人都是独特的个体，以上分析仅供参考，请结合实际情况理解。",
            'career': "\n\n事业发展需要结合个人兴趣、能力和市场环境，建议多方面考虑。",
            'relationship': "\n\n感情需要双方共同经营，真诚和理解是维系关系的关键。",
            'health': "\n\n健康是最重要的财富，建议定期体检，保持良好的生活习惯。"
        }
        return supplements.get(content_type, "\n\n以上分析仅供参考，请结合实际情况理解。")
    
    def _check_quality(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """检查报告质量"""
        quality_issues = []
        
        # 检查必需元素
        for required_element in self.quality_standards['required_elements']:
            if required_element not in report or not report[required_element]:
                quality_issues.append(f"缺少必需元素: {required_element}")
        
        # 检查内容长度
        analysis = report.get('analysis', {})
        for content_type, min_length in self.quality_standards['min_content_length'].items():
            content = analysis.get(content_type, '')
            if len(content) < min_length:
                quality_issues.append(f"{content_type}内容过短 (当前: {len(content)}, 最少: {min_length})")
        
        # 检查建议数量
        suggestions = report.get('suggestions', [])
        if len(suggestions) < 3:
            quality_issues.append(f"建议数量不足 (当前: {len(suggestions)}, 最少: 3)")
        
        return {
            'is_acceptable': len(quality_issues) == 0,
            'issues': quality_issues,
            'quality_score': max(0, 100 - len(quality_issues) * 10)
        }
    
    def _improve_report_quality(self, report: Dict[str, Any], quality_report: Dict[str, Any]) -> Dict[str, Any]:
        """改进报告质量"""
        issues = quality_report.get('issues', [])
        
        for issue in issues:
            if '内容过短' in issue:
                # 处理内容过短问题
                content_type = issue.split('内容过短')[0]
                if content_type in report.get('analysis', {}):
                    current_content = report['analysis'][content_type]
                    supplement = self._get_generic_supplement(content_type)
                    report['analysis'][content_type] = current_content + supplement
            
            elif '建议数量不足' in issue:
                # 处理建议数量不足问题
                current_suggestions = report.get('suggestions', [])
                default_suggestions = [
                    "保持积极乐观的心态",
                    "注重学习和自我提升",
                    "珍惜人际关系",
                    "关注身体健康",
                    "制定明确的目标"
                ]
                
                needed_count = 3 - len(current_suggestions)
                report['suggestions'].extend(default_suggestions[:needed_count])
        
        return report
    
    def _calculate_overall_accuracy(self, calc_metadata: Dict[str, Any], processing_metadata: Dict[str, Any]) -> str:
        """计算整体准确度"""
        accuracy_factors = []
        
        # 计算精度因子
        calc_accuracy = calc_metadata.get('accuracy_level', '标准精度')
        if calc_accuracy == '高精度':
            accuracy_factors.append(95)
        elif calc_accuracy == '中等精度':
            accuracy_factors.append(80)
        elif calc_accuracy == '时间范围分析':
            accuracy_factors.append(60)
        else:
            accuracy_factors.append(75)
        
        # 数据质量因子
        data_quality = processing_metadata.get('data_quality', '一般')
        if data_quality == '优秀':
            accuracy_factors.append(95)
        elif data_quality == '良好':
            accuracy_factors.append(85)
        elif data_quality == '一般':
            accuracy_factors.append(70)
        else:
            accuracy_factors.append(60)
        
        # 计算平均准确度
        avg_accuracy = sum(accuracy_factors) / len(accuracy_factors)
        
        if avg_accuracy >= 90:
            return '高准确度'
        elif avg_accuracy >= 75:
            return '中等准确度'
        elif avg_accuracy >= 60:
            return '基础准确度'
        else:
            return '参考准确度'

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性检查器
确保同样的八字输入不会产生矛盾的分析结果
"""

from typing import Dict, List, Any, Tuple
from .core_logic_mapping import (
    get_rizhu_core_logic, 
    get_wuxing_pairing_core,
    TIANGAN_WUXING,
    DIZHI_WUXING,
    SHISHEN_CORE_TRAITS
)

class ConsistencyChecker:
    """一致性检查器，确保分析结果的逻辑一致性"""
    
    def __init__(self):
        self.forbidden_combinations = self._init_forbidden_combinations()
        self.required_alignments = self._init_required_alignments()
    
    def _init_forbidden_combinations(self) -> Dict[str, List[str]]:
        """初始化禁止的组合（会产生矛盾的内容）"""
        return {
            # 甲子日柱禁止的描述
            '甲子': [
                '性格急躁', '缺乏耐心', '五行缺水', '不利学习', 
                '脾气暴躁', '缺乏智慧', '根基不稳', '适应能力差'
            ],
            
            # 丙寅日柱禁止的描述  
            '丙寅': [
                '性格冷漠', '缺乏热情', '不善表达', '领导能力差',
                '木火不通', '智慧不足', '缺乏创新', '事业运差'
            ],
            
            # 庚午日柱禁止的描述
            '庚午': [
                '性格软弱', '缺乏决断', '竞争力差', '执行力不足',
                '过于温和', '缺乏魄力', '不敢竞争', '意志薄弱'
            ],
            
            # 癸酉日柱禁止的描述
            '癸酉': [
                '性格粗糙', '缺乏同情心', '不够纯真', '直觉迟钝',
                '金水不生', '智慧不足', '缺乏善良', '感受力差'
            ]
        }
    
    def _init_required_alignments(self) -> Dict[str, Dict[str, List[str]]]:
        """初始化必须对齐的内容（核心特征必须一致）"""
        return {
            '甲子': {
                'personality': ['坚韧', '智慧', '适应力强', '包容心'],
                'element_relation': ['水生木', '得水滋养'],
                'tendency': ['偏吉', '根基稳固'],
                'career': ['教育', '文化', '研究相关']
            },
            
            '丙寅': {
                'personality': ['热情', '开朗', '领导力', '感染力'],
                'element_relation': ['木生火', '木火通明'],
                'tendency': ['大吉', '智慧热情并存'],
                'career': ['教育', '媒体', '管理相关']
            },
            
            '庚午': {
                'personality': ['刚直', '果断', '执行力', '竞争意识'],
                'element_relation': ['火克金', '在冲突中成长'],
                'tendency': ['中平', '意志坚强'],
                'career': ['军警', '体育', '竞争性行业']
            },
            
            '癸酉': {
                'personality': ['纯真', '善良', '直觉敏锐', '同情心'],
                'element_relation': ['金生水', '金水相生'],
                'tendency': ['中吉', '纯净智慧并存'],
                'career': ['医疗', '慈善', '服务相关']
            }
        }
    
    def check_rizhu_consistency(self, day_pillar: str, analysis_content: str) -> Tuple[bool, List[str]]:
        """
        检查日柱分析的一致性
        
        Args:
            day_pillar: 日柱，如'甲子'
            analysis_content: 分析内容
            
        Returns:
            (是否一致, 错误信息列表)
        """
        errors = []
        
        # 检查禁止的描述
        if day_pillar in self.forbidden_combinations:
            for forbidden in self.forbidden_combinations[day_pillar]:
                if forbidden in analysis_content:
                    errors.append(f"包含禁止的描述: {forbidden}")
        
        # 检查必须对齐的内容
        core_logic = get_rizhu_core_logic(day_pillar)
        
        # 检查核心性格特征是否体现
        core_personalities = core_logic.get('core_personality', [])
        personality_mentioned = any(trait in analysis_content for trait in core_personalities)
        if not personality_mentioned and core_personalities:
            errors.append(f"未体现核心性格特征: {core_personalities}")
        
        # 检查五行关系是否正确
        element_relation = core_logic.get('core_element_relation', '')
        if element_relation and element_relation not in analysis_content:
            # 这里可以放宽要求，不一定要直接提到五行关系
            pass
        
        return len(errors) == 0, errors
    
    def check_wuxing_pairing_consistency(self, element1: str, element2: str, 
                                       analysis_content: str) -> Tuple[bool, List[str]]:
        """
        检查五行配对分析的一致性
        
        Args:
            element1: 第一个五行
            element2: 第二个五行  
            analysis_content: 分析内容
            
        Returns:
            (是否一致, 错误信息列表)
        """
        errors = []
        
        core_logic = get_wuxing_pairing_core(element1, element2)
        
        # 检查配对等级的一致性
        compatibility_level = core_logic.get('compatibility_level', '')
        stability_score = core_logic.get('stability_score', 60)
        
        # 根据稳定性分数检查描述是否一致
        if stability_score >= 85:
            # 高度匹配，不应该有负面描述
            negative_words = ['困难', '挑战很大', '不匹配', '相克严重', '矛盾重重']
            for word in negative_words:
                if word in analysis_content:
                    errors.append(f"高度匹配的配对不应包含: {word}")
        
        elif stability_score <= 60:
            # 需要努力的配对，不应该过于乐观
            overly_positive = ['完美匹配', '天作之合', '毫无问题', '绝佳配对']
            for word in overly_positive:
                if word in analysis_content:
                    errors.append(f"需要努力的配对不应包含: {word}")
        
        return len(errors) == 0, errors
    
    def check_overall_consistency(self, bazi_info: Dict[str, Any], 
                                analysis_result: str) -> Tuple[bool, List[str]]:
        """
        检查整体分析的一致性
        
        Args:
            bazi_info: 八字信息
            analysis_result: 分析结果
            
        Returns:
            (是否一致, 错误信息列表)
        """
        errors = []
        
        # 提取日柱信息
        day_pillar = bazi_info.get('day_pillar', '')
        if day_pillar:
            is_consistent, day_errors = self.check_rizhu_consistency(day_pillar, analysis_result)
            errors.extend(day_errors)
        
        # 检查五行一致性
        # 这里可以添加更多的整体一致性检查
        
        return len(errors) == 0, errors
    
    def validate_content_before_output(self, content_type: str, content: str, 
                                     context: Dict[str, Any]) -> Tuple[bool, str]:
        """
        在输出前验证内容的一致性
        
        Args:
            content_type: 内容类型 ('personality', 'compatibility', 'advice')
            content: 要输出的内容
            context: 上下文信息（包含八字信息等）
            
        Returns:
            (是否通过验证, 修正后的内容或错误信息)
        """
        if content_type == 'personality':
            day_pillar = context.get('day_pillar', '')
            if day_pillar:
                is_consistent, errors = self.check_rizhu_consistency(day_pillar, content)
                if not is_consistent:
                    # 尝试修正内容
                    corrected_content = self._correct_personality_content(day_pillar, content, errors)
                    return True, corrected_content
        
        elif content_type == 'compatibility':
            element1 = context.get('element1', '')
            element2 = context.get('element2', '')
            if element1 and element2:
                is_consistent, errors = self.check_wuxing_pairing_consistency(element1, element2, content)
                if not is_consistent:
                    # 尝试修正内容
                    corrected_content = self._correct_compatibility_content(element1, element2, content, errors)
                    return True, corrected_content
        
        return True, content
    
    def _correct_personality_content(self, day_pillar: str, content: str, errors: List[str]) -> str:
        """修正个性分析内容"""
        corrected = content
        
        # 移除禁止的描述
        if day_pillar in self.forbidden_combinations:
            for forbidden in self.forbidden_combinations[day_pillar]:
                corrected = corrected.replace(forbidden, '')
        
        # 确保包含核心特征
        core_logic = get_rizhu_core_logic(day_pillar)
        core_personalities = core_logic.get('core_personality', [])
        
        if core_personalities and not any(trait in corrected for trait in core_personalities):
            # 添加核心特征描述
            trait_desc = f"您具有{day_pillar}日的特质：{', '.join(core_personalities[:2])}。"
            corrected = trait_desc + corrected
        
        return corrected.strip()
    
    def _correct_compatibility_content(self, element1: str, element2: str, 
                                     content: str, errors: List[str]) -> str:
        """修正配对分析内容"""
        corrected = content
        
        core_logic = get_wuxing_pairing_core(element1, element2)
        compatibility_level = core_logic.get('compatibility_level', '')
        
        # 根据配对等级调整描述
        if '高度匹配' in compatibility_level:
            # 移除过于负面的描述
            negative_replacements = {
                '困难': '需要磨合',
                '挑战很大': '有成长空间', 
                '不匹配': '需要理解',
                '相克严重': '需要平衡'
            }
            for negative, positive in negative_replacements.items():
                corrected = corrected.replace(negative, positive)
        
        return corrected.strip()

# 全局一致性检查器实例
consistency_checker = ConsistencyChecker()

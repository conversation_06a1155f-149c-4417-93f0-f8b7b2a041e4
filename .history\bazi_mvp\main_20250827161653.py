#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生辰八字算命系统 MVP版本
主程序入口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.user_input import UserInputModule
from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator
from modules.corpus_matcher import CorpusMatcher
from modules.output_generator import OutputGenerator
from modules.couple_input import CoupleInputModule
from modules.couple_compatibility import CoupleCompatibilityCalculator
from utils.logger import setup_logger

class BaziMVPSystem:
    def __init__(self):
        """初始化八字算命系统"""
        self.logger = setup_logger()
        self.user_input = UserInputModule()
        self.data_processor = DataProcessor()
        self.bazi_calculator = BaziCalculator()
        self.corpus_matcher = CorpusMatcher()
        self.output_generator = OutputGenerator()
        self.couple_input = CoupleInputModule()
        self.couple_calculator = CoupleCompatibilityCalculator()
        
        self.logger.info("八字算命系统初始化完成")
    
    def run(self):
        """运行主程序"""
        print("=" * 60)
        print("🔮 生辰八字算命系统 MVP版本")
        print("=" * 60)
        print()
        
        try:
            # 第一步：用户输入
            print("📝 第一步：收集您的生辰信息")
            print("-" * 40)
            user_data = self.user_input.collect_birth_info()
            
            if not user_data:
                print("❌ 输入收集失败，程序退出")
                return
            
            # 第二步：数据处理
            print("\n🔄 第二步：处理和验证数据")
            print("-" * 40)
            processed_data = self.data_processor.process(user_data)
            
            # 第三步：八字计算
            print("\n⚡ 第三步：计算八字命理")
            print("-" * 40)
            calculation_result = self.bazi_calculator.calculate(processed_data)
            
            # 第四步：语料匹配
            print("\n📚 第四步：匹配分析内容")
            print("-" * 40)
            matched_content = self.corpus_matcher.match(calculation_result)
            
            # 第五步：生成输出
            print("\n📄 第五步：生成分析报告")
            print("-" * 40)
            final_report = self.output_generator.generate(
                calculation_result, matched_content, user_data
            )
            
            # 显示简短总结
            print("\n📄 第五步：生成分析总结")
            print("-" * 40)
            show_detailed = self.output_generator.display_summary(final_report)

            # 如果用户选择查看详细报告，则显示完整结果
            if show_detailed:
                print("\n📋 详细分析报告")
                print("=" * 60)
                self.display_detailed_result(final_report)
            
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，程序退出")
        except Exception as e:
            self.logger.error(f"系统运行错误: {e}")
            print(f"❌ 系统错误: {e}")
            print("请联系技术支持")
    
    def display_detailed_result(self, report):
        """显示详细分析结果"""
        
        # 基本信息
        print(f"\n📋 基本信息:")
        print(f"   姓名: {report.get('name', '未提供')}")
        print(f"   性别: {report.get('gender', '未提供')}")
        print(f"   阳历生日: {report.get('birth_time', '未提供')}")

        # 显示阴历信息（如果有）
        if report.get('lunar_info'):
            lunar_info = report['lunar_info']
            print(f"   阴历生日: {lunar_info['lunar_date_full']}")
            print(f"   转换精度: {lunar_info['conversion_accuracy']}")

        print(f"   出生地点: {report.get('birth_location', '未提供')}")
        
        # 八字信息
        bazi_info = report.get('bazi_info', {})
        print(f"\n🎴 八字排盘:")
        print(f"   年柱: {bazi_info.get('year_pillar', 'N/A')}")
        print(f"   月柱: {bazi_info.get('month_pillar', 'N/A')}")
        print(f"   日柱: {bazi_info.get('day_pillar', 'N/A')}")
        print(f"   时柱: {bazi_info.get('hour_pillar', 'N/A')}")
        
        # 五行分析
        wuxing_info = report.get('wuxing_analysis', {})
        print(f"\n🌟 五行分析:")
        distribution = wuxing_info.get('distribution', {})
        for element, count in distribution.items():
            print(f"   {element}: {count}个")
        print(f"   日主强弱: {wuxing_info.get('strength', 'N/A')}")
        print(f"   用神: {wuxing_info.get('useful_god', 'N/A')}")
        
        # 分析内容
        analysis = report.get('analysis', {})
        
        print(f"\n👤 性格特征:")
        personality = analysis.get('personality', '暂无分析')
        print(f"   {personality}")
        
        print(f"\n💼 事业运势:")
        career = analysis.get('career', '暂无分析')
        print(f"   {career}")
        
        print(f"\n💕 感情婚姻:")
        relationship = analysis.get('relationship', '暂无分析')
        print(f"   {relationship}")
        
        print(f"\n🏥 健康状况:")
        health = analysis.get('health', '暂无分析')
        print(f"   {health}")
        
        # 建议
        suggestions = report.get('suggestions', [])
        if suggestions:
            print(f"\n💡 人生建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion}")
        
        # 系统信息
        metadata = report.get('metadata', {})
        print(f"\n📊 分析信息:")
        print(f"   计算准确度: {metadata.get('accuracy', 'N/A')}")
        print(f"   分析时间: {metadata.get('analysis_time', 'N/A')}")
        print(f"   系统版本: {metadata.get('version', 'MVP-1.0')}")
        
        print("\n" + "=" * 60)
        print("✨ 分析完成！感谢使用八字算命系统")
        print("=" * 60)

def main():
    """主函数"""
    system = BaziMVPSystem()
    system.run()

if __name__ == "__main__":
    main()

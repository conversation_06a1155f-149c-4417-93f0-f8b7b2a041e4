#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的农历库测试
直接测试已安装的库
"""

from datetime import datetime, date

def test_available_libraries():
    """测试可用的农历库"""
    print("🌙 测试可用的农历库")
    print("=" * 50)
    
    # 测试日期
    test_dates = [
        (1988, 3, 15, "甲辰年测试"),
        (1990, 7, 22, "庚午年测试"),
        (2024, 1, 1, "2024年元旦"),
        (2024, 2, 10, "2024年春节"),
    ]
    
    libraries_tested = []
    
    # 测试 LunarCalendar
    print("\n1. 测试 LunarCalendar 库:")
    print("-" * 30)
    try:
        from LunarCalendar import Converter, Solar, Lunar
        
        for year, month, day, desc in test_dates:
            try:
                solar = Solar(year, month, day)
                lunar = Converter.Solar2Lunar(solar)
                
                print(f"  {desc}: {year}-{month:02d}-{day:02d}")
                print(f"    → 农历{lunar.year}年{lunar.month}月{lunar.day}日")
                
                # 尝试获取干支信息
                if hasattr(lunar, 'gz_year'):
                    print(f"    → 年干支: {lunar.gz_year}")
                if hasattr(lunar, 'gz_month'):
                    print(f"    → 月干支: {lunar.gz_month}")
                if hasattr(lunar, 'gz_day'):
                    print(f"    → 日干支: {lunar.gz_day}")
                print()
                
            except Exception as e:
                print(f"    转换失败: {e}")
        
        libraries_tested.append("LunarCalendar ✅")
        
    except ImportError:
        print("  ❌ LunarCalendar 库未安装")
        libraries_tested.append("LunarCalendar ❌")
    except Exception as e:
        print(f"  ❌ LunarCalendar 测试失败: {e}")
        libraries_tested.append("LunarCalendar ❌")
    
    # 测试 lunardate
    print("\n2. 测试 lunardate 库:")
    print("-" * 30)
    try:
        from lunardate import LunarDate
        
        for year, month, day, desc in test_dates:
            try:
                lunar_date = LunarDate.fromSolarDate(year, month, day)
                
                print(f"  {desc}: {year}-{month:02d}-{day:02d}")
                print(f"    → 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
                
                # 获取干支信息
                try:
                    print(f"    → 年干支: {lunar_date.gz_year()}")
                    print(f"    → 月干支: {lunar_date.gz_month()}")
                    print(f"    → 日干支: {lunar_date.gz_day()}")
                except:
                    print("    → 干支信息获取失败")
                print()
                
            except Exception as e:
                print(f"    转换失败: {e}")
        
        libraries_tested.append("lunardate ✅")
        
    except ImportError:
        print("  ❌ lunardate 库未安装")
        libraries_tested.append("lunardate ❌")
    except Exception as e:
        print(f"  ❌ lunardate 测试失败: {e}")
        libraries_tested.append("lunardate ❌")
    
    # 测试 chinese-calendar
    print("\n3. 测试 chinese-calendar 库:")
    print("-" * 30)
    try:
        import chinese_calendar as cc
        
        for year, month, day, desc in test_dates:
            try:
                test_date = date(year, month, day)
                is_holiday = cc.is_holiday(test_date)
                is_workday = cc.is_workday(test_date)
                
                print(f"  {desc}: {year}-{month:02d}-{day:02d}")
                if is_holiday:
                    print("    → 节假日")
                elif is_workday:
                    print("    → 工作日")
                else:
                    print("    → 周末")
                print()
                
            except Exception as e:
                print(f"    查询失败: {e}")
        
        libraries_tested.append("chinese-calendar ✅")
        
    except ImportError:
        print("  ❌ chinese-calendar 库未安装")
        libraries_tested.append("chinese-calendar ❌")
    except Exception as e:
        print(f"  ❌ chinese-calendar 测试失败: {e}")
        libraries_tested.append("chinese-calendar ❌")
    
    # 测试其他可能的库
    print("\n4. 测试其他农历库:")
    print("-" * 30)
    
    # 测试 zhdate
    try:
        import zhdate
        print("  ✅ zhdate 库可用")
        
        for year, month, day, desc in test_dates[:2]:  # 只测试前两个
            try:
                zh_date = zhdate.ZhDate.from_datetime(datetime(year, month, day))
                print(f"    {desc}: {zh_date}")
            except Exception as e:
                print(f"    zhdate转换失败: {e}")
        
        libraries_tested.append("zhdate ✅")
        
    except ImportError:
        print("  ❌ zhdate 库未安装")
        libraries_tested.append("zhdate ❌")
    
    # 测试 lunar_python
    try:
        import lunar_python
        print("  ✅ lunar_python 库可用")
        libraries_tested.append("lunar_python ✅")
    except ImportError:
        print("  ❌ lunar_python 库未安装")
        libraries_tested.append("lunar_python ❌")
    
    return libraries_tested

def compare_specific_date():
    """对比特定日期的不同库输出"""
    print("\n🔍 对比特定日期的输出")
    print("=" * 50)
    
    test_year, test_month, test_day = 1988, 3, 15
    print(f"测试日期: {test_year}-{test_month:02d}-{test_day:02d} (甲辰年)")
    print()
    
    results = {}
    
    # LunarCalendar
    try:
        from LunarCalendar import Converter, Solar
        solar = Solar(test_year, test_month, test_day)
        lunar = Converter.Solar2Lunar(solar)
        
        result = f"农历{lunar.year}年{lunar.month}月{lunar.day}日"
        if hasattr(lunar, 'gz_year'):
            result += f" ({lunar.gz_year}年)"
        if hasattr(lunar, 'gz_day'):
            result += f" 日柱:{lunar.gz_day}"
        
        results["LunarCalendar"] = result
        
    except Exception as e:
        results["LunarCalendar"] = f"失败: {e}"
    
    # lunardate
    try:
        from lunardate import LunarDate
        lunar_date = LunarDate.fromSolarDate(test_year, test_month, test_day)
        
        result = f"农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日"
        try:
            result += f" ({lunar_date.gz_year()}年)"
            result += f" 日柱:{lunar_date.gz_day()}"
        except:
            pass
        
        results["lunardate"] = result
        
    except Exception as e:
        results["lunardate"] = f"失败: {e}"
    
    # zhdate
    try:
        import zhdate
        zh_date = zhdate.ZhDate.from_datetime(datetime(test_year, test_month, test_day))
        results["zhdate"] = str(zh_date)
    except Exception as e:
        results["zhdate"] = f"失败: {e}"
    
    # 输出对比结果
    for lib_name, result in results.items():
        print(f"  {lib_name:15}: {result}")

def main():
    """主函数"""
    print("🌟 农历库功能测试和对比")
    print("=" * 60)
    
    # 测试可用库
    libraries_tested = test_available_libraries()
    
    # 对比特定日期
    compare_specific_date()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    for lib_status in libraries_tested:
        print(f"  {lib_status}")
    
    print("\n💡 建议:")
    successful_libs = [lib.split()[0] for lib in libraries_tested if "✅" in lib]
    
    if successful_libs:
        print(f"  可用的库: {', '.join(successful_libs)}")
        
        if "lunardate" in successful_libs:
            print("  🌟 推荐 lunardate: 提供完整的干支信息，适合八字计算")
        if "LunarCalendar" in successful_libs:
            print("  🌟 推荐 LunarCalendar: 功能全面，支持节气和节日")
        if "zhdate" in successful_libs:
            print("  🌟 推荐 zhdate: 简单易用的中文日期库")
    else:
        print("  ❌ 没有可用的农历库，需要先安装")
        print("  建议运行: pip install LunarCalendar lunardate zhdate")

if __name__ == "__main__":
    main()

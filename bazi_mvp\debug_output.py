#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试输出结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator
from modules.corpus_matcher import CorpusMatcher
from modules.output_generator import OutputGenerator
import json

def debug_output_structure():
    """调试输出数据结构"""
    print("🔍 调试输出数据结构...")
    
    # 示例数据
    sample_data = {
        'name': '测试用户',
        'gender': '男',
        'year': 1990,
        'month': 5,
        'day': 15,
        'hour': 14,
        'minute': 30,
        'birth_location': '北京市',
        'time_certainty': 'exact'
    }
    
    # 初始化模块
    data_processor = DataProcessor()
    bazi_calculator = BaziCalculator()
    corpus_matcher = CorpusMatcher()
    output_generator = OutputGenerator()
    
    # 处理数据
    print("1. 数据处理...")
    processed_data = data_processor.process(sample_data)
    print(f"   processed_data keys: {list(processed_data.keys())}")
    
    # 八字计算
    print("2. 八字计算...")
    calculation_result = bazi_calculator.calculate(processed_data)
    print(f"   calculation_result keys: {list(calculation_result.keys())}")
    
    # 语料匹配
    print("3. 语料匹配...")
    matched_content = corpus_matcher.match(calculation_result)
    print(f"   matched_content keys: {list(matched_content.keys())}")
    print(f"   matched_content: {matched_content}")
    
    # 输出生成
    print("4. 输出生成...")
    final_report = output_generator.generate(calculation_result, matched_content, processed_data)
    print(f"   final_report keys: {list(final_report.keys())}")
    
    # 详细检查每个部分
    print("\n📋 详细数据结构检查:")
    
    print(f"\n🎯 基本信息:")
    print(f"   name: {final_report.get('name')}")
    print(f"   gender: {final_report.get('gender')}")
    print(f"   birth_time: {final_report.get('birth_time')}")
    print(f"   birth_location: {final_report.get('birth_location')}")
    
    print(f"\n🎴 八字信息:")
    bazi_info = final_report.get('bazi_info', {})
    print(f"   bazi_info keys: {list(bazi_info.keys())}")
    print(f"   year_pillar: {bazi_info.get('year_pillar')}")
    print(f"   month_pillar: {bazi_info.get('month_pillar')}")
    print(f"   day_pillar: {bazi_info.get('day_pillar')}")
    print(f"   hour_pillar: {bazi_info.get('hour_pillar')}")
    
    print(f"\n🌟 五行分析:")
    wuxing_analysis = final_report.get('wuxing_analysis', {})
    print(f"   wuxing_analysis keys: {list(wuxing_analysis.keys())}")
    print(f"   distribution: {wuxing_analysis.get('distribution')}")
    print(f"   strength: {wuxing_analysis.get('strength')}")
    print(f"   useful_god: {wuxing_analysis.get('useful_god')}")
    
    print(f"\n📝 分析内容:")
    analysis = final_report.get('analysis', {})
    print(f"   analysis keys: {list(analysis.keys())}")
    print(f"   personality length: {len(analysis.get('personality', ''))}")
    print(f"   career length: {len(analysis.get('career', ''))}")
    print(f"   relationship length: {len(analysis.get('relationship', ''))}")
    print(f"   health length: {len(analysis.get('health', ''))}")
    
    print(f"\n   personality preview: {analysis.get('personality', '')[:100]}...")
    print(f"   career preview: {analysis.get('career', '')[:100]}...")
    print(f"   relationship preview: {analysis.get('relationship', '')[:100]}...")
    print(f"   health preview: {analysis.get('health', '')[:100]}...")
    
    print(f"\n💡 建议:")
    suggestions = final_report.get('suggestions', [])
    print(f"   suggestions count: {len(suggestions)}")
    for i, suggestion in enumerate(suggestions[:3], 1):
        print(f"   {i}. {suggestion}")
    
    print(f"\n📊 元数据:")
    metadata = final_report.get('metadata', {})
    print(f"   metadata keys: {list(metadata.keys())}")
    print(f"   accuracy: {metadata.get('accuracy')}")
    print(f"   data_quality: {metadata.get('data_quality')}")
    
    # 保存完整数据到文件以便检查
    with open('debug_report.json', 'w', encoding='utf-8') as f:
        json.dump(final_report, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 完整报告已保存到 debug_report.json")

if __name__ == "__main__":
    debug_output_structure()

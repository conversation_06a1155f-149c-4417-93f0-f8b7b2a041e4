#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版农历库对比测试
专门测试已安装的库：lunar_python, LunarCalendar
"""

import sys
import os

def test_lunar_python(year, month, day, hour=12):
    """测试lunar_python库"""
    print("🔍 测试 lunar_python 库")
    print("-" * 40)
    
    try:
        from lunar_python import Solar
        
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        eightChar = lunar.getEightChar()
        
        print("✅ 状态: 成功")
        print(f"📅 农历: {lunar.toString()}")
        print(f"🎯 年柱: {eightChar.getYear()} ({eightChar.getYearWuXing()}) - {eightChar.getYearShiShenGan()}")
        print(f"🎯 月柱: {eightChar.getMonth()} ({eightChar.getMonthWuXing()}) - {eightChar.getMonthShiShenGan()}")
        print(f"🎯 日柱: {eightChar.getDay()} ({eightChar.getDayWuXing()}) - {eightChar.getDayShiShenGan()}")
        print(f"🎯 时柱: {eightChar.getTime()} ({eightChar.getTimeWuXing()}) - {eightChar.getTimeShiShenGan()}")
        
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def test_lunar_calendar(year, month, day, hour=12):
    """测试LunarCalendar库"""
    print("\n🔍 测试 LunarCalendar 库")
    print("-" * 40)
    
    try:
        from LunarCalendar import Converter, Solar
        
        solar = Solar(year, month, day)
        lunar = Converter.Solar2Lunar(solar)
        
        print("✅ 状态: 成功")
        print(f"📅 农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查干支属性
        print("🔍 检查干支功能:")
        ganzhi_found = False
        
        # 检查常见的干支属性
        ganzhi_attrs = ['gz_year', 'gz_month', 'gz_day', 'gzYear', 'gzMonth', 'gzDay', 
                       'yearGanZhi', 'monthGanZhi', 'dayGanZhi']
        
        for attr in ganzhi_attrs:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    print(f"  {attr}: {value}")
                    ganzhi_found = True
                except Exception as e:
                    print(f"  {attr}: 获取失败 - {e}")
        
        if not ganzhi_found:
            print("  ❌ 不支持干支计算")
            
        # 显示所有可用属性
        print("📋 可用属性:")
        attrs = [attr for attr in dir(lunar) if not attr.startswith('_') and not callable(getattr(lunar, attr))]
        for attr in attrs[:8]:  # 只显示前8个
            try:
                value = getattr(lunar, attr)
                print(f"  {attr}: {value}")
            except:
                pass
        
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def test_other_libraries(year, month, day, hour=12):
    """测试其他可能的库"""
    print("\n🔍 测试其他库")
    print("-" * 40)
    
    # 测试zhdate
    try:
        import zhdate
        import datetime
        lunar = zhdate.ZhDate.from_datetime(datetime.date(year, month, day))
        print(f"✅ zhdate: 农历{lunar.lunar_year}年{lunar.lunar_month}月{lunar.lunar_day}日")
    except ImportError:
        print("❌ zhdate: 未安装")
    except Exception as e:
        print(f"❌ zhdate: 错误 - {e}")
    
    # 测试lunardate
    try:
        import lunardate
        lunar = lunardate.LunarDate.fromSolarDate(year, month, day)
        print(f"✅ lunardate: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
    except ImportError:
        print("❌ lunardate: 未安装")
    except Exception as e:
        print(f"❌ lunardate: 错误 - {e}")
    
    # 测试sxtwl
    try:
        import sxtwl
        lunar = sxtwl.fromSolar(year, month, day)
        print(f"✅ sxtwl: 天文历法库可用")
    except ImportError:
        print("❌ sxtwl: 未安装")
    except Exception as e:
        print(f"❌ sxtwl: 错误 - {e}")

def main():
    """主函数"""
    print("🌙 农历库对比测试工具")
    print("=" * 60)
    
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    while True:
        try:
            print("\n请输入要测试的日期:")
            
            # 获取用户输入
            date_input = input("日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
            
            if date_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            try:
                date_parts = date_input.split('-')
                if len(date_parts) != 3:
                    print("❌ 格式错误，请使用 YYYY-MM-DD")
                    continue
                
                year, month, day = map(int, date_parts)
                
                # 获取时辰
                hour_input = input("时辰 (0-23，默认12): ").strip()
                hour = int(hour_input) if hour_input else 12
                
            except ValueError:
                print("❌ 日期格式错误")
                continue
            
            print(f"\n{'='*60}")
            print(f"🌟 测试日期: {year}年{month}月{day}日 {hour}时")
            print(f"{'='*60}")
            
            # 测试主要库
            lunar_python_ok = test_lunar_python(year, month, day, hour)
            lunar_calendar_ok = test_lunar_calendar(year, month, day, hour)
            
            # 测试其他库
            test_other_libraries(year, month, day, hour)
            
            # 总结推荐
            print(f"\n{'='*60}")
            print("💡 推荐使用:")
            if lunar_python_ok:
                print("🌟 lunar_python - 完整的年月日时柱、五行、十神计算")
            elif lunar_calendar_ok:
                print("📦 LunarCalendar - 基础农历转换")
            else:
                print("❌ 没有可用的专业农历库")
            print(f"{'='*60}")
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()

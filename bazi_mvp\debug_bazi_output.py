#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试八字计算输出格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator

def debug_bazi_calculation():
    """调试八字计算过程"""
    print("🔍 调试八字计算输出格式")
    print("=" * 40)
    
    # 创建处理器
    data_processor = DataProcessor()
    bazi_calculator = BaziCalculator()
    
    # 模拟用户数据
    user_data = {
        'name': '测试用户',
        'gender': '男',
        'year': 1990,
        'month': 5,
        'day': 5,
        'hour': 1,
        'minute': 0,
        'birth_location': '东莞',
        'time_certainty': 'exact'
    }
    
    print("📝 输入数据:")
    for key, value in user_data.items():
        print(f"   {key}: {value}")
    
    # 处理数据
    print("\n🔄 数据处理...")
    processed_data = data_processor.process(user_data)
    
    print("📊 处理后数据:")
    for key, value in processed_data.items():
        print(f"   {key}: {value}")
    
    # 计算八字
    print("\n⚡ 八字计算...")
    bazi_result = bazi_calculator.calculate(processed_data)
    
    print("📋 八字计算结果结构:")
    for key, value in bazi_result.items():
        print(f"   {key}: {type(value)} - {value if not isinstance(value, dict) else '...'}")
        if isinstance(value, dict):
            for sub_key, sub_value in value.items():
                print(f"      {sub_key}: {sub_value}")
    
    # 测试提取函数
    print("\n🔧 测试数据提取...")
    
    def extract_bazi_for_compatibility(bazi_result, person_data):
        """提取八字数据用于合婚分析"""
        bazi_pillars = bazi_result.get('bazi_pillars', {})
        wuxing_analysis = bazi_result.get('wuxing_analysis', {})
        shishen_analysis = bazi_result.get('shishen_analysis', {})
        
        print(f"   bazi_pillars: {bazi_pillars}")
        print(f"   wuxing_analysis: {wuxing_analysis}")
        print(f"   shishen_analysis: {shishen_analysis}")
        
        # 构建合婚计算器需要的数据格式
        compatibility_data = {
            'name': person_data.get('name', '未知'),
            'year_pillar': bazi_pillars.get('year_pillar', ''),
            'month_pillar': bazi_pillars.get('month_pillar', ''),
            'day_pillar': bazi_pillars.get('day_pillar', ''),
            'hour_pillar': bazi_pillars.get('hour_pillar', ''),
            'day_master_element': wuxing_analysis.get('day_master_element', '未知'),
            'main_shishen': shishen_analysis.get('main_shishen', '未知')
        }
        
        return compatibility_data
    
    extracted_data = extract_bazi_for_compatibility(bazi_result, user_data)
    
    print("📤 提取的合婚数据:")
    for key, value in extracted_data.items():
        print(f"   {key}: {value}")

if __name__ == "__main__":
    debug_bazi_calculation()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统
提供系统运行日志记录功能
"""

import logging
import os
from datetime import datetime
from typing import Optional

def setup_logger(name: str = 'bazi_mvp', 
                log_level: str = 'INFO',
                log_file: Optional[str] = None) -> logging.Logger:
    """设置日志系统"""
    
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 如果没有指定日志文件，使用默认文件名
    if log_file is None:
        log_file = os.path.join(log_dir, f'bazi_mvp_{datetime.now().strftime("%Y%m%d")}.log')
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

class BaziLogger:
    """八字系统专用日志记录器"""
    
    def __init__(self, name: str = 'bazi_system'):
        self.logger = setup_logger(name)
    
    def log_user_input(self, user_data: dict):
        """记录用户输入（脱敏）"""
        safe_data = {
            'name': user_data.get('name', '未提供')[:1] + '*' if user_data.get('name') else '未提供',
            'gender': user_data.get('gender', '未提供'),
            'birth_year': user_data.get('year'),
            'birth_month': user_data.get('month'),
            'birth_day': user_data.get('day'),
            'time_certainty': user_data.get('time_certainty', 'unknown'),
            'location_provided': bool(user_data.get('birth_location'))
        }
        self.logger.info(f"用户输入收集完成: {safe_data}")
    
    def log_calculation_start(self, calculation_type: str):
        """记录计算开始"""
        self.logger.info(f"开始{calculation_type}计算")
    
    def log_calculation_complete(self, calculation_type: str, duration: float):
        """记录计算完成"""
        self.logger.info(f"{calculation_type}计算完成，耗时: {duration:.2f}秒")
    
    def log_error(self, error_type: str, error_message: str, context: dict = None):
        """记录错误"""
        error_info = {
            'error_type': error_type,
            'error_message': error_message,
            'context': context or {}
        }
        self.logger.error(f"系统错误: {error_info}")
    
    def log_quality_check(self, quality_score: float, issues: list):
        """记录质量检查结果"""
        self.logger.info(f"质量检查完成 - 得分: {quality_score}, 问题数: {len(issues)}")
        if issues:
            for issue in issues:
                self.logger.warning(f"质量问题: {issue}")
    
    def log_system_performance(self, module: str, execution_time: float, memory_usage: float = None):
        """记录系统性能"""
        perf_info = f"模块: {module}, 执行时间: {execution_time:.2f}秒"
        if memory_usage:
            perf_info += f", 内存使用: {memory_usage:.2f}MB"
        self.logger.debug(f"性能统计: {perf_info}")

import com.nlf.calendar.Solar;
import com.nlf.calendar.Lunar;
import java.util.Scanner;

/**
 * Basic Java lunar-calendar library test
 * Test basic lunar conversion
 */
public class BasicLunarTest {
    
    public static void main(String[] args) {
        System.out.println("Basic Java lunar-calendar Library Test");
        System.out.println("============================================================");
        
        // Test fixed dates first
        testFixedDates();
        
        // Interactive mode
        interactiveMode();
    }
    
    private static void testFixedDates() {
        System.out.println("\nDemo Cases:");
        
        int[][] testDates = {
            {1988, 3, 15, 12},
            {1990, 7, 22, 8},
            {2024, 1, 1, 12}
        };
        
        String[] descriptions = {
            "Test 1988-03-15",
            "Test 1990-07-22", 
            "Test 2024-01-01"
        };
        
        for (int i = 0; i < testDates.length; i++) {
            int[] date = testDates[i];
            System.out.println("\n" + descriptions[i] + ": " + 
                             date[0] + "-" + String.format("%02d", date[1]) + "-" + 
                             String.format("%02d", date[2]) + " " + date[3] + ":00");
            System.out.println("--------------------------------------------------");
            
            testLunarCalendar(date[0], date[1], date[2], date[3]);
        }
    }
    
    private static void interactiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n============================================================");
        System.out.println("Interactive Mode - Enter your date");
        
        while (true) {
            try {
                System.out.print("\nEnter date (YYYY-MM-DD) or 'quit' to exit: ");
                String input = scanner.nextLine().trim();
                
                if (input.toLowerCase().equals("quit") || input.toLowerCase().equals("q")) {
                    System.out.println("Goodbye!");
                    break;
                }
                
                // Parse date
                String[] parts = input.split("-");
                if (parts.length != 3) {
                    System.out.println("Error: Please use YYYY-MM-DD format");
                    continue;
                }
                
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                
                System.out.print("Hour (0-23, default 12): ");
                String hourInput = scanner.nextLine().trim();
                int hour = hourInput.isEmpty() ? 12 : Integer.parseInt(hourInput);
                
                System.out.println("\n============================================================");
                System.out.printf("Test Date: %d-%02d-%02d %02d:00%n", year, month, day, hour);
                System.out.println("============================================================");
                
                testLunarCalendar(year, month, day, hour);
                
            } catch (NumberFormatException e) {
                System.out.println("Error: Invalid date format");
            } catch (Exception e) {
                System.out.println("Error: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }
    
    private static void testLunarCalendar(int year, int month, int day, int hour) {
        System.out.println("Testing lunar-calendar library");
        System.out.println("----------------------------------------");
        
        try {
            // Create Solar object
            Solar solar = new Solar(year, month, day, hour, 0, 0);
            
            // Convert to Lunar
            Lunar lunar = solar.getLunar();
            
            System.out.println("Status: SUCCESS");
            System.out.println("Solar Date: " + solar.toString());
            System.out.println("Lunar Date: " + lunar.toString());
            
            // Try to get available methods
            System.out.println("\nAvailable Lunar Info:");
            System.out.println("Lunar Year: " + lunar.getYear());
            System.out.println("Lunar Month: " + lunar.getMonth());
            System.out.println("Lunar Day: " + lunar.getDay());
            
            // Try to get GanZhi if available
            try {
                System.out.println("\nTrying GanZhi methods:");
                System.out.println("Year GanZhi: " + lunar.getYearInGanZhi());
                System.out.println("Month GanZhi: " + lunar.getMonthInGanZhi());
                System.out.println("Day GanZhi: " + lunar.getDayInGanZhi());
                System.out.println("Time GanZhi: " + lunar.getTimeInGanZhi());
            } catch (Exception e) {
                System.out.println("GanZhi methods not available: " + e.getMessage());
            }
            
            // Try to get EightChar if available
            try {
                Object eightChar = lunar.getClass().getMethod("getEightChar").invoke(lunar);
                System.out.println("\nEightChar available: " + eightChar.toString());
                
                // Try to get pillars
                try {
                    Object yearPillar = eightChar.getClass().getMethod("getYear").invoke(eightChar);
                    Object monthPillar = eightChar.getClass().getMethod("getMonth").invoke(eightChar);
                    Object dayPillar = eightChar.getClass().getMethod("getDay").invoke(eightChar);
                    Object timePillar = eightChar.getClass().getMethod("getTime").invoke(eightChar);
                    
                    System.out.println("Year Pillar: " + yearPillar);
                    System.out.println("Month Pillar: " + monthPillar);
                    System.out.println("Day Pillar: " + dayPillar);
                    System.out.println("Time Pillar: " + timePillar);
                } catch (Exception e2) {
                    System.out.println("Pillar methods not available: " + e2.getMessage());
                }
                
            } catch (Exception e) {
                System.out.println("EightChar not available: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.out.println("Status: ERROR - " + e.getMessage());
            e.printStackTrace();
        }
    }
}

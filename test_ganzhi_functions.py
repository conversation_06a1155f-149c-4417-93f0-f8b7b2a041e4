#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试各个农历库的干支功能
详细检查每个库是否支持年柱、月柱、日柱查询
"""

from datetime import datetime, date

def test_zhdate_ganzhi():
    """测试zhdate的干支功能"""
    print("🔍 测试 zhdate 的干支功能")
    print("-" * 40)
    
    try:
        import zhdate
        
        test_date = datetime(1988, 3, 15)
        zh_date = zhdate.ZhDate.from_datetime(test_date)
        
        print(f"测试日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"农历: {zh_date}")
        
        # 检查所有可能的干支相关方法和属性
        ganzhi_methods = [
            'gz_year', 'gz_month', 'gz_day', 'gz_hour',
            'ganzhi_year', 'ganzhi_month', 'ganzhi_day', 'ganzhi_hour',
            'year_ganzhi', 'month_ganzhi', 'day_ganzhi', 'hour_ganzhi'
        ]
        
        print("\n检查干支方法:")
        for method in ganzhi_methods:
            if hasattr(zh_date, method):
                try:
                    attr = getattr(zh_date, method)
                    if callable(attr):
                        result = attr()
                        print(f"  ✅ {method}(): {result}")
                    else:
                        print(f"  ✅ {method}: {attr}")
                except Exception as e:
                    print(f"  ❌ {method}: 调用失败 - {e}")
            else:
                print(f"  ❌ {method}: 方法不存在")
        
        # 检查所有属性
        print("\n所有可用属性:")
        for attr in dir(zh_date):
            if not attr.startswith('_'):
                try:
                    value = getattr(zh_date, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        
        return True
        
    except ImportError:
        print("❌ zhdate 库未安装")
        return False
    except Exception as e:
        print(f"❌ zhdate 测试失败: {e}")
        return False

def test_lunardate_ganzhi():
    """测试lunardate的干支功能"""
    print("\n🔍 测试 lunardate 的干支功能")
    print("-" * 40)
    
    try:
        from lunardate import LunarDate
        
        lunar_date = LunarDate.fromSolarDate(1988, 3, 15)
        
        print(f"测试日期: 1988-03-15")
        print(f"农历: 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
        
        # 检查所有可能的干支相关方法和属性
        ganzhi_methods = [
            'gz_year', 'gz_month', 'gz_day', 'gz_hour',
            'ganzhi_year', 'ganzhi_month', 'ganzhi_day', 'ganzhi_hour',
            'year_ganzhi', 'month_ganzhi', 'day_ganzhi', 'hour_ganzhi'
        ]
        
        print("\n检查干支方法:")
        for method in ganzhi_methods:
            if hasattr(lunar_date, method):
                try:
                    attr = getattr(lunar_date, method)
                    if callable(attr):
                        result = attr()
                        print(f"  ✅ {method}(): {result}")
                    else:
                        print(f"  ✅ {method}: {attr}")
                except Exception as e:
                    print(f"  ❌ {method}: 调用失败 - {e}")
            else:
                print(f"  ❌ {method}: 方法不存在")
        
        # 检查所有属性
        print("\n所有可用属性:")
        for attr in dir(lunar_date):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar_date, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        
        # 检查所有方法
        print("\n所有可用方法:")
        for attr in dir(lunar_date):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar_date, attr)
                    if callable(value):
                        print(f"  {attr}()")
                except:
                    pass
        
        return True
        
    except ImportError:
        print("❌ lunardate 库未安装")
        return False
    except Exception as e:
        print(f"❌ lunardate 测试失败: {e}")
        return False

def test_lunarcalendar_ganzhi():
    """测试LunarCalendar的干支功能"""
    print("\n🔍 测试 LunarCalendar 的干支功能")
    print("-" * 40)
    
    try:
        from LunarCalendar import Converter, Solar
        
        solar = Solar(1988, 3, 15)
        lunar = Converter.Solar2Lunar(solar)
        
        print(f"测试日期: 1988-03-15")
        print(f"农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查所有可能的干支相关方法和属性
        ganzhi_methods = [
            'gz_year', 'gz_month', 'gz_day', 'gz_hour',
            'ganzhi_year', 'ganzhi_month', 'ganzhi_day', 'ganzhi_hour',
            'year_ganzhi', 'month_ganzhi', 'day_ganzhi', 'hour_ganzhi'
        ]
        
        print("\n检查干支方法:")
        for method in ganzhi_methods:
            if hasattr(lunar, method):
                try:
                    attr = getattr(lunar, method)
                    if callable(attr):
                        result = attr()
                        print(f"  ✅ {method}(): {result}")
                    else:
                        print(f"  ✅ {method}: {attr}")
                except Exception as e:
                    print(f"  ❌ {method}: 调用失败 - {e}")
            else:
                print(f"  ❌ {method}: 方法不存在")
        
        # 检查所有属性
        print("\n所有可用属性:")
        for attr in dir(lunar):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                except:
                    pass
        
        return True
        
    except ImportError:
        print("❌ LunarCalendar 库未安装")
        return False
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
        return False

def test_other_libraries():
    """测试其他可能的库"""
    print("\n🔍 测试其他可能的农历库")
    print("-" * 40)
    
    # 测试 chinese-calendar
    try:
        import chinese_calendar as cc
        print("✅ chinese-calendar 可用 (但主要用于节假日查询)")
    except ImportError:
        print("❌ chinese-calendar 未安装")
    
    # 测试 lunar_python
    try:
        import lunar_python
        print("✅ lunar_python 可用")
        # 尝试使用
        # 这里需要根据实际API来测试
    except ImportError:
        print("❌ lunar_python 未安装")
    
    # 测试 sxtwl (寿星天文历)
    try:
        import sxtwl
        print("✅ sxtwl 可用 (寿星天文历)")
        
        # 测试寿星天文历的干支功能
        lunar = sxtwl.Lunar()
        day = lunar.getDayBySolar(1988, 3, 15)
        
        print(f"  年柱: {day.getYearGZ()}")
        print(f"  月柱: {day.getMonthGZ()}")
        print(f"  日柱: {day.getDayGZ()}")
        
        return True
        
    except ImportError:
        print("❌ sxtwl 未安装")
    except Exception as e:
        print(f"❌ sxtwl 测试失败: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🌟 农历库干支功能详细测试")
    print("=" * 60)
    
    results = []
    
    # 测试各个库
    results.append(("zhdate", test_zhdate_ganzhi()))
    results.append(("lunardate", test_lunardate_ganzhi()))
    results.append(("LunarCalendar", test_lunarcalendar_ganzhi()))
    
    # 测试其他库
    print("\n" + "=" * 60)
    test_other_libraries()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for lib_name, success in results:
        status = "✅ 可用" if success else "❌ 不可用"
        print(f"  {lib_name:15}: {status}")
    
    print("\n💡 建议:")
    print("  如果没有库支持完整的干支功能，")
    print("  我们将创建一个自实现的干支计算脚本。")

if __name__ == "__main__":
    main()

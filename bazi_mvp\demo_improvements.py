#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示改进功能
展示新的用户输入处理和输出格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_calendar_conversion():
    """演示阳历阴历转换功能"""
    print("=" * 60)
    print("🌙 阳历阴历转换功能演示")
    print("=" * 60)
    
    from utils.calendar_converter import CalendarConverter
    cc = CalendarConverter()
    
    # 演示几个典型日期
    demo_dates = [
        (1990, 5, 15, "典型90后生日"),
        (2000, 1, 1, "千禧年元旦"),
        (1995, 8, 20, "夏末生日"),
        (2023, 2, 14, "情人节")
    ]
    
    for year, month, day, description in demo_dates:
        print(f"\n📅 {description}")
        print(f"阳历: {year}年{month}月{day}日")
        
        result = cc.solar_to_lunar(year, month, day)
        if result:
            print(f"阴历: {result['lunar_date_full']}")
            print(f"精度: {result['conversion_accuracy']}")
            print(f"干支年: {result['lunar_year_name']}")
        else:
            print("转换失败")

def demo_time_parsing():
    """演示时间解析功能"""
    print("\n" + "=" * 60)
    print("⏰ 智能时间解析功能演示")
    print("=" * 60)
    
    from modules.user_input import UserInputModule
    ui = UserInputModule()
    
    # 演示各种时间输入格式
    demo_times = [
        ("14:30", "标准24小时制"),
        ("下午2点30分", "12小时制描述"),
        ("晚上8点", "模糊时间"),
        ("凌晨3点", "凌晨时间"),
        ("上午", "时间段"),
        ("下午2-4点", "时间区间"),
        ("半夜12点", "特殊时间")
    ]
    
    for time_input, description in demo_times:
        print(f"\n🕐 {description}")
        print(f"输入: '{time_input}'")
        
        result = ui._parse_time_input(time_input)
        if result:
            print(f"解析: {result.get('time_description', '未知')}")
            if result.get('hour') is not None:
                print(f"24小时制: {result['hour']}:{result['minute']:02d}")
            print(f"精度: {result.get('time_certainty', '未知')}")
            if 'time_range' in result:
                time_range = result['time_range']
                print(f"范围: {time_range[0]}:00-{time_range[1]}:00")
        else:
            print("解析失败")

def demo_summary_output():
    """演示总结输出功能"""
    print("\n" + "=" * 60)
    print("📊 两阶段输出功能演示")
    print("=" * 60)
    
    from modules.output_generator import OutputGenerator
    
    # 模拟完整的报告数据
    mock_report = {
        'name': '张三',
        'gender': '男',
        'birth_time': '1990年5月15日 14:30',
        'birth_location': '北京市',
        'lunar_info': {
            'lunar_date_full': '农历庚午年四月廿二',
            'conversion_accuracy': 'high'
        },
        'summary': {
            'basic_profile': {
                'day_master': '甲木',
                'strength_level': '偏弱',
                'useful_god': '水木',
                'core_description': '您是甲木命，命格偏弱，宜用水木调候'
            },
            'personality_summary': '性格温和，具有很强的责任心和同情心，善于与人沟通...',
            'fortune_overview': {
                'career': '事业运势稳步上升，适合从事教育、咨询等工作...',
                'relationship': '感情运势良好，容易遇到合适的伴侣...',
                'health': '身体健康状况良好，注意肝胆保养...'
            },
            'key_suggestions': [
                '保持积极乐观的心态',
                '注重个人能力提升',
                '维护良好的人际关系'
            ],
            'overall_rating': {
                'scores': {
                    'career': 75,
                    'wealth': 70,
                    'relationship': 80,
                    'health': 85,
                    'overall': 77
                },
                'overall_level': '良好'
            }
        }
    }
    
    og = OutputGenerator()
    
    print("\n🎯 第一阶段：简短总结")
    print("-" * 40)
    
    # 显示简短总结（模拟）
    summary = mock_report['summary']
    
    print(f"👤 基本信息: {mock_report['name']} ({mock_report['gender']})")
    print(f"📅 出生: {mock_report['birth_time']}")
    print(f"🌙 农历: {mock_report['lunar_info']['lunar_date_full']}")
    
    print(f"\n🎴 命理概况:")
    print(f"   {summary['basic_profile']['core_description']}")
    
    print(f"\n👤 性格特征:")
    print(f"   {summary['personality_summary']}")
    
    print(f"\n🌟 运势概况:")
    fortune = summary['fortune_overview']
    print(f"   事业: {fortune['career']}")
    print(f"   感情: {fortune['relationship']}")
    print(f"   健康: {fortune['health']}")
    
    print(f"\n📊 综合评分:")
    scores = summary['overall_rating']['scores']
    print(f"   事业运势: {scores['career']}分")
    print(f"   财运状况: {scores['wealth']}分")
    print(f"   感情运势: {scores['relationship']}分")
    print(f"   健康状况: {scores['health']}分")
    print(f"   综合评分: {scores['overall']}分 ({summary['overall_rating']['overall_level']})")
    
    print(f"\n💡 关键建议:")
    for i, suggestion in enumerate(summary['key_suggestions'], 1):
        print(f"   {i}. {suggestion}")
    
    print("\n" + "=" * 40)
    print("💡 这就是新的两阶段输出格式！")
    print("   第一阶段：显示简洁总结")
    print("   第二阶段：用户选择后显示详细报告")

def main():
    """主演示函数"""
    print("🔮 生辰八字算命系统 - 功能改进演示")
    print("=" * 80)
    print("本演示展示以下改进功能：")
    print("1. 🌙 阳历阴历自动转换")
    print("2. ⏰ 智能时间输入解析")
    print("3. 📊 两阶段输出格式")
    print("=" * 80)
    
    try:
        # 演示阳历阴历转换
        demo_calendar_conversion()
        
        # 演示时间解析
        demo_time_parsing()
        
        # 演示总结输出
        demo_summary_output()
        
        print("\n" + "=" * 80)
        print("🎉 功能改进演示完成！")
        print("=" * 80)
        print("主要改进总结：")
        print("✅ 用户输入更智能：支持多种时间格式，自动容错")
        print("✅ 阳历阴历转换：自动转换并显示确认")
        print("✅ 两阶段输出：先简短总结，再详细报告")
        print("✅ 更好的用户体验：引导更清晰，操作更简单")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

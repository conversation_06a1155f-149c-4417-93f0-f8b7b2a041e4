<!doctype html>
<html lang="en"><head><meta charset="utf-8">
<title>Learning Chinese Five Elements Astrology Menu, Chart, Relationship, Weight, Score</title>
<meta name="description" content="Five Elements Menu includes Chart, Background, Relationships, Weights, Scores, Calendar - Learning Chinese Astrology">
<meta name="author" content="Chinese Fortune Calendar">
<meta name="robots" content="index,follow"> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">    
<link rel="canonical" href="https://www.chinesefortunecalendar.com/5Emenu.htm" />
<link rel="apple-touch-icon" href="Cache-icon/5E-192.png"/>
<link rel="shortcut icon" href="Cache-icon/5E-192.png">
<meta name="apple-mobile-web-app-title" content="Five Elements">     
<link rel="icon" href="https://www.chinesefortunecalendar.com/Images/Icon/5E.ico" type="image/x-icon">
<link rel="stylesheet" href="layout-99.css" type="text/css">   
<script async src="https://cdn.ampproject.org/v0.js"></script>
<script async custom-element="amp-ad" src="https://cdn.ampproject.org/v0/amp-ad-0.1.js"></script>
<style amp-custom="">
.check1::before {content:"\2756";color:#f66b5d;font-size:120%;line-height:2em;} 
.star9::before{content:'\2742';color:dodgerblue;font-size:175%;}
.ad-logo {min-height:205px;min-width:349px;margin:6px auto;text-align:center;max-width:100%;} 
</style>
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-714597-1"></script>
<script>
window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);}
gtag('js', new Date()); gtag('config', 'UA-714597-1');
</script>
    
<script type="application/ld+json">    
{
  "@context": "https://schema.org",
  "@type": "NewsArticle",
  "mainEntityOfPage": "https://www.chinesefortunecalendar.com/5Emenu.htm",
  "headline": "Chinese Five Elements Astrology Index Menu",
  "description": "Learning Chinese Astrology Five Elements Menu includes Chart, Background, Relationships, Weights, Scores, and Calendar",
  "datePublished": "2019-10-08T08:08:08-08:00", 
  "dateModified":  "2021-07-20T08:08:08-08:00",
  "image": { "@type": "ImageObject", "url": "https://www.chinesefortunecalendar.com/Images/5E/5Elements-1.png", "width": "256", "height": "256" },
  "author": { "@type": "Person", "name": "Master Allen Tsai" },
  "publisher": {
    "@type": "Organization",
    "name": "www.chinesefortunecalendar.com",
    "logo": {
      "@type": "ImageObject", "width": "1200", "height": "1200",
      "url": "https://www.chinesefortunecalendar.com/Images/CFC-logo1200.png"
    } 
  }
}
</script>
<script type="application/ld+json">   
{
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "name": "Five Elements",
  "itemListElement": [{
    "@type": "ListItem",
    "position": 1,
    "name": "Chinese Fortune Calendar",
    "item": "https://www.chinesefortunecalendar.com/"
  },{
    "@type": "ListItem",
    "position": 2,
    "name": "Five Elements",
    "item": "https://www.chinesefortunecalendar.com/5Emenu.htm"
  }]
}
</script> 
    
</head>
<body>    
<!-- Start Navbar -->
<header class="ampstart-headerbar fixed flex justify-start items-center top-0 left-0 right-0 pl1 noframe" style="background-color:#367F86">
<div class="iconbar mt1 mb1"><a href="SiteIndex.htm">&#x2630;</a> &nbsp; <a href="default.htm">Home</a>  
<span class=diamond2></span> <a href="5Elist.htm">5E Table</a> 
<span class=diamond2></span> <a href="5LE.htm">Lucky Element</a>  
&nbsp;<a href="Search.htm"><svg style="width:12px;height:12px;" role=img xmlns="http://www.w3.org/2000/svg" title="Search" viewBox="0 0 19.9 19.7"><g class="search-path" fill=none stroke=white stroke-width=3><path stroke-linecap="square" d="M18.5 18.3l-5.4-5.4" /><circle fill="transparent" cx=8 cy=8 r=7 /></g></svg></a>
</div>    
</header> 
<!-- End Navbar -->
<div class="main">

<h1 class="pb2 h1red">Learning Chinese Five Elements</h1>
    
<div class=ad2>  
<amp-ad layout="fixed" width="300" height="250" type="adsense" data-ad-client="ca-pub-5744504564740676" data-ad-slot="9730390405"></amp-ad>
</div>    
<script src="CFCLink/Icon-Link.js"></script>
    
<p class="my3 px3">This is Chinese Five Element Menu. You can find all Five Element theory, Calendar, weights, scores, charts and guides for Chinese horoscopes. It also includes the Chinese Stemp-Branch Zodiac calendars, and reveals the relationships between Zodiac signs and Five Elements.</p>    

<p class="my3 px3">Chinese Five Elements are Metal, Water, Wood, Fire and Earth. Five 
Elements are also divided into Yin and Yang (Female and Male). Ten elements are 
called Stems. Chinese Astrology calendar has 12 zodiac animal symbols, which are 
called Branches. Each zodiac sign contains <a href="5EinAnimals.htm">different ingredients of Five 
Elements</a>. Therefore, Five Elements are the basic foundation to learn Chinese 
Astrology.</p>
    
<p class="my3 px3">Chinese Astrology Birth Chart contains four pairs of Five Element 
Zodiac signs. We need to find out much much weights (scores) inside the birth 
chart. Then we can use Five Element balance theory to find our lucky element, 
color, season, time and places using the <a href="5Elist.htm">Five Elements Application Chart</a>. 
The following educational articles contain <a href="5EBasic.htm">Five Element Basic Background</a> 
and the Chinese astrology calendar application to find the Five Element weights.</p>
    
<div class=ad3><amp-ad width="100vw" height="320" type="adsense" data-ad-client="ca-pub-5744504564740676" data-ad-slot="3917865581" data-auto-format="rspv" data-full-width="true">
<div overflow=""></div></amp-ad></div>   


<a name="MENU"></a>
<h2 class="my3 h2teal">Five Elements Menu</h2> 
    
<div class="div-center">
<div class="ex12 ex6">
<h3 class="sky"><span class="fa sun2 fa-spin white pb1 pt1"></span> Five Elements</h3>    
<p class="check1"> <a href="Metal1.htm">Five Element - Metal</a></p>
<p class="check1"> <a href="Water1.htm">Five Element - Water</a></p>
<p class="check1"> <a href="Wood1.htm">Five Element - Wood</a></p>
<p class="check1"> <a href="Fire1.htm">Five Element - Fire</a></p>
<p class="check1"> <a href="Earth1.htm">Five Element - Earth</a></p>
<p class="check1"> <a href="5Elist.htm">Five Element - Table</a></p>
</div>        
<div class="ex12 ex6"> 
<h3 class=sky><span class="fa sun3 fa-spin white pb1 pt1"></span> Five Element Theory</h3>
<p class="check1"> <a href="5LE.htm">Lucky Elements</a></p>
<p class="check1"> <a href="5EBasic.htm">Five Element Basic Theory</a></p>    
<p class="check1"> <a href="5ERelation.htm">Five Element Relationships</a></p>
<p class="check1"> <a href="5EPersonalityMenu.htm">Five Element Personality</a></p>    
<p class="check1"> <a href="Five-Elements-Solar-System-Planets.htm">Five Planets</a></p>
<p class="check1"> <a href="StemRelation.htm">Yin-Yang Five Elements</a></p>
</div>  
    
<div class="ex12 ex6"> 
<h3 class=sky><span class="fa ring1 fa-spin white pb1 pt1"></span> Five Element Weights</h3>
<p class="check1"> <a href="cgi-bin/5EToday.asp">Today's Five Elements</a></p>
<p class="check1"> <a href="5EbyYear.htm">Yearly Five Element Scores</a></p>    
<p class="check1"> <a href="5EMonths.htm">Monthly Five Element Scores</a></p>
<p class="check1"> <a href="5EDate.htm">Daily Five Element Scores</a></p>    
<p class="check1"> <a href="5EinAnimals.htm">Five Elements in the Zodiacs</a></p>

</div>
    
<div class="ex12 ex6"> 
<h3 class=sky><span class="fa snow4 fa-spin white pb1 pt1"></span> Five Element Listing</h3>
<p class="check1"> <a href="Charts/5E105years.htm">Five Elements 1900-2005</a></p>
<p class="check1"> <a href="cgi-bin/5E15days.asp">Next 15 Day's Five Elements</a></p>    
<p class="check1"> <a href="5EThisMonths.htm">Current Month Five Elements</a></p>
<p class="check1"> <a href="Chineseastrology.htm">Birthday's Five Elements</a></p>
<p class="check1"> <a href="5E10years.htm">Five Element Weights for 10 Years</a></p>    
</div>

<div class="ex12 ex6" id=YYYY> 
<h3 class=sky><span class="fa snow1 fa-spin white pb1 pt1"></span> <span class=YYYY></span> Five Elements</h3>
<p class="check1"> <a href="2021-YearFiveElementChart.htm" class=href><span class=YYYY></span> Five Element Chart</a></p>
<p class="check1"> <a href="2021-ChineseHoroscope.htm" class=href><span class=YYYY></span> Five Element Horoscope</a></p>    
<p class="check1"> <a href="5EPersonalityQuiz.asp"><span class=YYYY></span> Five Elements Personality Quiz</a></p>   
<p class="check1"> <a href="Yearly6Gods.htm">Yearly Six Gods Calendar</a></p>    
<p class="check1"> <a href="SBmenu.htm">Menu of Stem-Branch</a></p>     
    
</div>

<div class="ex12 ex6"> 
<h3 class=sky><span class="fa sun1 fa-spin white pb1 pt1"></span> Yin Yang Five Elements</h3>
<p class="check1"> <a href="TenGods.htm">Ten Gods and Ten Stems</a></p>
<p class="check1"> <a href="6GRelation.htm">Six Gods Relationships</a></p>    
<p class="check1"> <a href="Yearly6Gods.htm">Yearly Six Gods Calendar</a></p>
<p class="check1"> <a href="ChineseZodiac/10Gods-Introduction.htm">Ten Gods Introduction</a></p>
<p class="check1"> <a href="ChineseZodiac/BirthChart.htm">Ten Gods Birth Chart</a></p>  
</div>
    
</div>    
  
<div class=clearfix></div>
   
<h2 class="my3 h2teal">Articles You Might Like</h2>     
<div class="row div-center">
<div class="col-12 col-6h adjleft" >
<p><img src="Images/AMLogo34.gif" width=30 height=34 alt="Love Heart"> <a href="Match1.htm" >Fast Love Match - Very Accurate</a></p>
<p><img src="Images/5EFTChart.GIF" width=32 height=24 alt="Chart" > <a href="Chineseastrology.htm">&nbsp;Your Rise and Fall Balance Chart</a></p>
<p><img src="Images/W-Rabbit.gif" width=29 height=29 alt="Rabbit"> <a href="YourSign.htm" >Your Chinese Horoscope Animal Sign</a></p>

<p><img src="Images/4Sign.gif" width=32 height=32 alt="Five Element Signs"> <a href="Astro/DailyChineseHoroscope.htm">Daily Chinese Horoscopes</a></p>
</div>    
<div class="col-12 col-6h adjleft" >    
<p><img src="Images/Horse32.gif" width=32 height=32 alt="Horse"> 
<a href="RiverDiagram.htm">River Diagram, Pre-Heavenly Pa Kua</a></p>    
<p><img src="Images/TurtleBK.gif" width=32 height=32 alt="Turtle"> 
<a href="TurtleLoShu.htm">Lo Shu Diagram, Post-Heavenly Pa Kua</a></p>
<p><img src="Images/Dragon/Dragon-Red32.png" style="max-width:100%" alt="Dragon Astrology"> <a href="IChingDragons.htm" >I-Ching Dragon and Chinese Astrology</a></p>    
<p><img src="Images/MasterTsai32.gif" width=32 height=32 alt="Master Tsai"> <a href="NewChineseastrology.htm">Master Tsai's New Chinese Astrology</a></p>
</div> 
</div> 
<div class=clearfix></div>
    
</div>
<div class="flex flex-column items-center pt1 pb1" style="background-color:#367F86;">
    
<p class="footertxt"><a href="default.htm">Home</a> &nbsp; <a href='About.htm' title='About Us'>About</a> &nbsp; 
<a href='Contact.asp' title='Contact Form'>Contact</a> &nbsp; 
<a href='disclaim.htm' title='Disclaimer'>Disclaimer</a> &nbsp; 
<a href='Privacy.htm' title='Privacy Policy'>Privacy</a> &nbsp; 
<a href='SiteIndex.htm' title='Site Index'>Sitemap</a> </p> 
</div>
<p class="foottxt">Copyright &#169; 1999-2021 Chinese Fortune Calendar All Right Reserved.</p> 
<script>
var i;
var cur = new Date();
var year=cur.getYear();
var mm=cur.getMonth();    
if (year < 2000) year=year+1900;
if (mm > 9) year=year+1;  
// window.onload = function() {
var x = document.getElementById("YYYY");
var y = x.getElementsByClassName("YYYY");
for (i = 0; i < y.length; i++) { y[i].innerHTML = year;}   
y = x.getElementsByClassName("href");
for (i = 0; i < y.length; i++) { y[i].href=y[i].href.split("2021").join(year);}  // y[i].href.replace("2021", year); 
// };
</script> 
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语料管理系统
实现多样化的语料输出，避免重复内容
"""

import random
from typing import List, Dict, Any, Optional
from .corpus_database import COUPLE_COMPATIBILITY_CORPUS
from .corpus.extended_corpus import (
    SHISHEN_DETAILED_CORPUS,
    GEJU_DETAILED_CORPUS,
    SHENSHA_DETAILED_CORPUS,
    DAYUN_LIUNIAN_CORPUS,
    RIZHU_DETAILED_CORPUS,
    WUXING_PAIRING_CORPUS,
    COMPATIBILITY_LEVEL_DETAILED,
    LIFE_ADVICE_CORPUS
)
from .core_logic_mapping import get_rizhu_core_logic, get_wuxing_pairing_core
from .consistency_checker import consistency_checker

class CorpusManager:
    """语料管理器，负责智能选择和组合语料内容"""
    
    def __init__(self):
        self.used_content = set()  # 记录已使用的内容，避免重复
        self.corpus_sources = {
            'basic': COUPLE_COMPATIBILITY_CORPUS,
            'shishen': SHISHEN_DETAILED_CORPUS,
            'geju': GEJU_DETAILED_CORPUS,
            'shensha': SHENSHA_DETAILED_CORPUS,
            'dayun': DAYUN_LIUNIAN_CORPUS,
            'rizhu': RIZHU_DETAILED_CORPUS,
            'wuxing': WUXING_PAIRING_CORPUS,
            'level': COMPATIBILITY_LEVEL_DETAILED,
            'advice': LIFE_ADVICE_CORPUS
        }
    
    def get_varied_content(self, category: str, subcategory: str, 
                          count: int = 1, avoid_recent: bool = True) -> List[str]:
        """
        获取多样化的语料内容
        
        Args:
            category: 语料类别
            subcategory: 子类别
            count: 需要的内容数量
            avoid_recent: 是否避免最近使用过的内容
        
        Returns:
            选中的语料内容列表
        """
        all_content = []
        
        # 从多个语料源收集内容
        for source_name, source_data in self.corpus_sources.items():
            content = self._extract_content(source_data, category, subcategory)
            if content:
                all_content.extend([(item, f"{source_name}_{category}_{subcategory}") 
                                  for item in content])
        
        if not all_content:
            return []
        
        # 过滤已使用的内容（如果需要）
        if avoid_recent:
            available_content = [
                (content, key) for content, key in all_content 
                if key not in self.used_content
            ]
            if not available_content:
                # 如果所有内容都用过了，清空使用记录重新开始
                self.used_content.clear()
                available_content = all_content
        else:
            available_content = all_content
        
        # 随机选择内容
        selected = random.sample(available_content, min(count, len(available_content)))
        
        # 记录已使用的内容
        for _, key in selected:
            self.used_content.add(key)
        
        return [content for content, _ in selected]
    
    def _extract_content(self, source_data: Dict, category: str, subcategory: str) -> List[str]:
        """从语料源中提取指定类别的内容"""
        try:
            if category in source_data:
                if isinstance(source_data[category], dict):
                    if subcategory in source_data[category]:
                        content = source_data[category][subcategory]
                        if isinstance(content, list):
                            return content
                        elif isinstance(content, str):
                            return [content]
                        elif isinstance(content, dict):
                            # 如果是字典，提取所有值
                            result = []
                            for value in content.values():
                                if isinstance(value, list):
                                    result.extend(value)
                                elif isinstance(value, str):
                                    result.append(value)
                            return result
            return []
        except (KeyError, TypeError):
            return []
    
    def get_personality_analysis(self, day_pillar: str, count: int = 2) -> List[str]:
        """获取个性分析内容（确保一致性）"""
        # 获取核心逻辑，确保分析的一致性
        core_logic = get_rizhu_core_logic(day_pillar)

        content = []

        # 从日柱详细分析中获取
        if day_pillar in RIZHU_DETAILED_CORPUS:
            personality_content = RIZHU_DETAILED_CORPUS[day_pillar].get('personality', [])
            content.extend(personality_content)

        # 从基础语料中获取
        basic_content = self.get_varied_content('day_pillar_specific_analysis', day_pillar, count=1)
        content.extend(basic_content)

        # 选择内容并进行一致性检查
        selected_content = []
        available_content = content.copy()

        for _ in range(min(count, len(available_content))):
            if not available_content:
                break

            # 随机选择一个内容
            chosen = random.choice(available_content)
            available_content.remove(chosen)

            # 进行一致性检查
            context = {'day_pillar': day_pillar}
            is_valid, corrected_content = consistency_checker.validate_content_before_output(
                'personality', chosen, context
            )

            if is_valid:
                selected_content.append(corrected_content)

        return selected_content if selected_content else [f"您具有{day_pillar}日的特质：{', '.join(core_logic.get('core_personality', ['待分析']))}。"]
    
    def get_compatibility_analysis(self, compatibility_level: str, count: int = 3) -> Dict[str, List[str]]:
        """获取合婚分析内容"""
        result = {
            'overall': [],
            'communication': [],
            'financial': [],
            'career': []
        }
        
        # 从详细等级分析中获取
        if compatibility_level in COMPATIBILITY_LEVEL_DETAILED:
            level_data = COMPATIBILITY_LEVEL_DETAILED[compatibility_level]
            result['overall'].extend(level_data.get('characteristics', []))
            result['overall'].extend(level_data.get('relationship_forecast', []))
        
        # 从基础语料中获取具体建议
        basic_suggestions = self.get_varied_content('specific_suggestions_by_compatibility', 
                                                   compatibility_level, count=count)
        if basic_suggestions:
            # 解析基础建议中的不同类型
            for suggestion in basic_suggestions:
                if '沟通' in suggestion or '交流' in suggestion:
                    result['communication'].append(suggestion)
                elif '财务' in suggestion or '理财' in suggestion or '金钱' in suggestion:
                    result['financial'].append(suggestion)
                elif '事业' in suggestion or '工作' in suggestion or '职业' in suggestion:
                    result['career'].append(suggestion)
                else:
                    result['overall'].append(suggestion)
        
        return result
    
    def get_wuxing_pairing_analysis(self, element1: str, element2: str) -> Dict[str, Any]:
        """获取五行配对分析（确保一致性）"""
        # 获取核心逻辑，确保分析的一致性
        core_logic = get_wuxing_pairing_core(element1, element2)

        pairing_key = f"{element1}{element2}"
        reverse_key = f"{element2}{element1}"

        # 查找配对分析
        pairing_data = None
        if pairing_key in WUXING_PAIRING_CORPUS:
            pairing_data = WUXING_PAIRING_CORPUS[pairing_key]
        elif reverse_key in WUXING_PAIRING_CORPUS:
            pairing_data = WUXING_PAIRING_CORPUS[reverse_key]

        if pairing_data:
            # 选择描述并进行一致性检查
            descriptions = pairing_data.get('description', ['五行配对分析'])
            chosen_description = random.choice(descriptions)

            # 一致性检查
            context = {'element1': element1, 'element2': element2}
            is_valid, corrected_description = consistency_checker.validate_content_before_output(
                'compatibility', chosen_description, context
            )

            return {
                'compatibility': core_logic.get('compatibility_level', pairing_data.get('compatibility', '中等匹配')),
                'description': corrected_description,
                'advantages': random.sample(pairing_data.get('advantages', []),
                                          min(2, len(pairing_data.get('advantages', [])))),
                'suggestions': random.sample(pairing_data.get('suggestions', []),
                                           min(2, len(pairing_data.get('suggestions', []))))
            }

        # 使用核心逻辑提供默认分析
        return {
            'compatibility': core_logic.get('compatibility_level', '需要磨合'),
            'description': f"这种{core_logic.get('relation_type', '五行')}组合{core_logic.get('core_advantage', '需要双方的理解和包容')}。",
            'advantages': [core_logic.get('core_advantage', '每种组合都有其独特的优势，需要用心发现')],
            'suggestions': ['多沟通交流，寻找彼此的共同点。']
        }
    
    def get_life_advice(self, element: str, advice_type: str, count: int = 3) -> List[str]:
        """获取生活建议"""
        element_key = f"{element}命人"
        
        if advice_type in LIFE_ADVICE_CORPUS and element_key in LIFE_ADVICE_CORPUS[advice_type]:
            advice_list = LIFE_ADVICE_CORPUS[advice_type][element_key]
            return random.sample(advice_list, min(count, len(advice_list)))
        
        return []
    
    def reset_usage_history(self):
        """重置使用历史，允许重复使用内容"""
        self.used_content.clear()

# 全局语料管理器实例
corpus_manager = CorpusManager()

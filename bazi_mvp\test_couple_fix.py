#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合婚功能修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_couple_compatibility():
    """测试合婚计算功能"""
    try:
        from modules.couple_compatibility import CoupleCompatibilityCalculator
        from data.corpus_database import get_random_content, COUPLE_COMPATIBILITY_CORPUS
        
        print("💕 测试八字合婚计算功能")
        print("=" * 40)
        
        # 创建合婚计算器
        calculator = CoupleCompatibilityCalculator()
        print("✅ 合婚计算器创建成功")
        
        # 模拟八字数据
        male_bazi = {
            'name': '张三',
            'year_pillar': '庚午',
            'month_pillar': '辛巳', 
            'day_pillar': '甲子',
            'hour_pillar': '辛未',
            'day_master_element': '甲木',
            'main_shishen': '正官'
        }
        
        female_bazi = {
            'name': '李四',
            'year_pillar': '辛未',
            'month_pillar': '壬午',
            'day_pillar': '乙丑', 
            'hour_pillar': '壬申',
            'day_master_element': '乙木',
            'main_shishen': '正财'
        }
        
        print("✅ 模拟八字数据准备完成")
        
        # 测试合婚计算
        result = calculator.calculate_compatibility(male_bazi, female_bazi)
        
        print("✅ 合婚计算完成")
        print(f"   综合评分: {result['overall_score']}分")
        print(f"   配对等级: {result['compatibility_level']}")
        print(f"   建议数量: {len(result['suggestions'])}条")
        
        # 测试随机语料功能
        print("\n🎲 测试随机语料功能")
        print("=" * 40)
        
        # 测试配对等级描述
        level = '非常匹配'
        descriptions = COUPLE_COMPATIBILITY_CORPUS['compatibility_levels'][level]
        random_desc = get_random_content(descriptions)
        print(f"随机描述: {random_desc}")
        
        return True
        
    except Exception as e:
        print(f"❌ 合婚功能测试出错：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_calendar_conversion():
    """测试阳历阴历转换修复"""
    try:
        from utils.calendar_converter import CalendarConverter
        
        print("\n🌙 测试阳历阴历转换修复")
        print("=" * 40)
        
        cc = CalendarConverter()
        
        # 测试您提到的日期：1990年9月2日应该是农历七月十四
        result = cc.solar_to_lunar(1990, 9, 2)
        
        print(f"测试日期：1990年9月2日")
        
        if result:
            print(f"转换结果：{result['lunar_date_full']}")
            print(f"农历月份：{result['lunar_month']}月")
            print(f"农历日期：{result['lunar_day']}日")
            
            # 验证是否正确
            if result['lunar_month'] == 7 and result['lunar_day'] == 14:
                print("✅ 转换结果正确！")
                return True
            else:
                print(f"❌ 转换结果仍有问题，应该是七月十四")
                return False
        else:
            print("❌ 转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 阳历转换测试出错：{e}")
        import traceback
        traceback.print_exc()
        return False

def test_random_corpus():
    """测试随机语料功能"""
    try:
        from data.corpus_database import get_random_content, get_random_suggestions
        
        print("\n🎲 测试随机语料选择功能")
        print("=" * 40)
        
        # 测试单个内容随机选择
        content_list = [
            "这是第一种表述方式",
            "这是第二种表述方式", 
            "这是第三种表述方式"
        ]
        
        print("测试内容随机选择:")
        for i in range(3):
            random_content = get_random_content(content_list)
            print(f"  第{i+1}次: {random_content}")
        
        # 测试建议随机选择
        suggestions_list = [
            ["建议一A", "建议一B", "建议一C"],
            ["建议二A", "建议二B", "建议二C"],
            ["建议三A", "建议三B", "建议三C"]
        ]
        
        print("\n测试建议随机选择:")
        random_suggestions = get_random_suggestions(suggestions_list)
        print(f"  随机选择的建议组: {random_suggestions}")
        
        print("✅ 随机语料功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 随机语料测试出错：{e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔮 系统修复功能测试")
    print("=" * 50)
    
    tests = [
        ("阳历阴历转换修复", test_calendar_conversion),
        ("八字合婚功能", test_couple_compatibility),
        ("随机语料功能", test_random_corpus)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试：{test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复测试通过！")
        print("\n📋 修复总结：")
        print("1. ✅ 合婚分析 'final_score' 错误已修复")
        print("2. ✅ 语料库内容已丰富，支持随机选择")
        print("3. ✅ 阳历阴历转换精度已改进")
        print("4. ✅ 合婚专用语料库已添加")
    else:
        print("⚠️  部分功能需要进一步检查")

if __name__ == "__main__":
    main()

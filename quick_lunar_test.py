#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速农历库测试
"""

print("🌟 快速农历库测试")
print("=" * 40)

# 测试日期
test_dates = [
    (1988, 3, 15, "甲辰年测试"),
    (1990, 7, 22, "庚午年测试"),
    (2024, 1, 1, "2024年元旦"),
]

print("📅 测试日期:")
for year, month, day, desc in test_dates:
    print(f"  {desc}: {year}-{month:02d}-{day:02d}")

print("\n🔍 检查可用的农历库:")

# 1. 测试 zhdate
print("\n1. 测试 zhdate:")
try:
    import zhdate
    print("  ✅ zhdate 可用")
    
    for year, month, day, desc in test_dates:
        try:
            from datetime import datetime
            dt = datetime(year, month, day)
            zh_date = zhdate.ZhDate.from_datetime(dt)
            print(f"    {desc}: {zh_date}")
        except Exception as e:
            print(f"    {desc}: 转换失败 - {e}")
            
except ImportError:
    print("  ❌ zhdate 未安装")
except Exception as e:
    print(f"  ❌ zhdate 错误: {e}")

# 2. 测试 lunardate
print("\n2. 测试 lunardate:")
try:
    from lunardate import LunarDate
    print("  ✅ lunardate 可用")
    
    for year, month, day, desc in test_dates:
        try:
            lunar_date = LunarDate.fromSolarDate(year, month, day)
            print(f"    {desc}: 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
            try:
                print(f"      日柱: {lunar_date.gz_day()}")
            except:
                print("      日柱: 获取失败")
        except Exception as e:
            print(f"    {desc}: 转换失败 - {e}")
            
except ImportError:
    print("  ❌ lunardate 未安装")
except Exception as e:
    print(f"  ❌ lunardate 错误: {e}")

# 3. 测试 LunarCalendar
print("\n3. 测试 LunarCalendar:")
try:
    from LunarCalendar import Converter, Solar
    print("  ✅ LunarCalendar 可用")
    
    for year, month, day, desc in test_dates:
        try:
            solar = Solar(year, month, day)
            lunar = Converter.Solar2Lunar(solar)
            print(f"    {desc}: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
            if hasattr(lunar, 'gz_day'):
                print(f"      日柱: {lunar.gz_day}")
        except Exception as e:
            print(f"    {desc}: 转换失败 - {e}")
            
except ImportError:
    print("  ❌ LunarCalendar 未安装")
except Exception as e:
    print(f"  ❌ LunarCalendar 错误: {e}")

# 4. 测试 chinese-calendar
print("\n4. 测试 chinese-calendar:")
try:
    import chinese_calendar as cc
    from datetime import date
    print("  ✅ chinese-calendar 可用")
    
    for year, month, day, desc in test_dates:
        try:
            test_date = date(year, month, day)
            is_holiday = cc.is_holiday(test_date)
            print(f"    {desc}: {'节假日' if is_holiday else '非节假日'}")
        except Exception as e:
            print(f"    {desc}: 查询失败 - {e}")
            
except ImportError:
    print("  ❌ chinese-calendar 未安装")
except Exception as e:
    print(f"  ❌ chinese-calendar 错误: {e}")

print("\n" + "=" * 40)
print("💡 如果没有可用的库，请运行以下命令安装:")
print("  pip install zhdate")
print("  pip install lunardate") 
print("  pip install LunarCalendar")
print("  pip install chinese-calendar")
print("=" * 40)

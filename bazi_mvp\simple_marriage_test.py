#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的合婚分析测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_marriage_analysis():
    """测试基本合婚分析功能"""
    try:
        print("🔮 测试增强的合婚分析系统")
        print("=" * 50)
        
        # 导入模块
        from modules.couple_compatibility import CoupleCompatibilityCalculator
        print("✅ 合婚计算器导入成功")
        
        # 测试数据
        male_bazi = {
            'name': '张先生',
            'birth_date': '1988-03-15 09:30',
            'year_pillar': '戊辰',
            'month_pillar': '乙卯',
            'day_pillar': '甲子',
            'hour_pillar': '己巳'
        }
        
        female_bazi = {
            'name': '李女士',
            'birth_date': '1990-07-22 15:45',
            'year_pillar': '庚午',
            'month_pillar': '癸未',
            'day_pillar': '丁卯',
            'hour_pillar': '戊申'
        }
        
        print(f"👨 男方：{male_bazi['name']} - {male_bazi['day_pillar']}")
        print(f"👩 女方：{female_bazi['name']} - {female_bazi['day_pillar']}")
        
        # 进行合婚分析
        calculator = CoupleCompatibilityCalculator()
        result = calculator.calculate_compatibility(male_bazi, female_bazi)
        
        print(f"\n📊 分析结果：")
        print(f"综合评分：{result['overall_score']}分")
        print(f"配对等级：{result['compatibility_level']}")
        
        print(f"\n📋 详细分析报告：")
        print(result['detailed_analysis'][:500] + "..." if len(result['detailed_analysis']) > 500 else result['detailed_analysis'])
        
        print("\n✅ 合婚分析测试成功！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_couples():
    """测试不同夫妻的差异化分析"""
    try:
        print("\n🎯 测试不同夫妻的差异化分析")
        print("=" * 50)
        
        from modules.couple_compatibility import CoupleCompatibilityCalculator
        
        # 测试案例1：甲子 + 丁卯
        couple1 = (
            {'name': '张三', 'day_pillar': '甲子', 'birth_date': '1988-03-15'},
            {'name': '李四', 'day_pillar': '丁卯', 'birth_date': '1990-07-22'}
        )
        
        # 测试案例2：丙寅 + 戊辰
        couple2 = (
            {'name': '王五', 'day_pillar': '丙寅', 'birth_date': '1985-06-10'},
            {'name': '赵六', 'day_pillar': '戊辰', 'birth_date': '1987-11-08'}
        )
        
        calculator = CoupleCompatibilityCalculator()
        
        for i, (male, female) in enumerate([couple1, couple2], 1):
            print(f"\n案例{i}：{male['name']}({male['day_pillar']}) + {female['name']}({female['day_pillar']})")
            
            # 补充必要的八字信息
            male_full = {**male, 'year_pillar': '戊辰', 'month_pillar': '乙卯', 'hour_pillar': '己巳'}
            female_full = {**female, 'year_pillar': '庚午', 'month_pillar': '癸未', 'hour_pillar': '戊申'}
            
            result = calculator.calculate_compatibility(male_full, female_full)
            print(f"评分：{result['overall_score']}分 - {result['compatibility_level']}")
            
            # 显示部分分析内容
            analysis_preview = result['detailed_analysis'][:200] + "..."
            print(f"分析预览：{analysis_preview}")
        
        print("\n✅ 差异化分析测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 差异化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌟 增强合婚分析系统简化测试")
    print()
    
    success_count = 0
    total_tests = 2
    
    # 测试基本功能
    if test_basic_marriage_analysis():
        success_count += 1
    
    # 测试差异化分析
    if test_different_couples():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果：{success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！增强合婚分析系统运行正常")
        print("\n✅ 主要改进：")
        print("  • 大幅扩充合婚专用语料库")
        print("  • 基于日柱组合的差异化分析")
        print("  • 重新设计的清晰报告框架")
        print("  • 多维度的专业分析内容")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()

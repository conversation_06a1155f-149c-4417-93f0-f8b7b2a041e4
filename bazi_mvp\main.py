#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生辰八字算命系统 MVP版本
主程序入口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.user_input import UserInputModule
from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator
from modules.corpus_matcher import CorpusMatcher
from modules.output_generator import OutputGenerator
from modules.couple_input import CoupleInputModule
from modules.couple_compatibility import CoupleCompatibilityCalculator
from utils.logger import setup_logger

class BaziMVPSystem:
    def __init__(self):
        """初始化八字算命系统"""
        self.logger = setup_logger()
        self.user_input = UserInputModule()
        self.data_processor = DataProcessor()
        self.bazi_calculator = BaziCalculator()
        self.corpus_matcher = CorpusMatcher()
        self.output_generator = OutputGenerator()
        self.couple_input = CoupleInputModule()
        self.couple_calculator = CoupleCompatibilityCalculator()
        
        self.logger.info("八字算命系统初始化完成")
    
    def run(self):
        """运行主程序"""
        print("=" * 60)
        print("🔮 生辰八字算命系统 MVP版本")
        print("=" * 60)
        print()

        try:
            # 选择分析模式
            analysis_mode = self.couple_input.get_analysis_mode_choice()

            if analysis_mode == 'single':
                self.run_single_analysis()
            elif analysis_mode == 'couple':
                self.run_couple_analysis()

        except KeyboardInterrupt:
            print("\n\n👋 感谢使用，再见！")
        except Exception as e:
            self.logger.error(f"程序运行错误: {e}")
            print(f"❌ 程序运行出错: {e}")

    def run_single_analysis(self):
        """运行个人八字分析"""
        try:
            # 第一步：用户输入
            print("\n📝 第一步：收集您的生辰信息")
            print("-" * 40)
            user_data = self.user_input.collect_birth_info()

            if not user_data:
                print("❌ 输入收集失败，程序退出")
                return

            # 第二步：数据处理
            print("\n🔄 第二步：处理和验证数据")
            print("-" * 40)
            processed_data = self.data_processor.process(user_data)

            # 第三步：八字计算
            print("\n⚡ 第三步：计算八字命理")
            print("-" * 40)
            calculation_result = self.bazi_calculator.calculate(processed_data)

            # 第四步：语料匹配
            print("\n📚 第四步：匹配分析内容")
            print("-" * 40)
            matched_content = self.corpus_matcher.match(calculation_result)

            # 第五步：生成输出
            print("\n📄 第五步：生成分析报告")
            print("-" * 40)
            final_report = self.output_generator.generate(
                calculation_result, matched_content, user_data
            )

            # 显示简短总结
            print("\n📄 第五步：生成分析总结")
            print("-" * 40)
            show_detailed = self.output_generator.display_summary(final_report)

            # 如果用户选择查看详细报告，则显示完整结果
            if show_detailed:
                print("\n📋 详细分析报告")
                print("=" * 60)
                self.display_detailed_result(final_report)

        except Exception as e:
            self.logger.error(f"个人分析错误: {e}")
            print(f"❌ 分析过程出错: {e}")

    def run_couple_analysis(self):
        """运行八字合婚分析"""
        try:
            # 第一步：收集两人信息
            print("\n📝 第一步：收集两人生辰信息")
            print("-" * 40)
            couple_data = self.couple_input.collect_couple_information()
            if not couple_data:
                print("❌ 信息收集失败，程序退出")
                return

            # 收集关系信息
            relationship_info = self.couple_input.collect_relationship_info()

            # 第二步：分别计算两人八字
            print("\n⚡ 第二步：计算两人八字命理")
            print("-" * 40)

            male_data = couple_data['male']
            female_data = couple_data['female']

            # 处理男方数据
            print("正在计算男方八字...")
            male_processed = self.data_processor.process(male_data)
            male_bazi = self.bazi_calculator.calculate(male_processed)

            # 处理女方数据
            print("正在计算女方八字...")
            female_processed = self.data_processor.process(female_data)
            female_bazi = self.bazi_calculator.calculate(female_processed)

            # 第三步：合婚分析
            print("\n💕 第三步：八字合婚分析")
            print("-" * 40)

            # 提取八字数据用于合婚分析
            male_bazi_data = self._extract_bazi_for_compatibility(male_bazi, male_data)
            female_bazi_data = self._extract_bazi_for_compatibility(female_bazi, female_data)

            compatibility_result = self.couple_calculator.calculate_compatibility(
                male_bazi_data, female_bazi_data
            )

            # 第四步：显示合婚结果
            print("\n📊 第四步：合婚分析结果")
            print("-" * 40)
            self.display_couple_result(
                male_data, female_data, compatibility_result, relationship_info
            )

        except Exception as e:
            self.logger.error(f"合婚分析错误: {e}")
            print(f"❌ 合婚分析过程出错: {e}")

    def _extract_bazi_for_compatibility(self, bazi_result: dict, person_data: dict) -> dict:
        """提取八字数据用于合婚分析"""
        bazi_pillars = bazi_result.get('bazi_pillars', {})
        wuxing_analysis = bazi_result.get('wuxing_analysis', {})
        shishen_analysis = bazi_result.get('shishen_analysis', {})

        # 构建合婚计算器需要的数据格式
        compatibility_data = {
            'name': person_data.get('name', '未知'),
            'year_pillar': bazi_pillars.get('year_pillar', ''),
            'month_pillar': bazi_pillars.get('month_pillar', ''),
            'day_pillar': bazi_pillars.get('day_pillar', ''),
            'hour_pillar': bazi_pillars.get('hour_pillar', ''),
            'day_master_element': wuxing_analysis.get('day_master_element', '未知'),
            'main_shishen': shishen_analysis.get('main_shishen', '未知')
        }

        return compatibility_data

    def display_couple_result(self, male_data: dict, female_data: dict,
                             compatibility_result: dict, relationship_info: dict):
        """显示合婚分析结果"""
        print("\n" + "=" * 70)
        print("💕 八字合婚分析报告")
        print("=" * 70)

        # 基本信息
        print(f"\n👨 男方：{male_data['name']} ({male_data['gender']})")
        print(f"   生辰：{male_data['year']}年{male_data['month']}月{male_data['day']}日")
        if male_data.get('lunar_info'):
            print(f"   农历：{male_data['lunar_info']['lunar_date_full']}")

        print(f"\n👩 女方：{female_data['name']} ({female_data['gender']})")
        print(f"   生辰：{female_data['year']}年{female_data['month']}月{female_data['day']}日")
        if female_data.get('lunar_info'):
            print(f"   农历：{female_data['lunar_info']['lunar_date_full']}")

        # 关系信息
        print(f"\n💑 关系状态：{relationship_info['relationship_status']}")
        if relationship_info['know_months'] > 0:
            print(f"   认识时间：{relationship_info['know_months']}个月")
        print(f"   关注重点：{relationship_info['main_concern']}")

        # 合婚结果
        print(f"\n🎯 合婚分析结果：")
        print(f"   综合评分：{compatibility_result['overall_score']}分")
        print(f"   配对等级：{compatibility_result['compatibility_level']}")

        # 四柱分析
        print(f"\n📊 四柱配对分析：")
        pillar_names = {'year': '年柱', 'month': '月柱', 'day': '日柱', 'hour': '时柱'}
        for pillar, data in compatibility_result['pillar_analysis'].items():
            print(f"   {pillar_names[pillar]}：{data['male_pillar']} vs {data['female_pillar']} "
                  f"(评分：{data['pillar_score']:.0f}分)")

        # 日柱分析（重点）
        day_analysis = compatibility_result['day_pillar_analysis']
        print(f"\n💖 日柱配对（重点）：")
        print(f"   男方日柱：{day_analysis['male_day_pillar']}")
        print(f"   女方日柱：{day_analysis['female_day_pillar']}")
        print(f"   配对评分：{day_analysis['final_score']:.0f}分")
        print(f"   分析：{day_analysis['analysis']}")

        # 五行配合
        wuxing_analysis = compatibility_result['wuxing_analysis']
        print(f"\n🌟 五行配合分析：")
        print(f"   互补程度：{wuxing_analysis['complement_score']}分")
        print(f"   主要关系：{wuxing_analysis['main_element_relation']['description']}")

        # 建议
        print(f"\n💡 合婚建议：")
        for i, suggestion in enumerate(compatibility_result['suggestions'], 1):
            print(f"   {i}. {suggestion}")

        # 询问是否查看详细分析
        print("\n" + "=" * 70)
        while True:
            choice = input("是否查看详细分析报告？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                self.display_detailed_couple_analysis(compatibility_result)
                break
            elif choice in ['n', 'no', '否']:
                print("\n✨ 感谢使用八字合婚系统！")
                break
            else:
                print("请输入 y(查看) 或 n(结束)")

    def display_detailed_couple_analysis(self, compatibility_result: dict):
        """显示详细合婚分析"""
        print("\n" + "=" * 70)
        print("📋 详细八字合婚分析报告")
        print("=" * 70)

        print(compatibility_result['detailed_analysis'])

        # 显示各项详细分析
        print(f"\n📊 详细评分分析：")

        # 四柱详细分析
        print(f"\n🏛️ 四柱详细分析：")
        pillar_names = {'year': '年柱', 'month': '月柱', 'day': '日柱', 'hour': '时柱'}
        for pillar, data in compatibility_result['pillar_analysis'].items():
            print(f"\n   {pillar_names[pillar]}配对：")
            print(f"     男方：{data['male_pillar']}")
            print(f"     女方：{data['female_pillar']}")
            print(f"     天干关系：{data['tiangan_relation']['description']}")
            print(f"     地支关系：{data['dizhi_relation']['description']}")
            print(f"     权重：{data['weight']*100:.0f}%")
            print(f"     得分：{data['pillar_score']:.1f}分")

        # 五行详细分析
        wuxing = compatibility_result['wuxing_analysis']
        print(f"\n🌟 五行详细分析：")
        print(f"   男方五行分布：{wuxing['male_elements']}")
        print(f"   女方五行分布：{wuxing['female_elements']}")
        print(f"   合并后分布：{wuxing['balance_analysis']['combined_elements']}")
        print(f"   主导五行：{wuxing['balance_analysis']['dominant_element']}")
        print(f"   薄弱五行：{wuxing['balance_analysis']['weak_elements']}")

        print("\n✨ 详细分析完成！")
    
    def display_detailed_result(self, report):
        """显示详细分析结果"""
        
        # 基本信息
        print(f"\n📋 基本信息:")
        print(f"   姓名: {report.get('name', '未提供')}")
        print(f"   性别: {report.get('gender', '未提供')}")
        print(f"   阳历生日: {report.get('birth_time', '未提供')}")

        # 显示阴历信息（如果有）
        if report.get('lunar_info'):
            lunar_info = report['lunar_info']
            print(f"   阴历生日: {lunar_info['lunar_date_full']}")
            print(f"   转换精度: {lunar_info['conversion_accuracy']}")

        print(f"   出生地点: {report.get('birth_location', '未提供')}")
        
        # 八字信息
        bazi_info = report.get('bazi_info', {})
        print(f"\n🎴 八字排盘:")
        print(f"   年柱: {bazi_info.get('year_pillar', 'N/A')}")
        print(f"   月柱: {bazi_info.get('month_pillar', 'N/A')}")
        print(f"   日柱: {bazi_info.get('day_pillar', 'N/A')}")
        print(f"   时柱: {bazi_info.get('hour_pillar', 'N/A')}")
        
        # 五行分析
        wuxing_info = report.get('wuxing_analysis', {})
        print(f"\n🌟 五行分析:")
        distribution = wuxing_info.get('distribution', {})
        for element, count in distribution.items():
            print(f"   {element}: {count}个")
        print(f"   日主强弱: {wuxing_info.get('strength', 'N/A')}")
        print(f"   用神: {wuxing_info.get('useful_god', 'N/A')}")
        
        # 分析内容
        analysis = report.get('analysis', {})
        
        print(f"\n👤 性格特征:")
        personality = analysis.get('personality', '暂无分析')
        print(f"   {personality}")
        
        print(f"\n💼 事业运势:")
        career = analysis.get('career', '暂无分析')
        print(f"   {career}")
        
        print(f"\n💕 感情婚姻:")
        relationship = analysis.get('relationship', '暂无分析')
        print(f"   {relationship}")
        
        print(f"\n🏥 健康状况:")
        health = analysis.get('health', '暂无分析')
        print(f"   {health}")
        
        # 建议
        suggestions = report.get('suggestions', [])
        if suggestions:
            print(f"\n💡 人生建议:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion}")
        
        # 系统信息
        metadata = report.get('metadata', {})
        print(f"\n📊 分析信息:")
        print(f"   计算准确度: {metadata.get('accuracy', 'N/A')}")
        print(f"   分析时间: {metadata.get('analysis_time', 'N/A')}")
        print(f"   系统版本: {metadata.get('version', 'MVP-1.0')}")
        
        print("\n" + "=" * 60)
        print("✨ 分析完成！感谢使用八字算命系统")
        print("=" * 60)

def main():
    """主函数"""
    system = BaziMVPSystem()
    system.run()

if __name__ == "__main__":
    main()

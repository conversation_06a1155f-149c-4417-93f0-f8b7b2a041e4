#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的报告生成器
生成结构清晰、可读性强的八字合婚分析报告
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, List, Any
from data.enhanced_marriage_analyzer import enhanced_marriage_analyzer
from data.core_logic_mapping import get_rizhu_core_logic

class EnhancedReportGenerator:
    """增强的报告生成器，提供结构化的专业报告"""
    
    def __init__(self):
        self.report_templates = {
            'comprehensive': self._generate_comprehensive_report,
            'summary': self._generate_summary_report,
            'detailed': self._generate_detailed_report
        }
    
    def generate_marriage_report(self, male_bazi: Dict[str, Any], 
                               female_bazi: Dict[str, Any],
                               overall_score: int,
                               report_type: str = 'comprehensive') -> str:
        """
        生成八字合婚分析报告
        
        Args:
            male_bazi: 男方八字信息
            female_bazi: 女方八字信息
            overall_score: 综合评分
            report_type: 报告类型 ('comprehensive', 'summary', 'detailed')
            
        Returns:
            格式化的分析报告
        """
        if report_type in self.report_templates:
            return self.report_templates[report_type](male_bazi, female_bazi, overall_score)
        else:
            return self._generate_comprehensive_report(male_bazi, female_bazi, overall_score)
    
    def _generate_comprehensive_report(self, male_bazi: Dict[str, Any], 
                                     female_bazi: Dict[str, Any],
                                     overall_score: int) -> str:
        """生成综合报告"""
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')
        
        # 获取详细分析
        analysis = enhanced_marriage_analyzer.get_comprehensive_analysis(male_bazi, female_bazi)
        
        # 确定配对等级
        compatibility_level = self._get_compatibility_level(overall_score)
        
        report = f"""
╔══════════════════════════════════════════════════════════════╗
║                    八字合婚详细分析报告                        ║
╚══════════════════════════════════════════════════════════════╝

【基本信息】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👨 {male_name}：{male_bazi.get('day_pillar', '')}日柱  |  👩 {female_name}：{female_bazi.get('day_pillar', '')}日柱
📅 出生信息：{male_bazi.get('birth_date', '未提供')}  |  📅 出生信息：{female_bazi.get('birth_date', '未提供')}

【综合评分】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 配对评分：{overall_score}分 / 100分
🏆 配对等级：{compatibility_level}
📊 匹配指数：{'★' * (overall_score // 20)}{'☆' * (5 - overall_score // 20)}

【日柱配对分析】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._format_rizhu_analysis(analysis.get('rizhu_analysis', {}))}

【生肖配对分析】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._format_shengxiao_analysis(analysis.get('shengxiao_analysis', {}))}

【五行强弱分析】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._format_wuxing_strength_analysis(analysis.get('wuxing_strength_analysis', {}))}

【个性化建议】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._format_detailed_advice(analysis.get('detailed_advice', {}))}

【发展前景】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._format_compatibility_summary(analysis.get('compatibility_summary', {}))}

【专业建议】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💡 八字合婚是传统文化的智慧结晶，为感情提供参考方向。
💝 真正的幸福需要双方的理解、包容和共同努力。
🌟 建议在相处中多观察彼此的性格、价值观和生活习惯。
🤝 命理分析只是指导，最终的选择要靠双方的真心和努力。

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
报告生成时间：{self._get_current_time()}
        """
        
        return report.strip()
    
    def _generate_summary_report(self, male_bazi: Dict[str, Any], 
                               female_bazi: Dict[str, Any],
                               overall_score: int) -> str:
        """生成简要报告"""
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')
        compatibility_level = self._get_compatibility_level(overall_score)
        
        analysis = enhanced_marriage_analyzer.get_comprehensive_analysis(male_bazi, female_bazi)
        rizhu_analysis = analysis.get('rizhu_analysis', {})
        
        report = f"""
🔮 {male_name}与{female_name}八字合婚简要分析

📊 综合评分：{overall_score}分 ({compatibility_level})
🎯 配对特点：{rizhu_analysis.get('relationship_dynamic', '需要具体分析')}

💕 性格匹配：{rizhu_analysis.get('personality_match', '两人性格各有特点')}
💬 沟通方式：{rizhu_analysis.get('communication_style', '需要相互理解')}
💰 财务配合：{rizhu_analysis.get('financial_compatibility', '需要协调配合')}

🌟 发展潜力：{rizhu_analysis.get('growth_potential', '通过努力可以建立良好关系')}

💡 建议：珍惜彼此，用心经营，在理解中成长，在包容中前进。
        """
        
        return report.strip()
    
    def _generate_detailed_report(self, male_bazi: Dict[str, Any], 
                                female_bazi: Dict[str, Any],
                                overall_score: int) -> str:
        """生成详细报告"""
        # 详细报告包含更多分析维度
        comprehensive_report = self._generate_comprehensive_report(male_bazi, female_bazi, overall_score)
        
        # 添加个人特征分析
        male_personality = self._analyze_individual_traits(male_bazi)
        female_personality = self._analyze_individual_traits(female_bazi)
        
        additional_analysis = f"""

【个人特征深度分析】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

👨 {male_bazi.get('name', '男方')}个人特征：
{male_personality}

👩 {female_bazi.get('name', '女方')}个人特征：
{female_personality}

【相处建议】
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
{self._generate_interaction_advice(male_bazi, female_bazi)}
        """
        
        return comprehensive_report + additional_analysis
    
    def _format_rizhu_analysis(self, rizhu_analysis: Dict[str, Any]) -> str:
        """格式化日柱分析"""
        if not rizhu_analysis:
            return "📝 日柱配对信息需要进一步分析"
        
        score = rizhu_analysis.get('compatibility_score', 70)
        dynamic = rizhu_analysis.get('relationship_dynamic', '需要具体分析')
        personality = rizhu_analysis.get('personality_match', '性格各有特点')
        communication = rizhu_analysis.get('communication_style', '需要磨合')
        
        return f"""🎯 配对评分：{score}分
💫 关系动态：{dynamic}
👥 性格匹配：{personality}
💬 沟通模式：{communication}"""
    
    def _format_shengxiao_analysis(self, shengxiao_analysis: Dict[str, Any]) -> str:
        """格式化生肖分析"""
        if not shengxiao_analysis:
            return "📝 生肖配对信息需要进一步分析"
        
        traditional = shengxiao_analysis.get('traditional_compatibility', '传统配对需要分析')
        dynamics = shengxiao_analysis.get('personality_dynamics', '性格互动需要观察')
        harmony = shengxiao_analysis.get('harmony_suggestions', '建议多沟通交流')
        
        return f"""🐲 传统配对：{traditional}
⚡ 性格互动：{dynamics}
🤝 和谐建议：{harmony}"""
    
    def _format_wuxing_strength_analysis(self, wuxing_analysis: Dict[str, Any]) -> str:
        """格式化五行强弱分析"""
        if not wuxing_analysis:
            return "📝 五行强弱信息需要进一步分析"
        
        balance = wuxing_analysis.get('balance_analysis', '五行配合需要平衡')
        dynamic = wuxing_analysis.get('relationship_dynamic', '关系动态需要观察')
        growth = wuxing_analysis.get('growth_direction', '建议相互补充')
        
        return f"""⚖️ 平衡分析：{balance}
🔄 关系动态：{dynamic}
📈 发展方向：{growth}"""
    
    def _format_detailed_advice(self, advice: Dict[str, Any]) -> str:
        """格式化详细建议"""
        if not advice:
            return "📝 个性化建议需要根据具体情况制定"
        
        formatted_advice = []
        advice_icons = {
            '沟通交流': '💬',
            '财务管理': '💰', 
            '家庭建设': '🏠'
        }
        
        for category, content in advice.items():
            icon = advice_icons.get(category, '📌')
            formatted_advice.append(f"{icon} {category}：{content}")
        
        return '\n'.join(formatted_advice)
    
    def _format_compatibility_summary(self, summary: Dict[str, Any]) -> str:
        """格式化兼容性总结"""
        if not summary:
            return "📝 发展前景需要根据具体情况分析"
        
        assessment = summary.get('overall_assessment', '配对具有独特特点')
        strengths = summary.get('key_strengths', [])
        challenges = summary.get('potential_challenges', [])
        
        formatted_summary = f"🎯 总体评价：{assessment}\n"
        
        if strengths:
            formatted_summary += f"✨ 主要优势：{', '.join(strengths)}\n"
        
        if challenges:
            formatted_summary += f"⚠️ 潜在挑战：{', '.join(challenges)}"
        
        return formatted_summary
    
    def _analyze_individual_traits(self, bazi_info: Dict[str, Any]) -> str:
        """分析个人特征"""
        day_pillar = bazi_info.get('day_pillar', '')
        core_logic = get_rizhu_core_logic(day_pillar)
        
        personalities = core_logic.get('core_personality', ['待分析'])
        element_relation = core_logic.get('core_element_relation', '需要分析')
        career_direction = core_logic.get('career_direction', ['多元发展'])
        
        return f"""🎭 核心特质：{', '.join(personalities[:3])}
🌟 五行关系：{element_relation}
💼 适合方向：{', '.join(career_direction[:2])}"""
    
    def _generate_interaction_advice(self, male_bazi: Dict[str, Any], 
                                   female_bazi: Dict[str, Any]) -> str:
        """生成相处建议"""
        male_day = male_bazi.get('day_pillar', '')
        female_day = female_bazi.get('day_pillar', '')
        
        # 基于日柱组合生成相处建议
        advice_templates = [
            f"💝 建议{male_bazi.get('name', '男方')}发挥{male_day}日的特质，多展现包容和理解",
            f"🌸 建议{female_bazi.get('name', '女方')}发挥{female_day}日的优势，保持自己的特色",
            "🤝 在日常相处中要相互尊重，给彼此成长的空间",
            "💕 定期进行深度交流，分享内心的想法和感受",
            "🎯 共同制定目标，在实现梦想的过程中增进感情"
        ]
        
        return '\n'.join(advice_templates)
    
    def _get_compatibility_level(self, score: int) -> str:
        """获取配对等级描述"""
        if score >= 90:
            return "天作之合"
        elif score >= 80:
            return "上等婚配"
        elif score >= 70:
            return "中上等婚配"
        elif score >= 60:
            return "中等婚配"
        elif score >= 50:
            return "中下等婚配"
        else:
            return "需要努力"
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        import datetime
        return datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M")

# 全局报告生成器实例
enhanced_report_generator = EnhancedReportGenerator()

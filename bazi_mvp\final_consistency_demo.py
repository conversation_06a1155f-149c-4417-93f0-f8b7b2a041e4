#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终一致性系统演示
展示完整的八字分析一致性保证机制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_output_generator import enhanced_output_generator
from modules.couple_compatibility import CoupleCompatibilityCalculator

def demo_same_input_different_expressions():
    """演示相同输入的不同表述方式"""
    print("🎯 核心功能演示：相同输入，不同表述，一致结论")
    print("=" * 60)
    
    # 测试数据
    male_bazi = {
        'name': '王先生',
        'birth_date': '1988-03-15 09:30',
        'year_pillar': '戊辰',
        'month_pillar': '乙卯', 
        'day_pillar': '甲子',
        'hour_pillar': '己巳'
    }
    
    female_bazi = {
        'name': '李女士',
        'birth_date': '1990-07-22 15:45',
        'year_pillar': '庚午',
        'month_pillar': '癸未',
        'day_pillar': '丁卯', 
        'hour_pillar': '戊申'
    }
    
    print(f"测试案例：{male_bazi['name']}({male_bazi['day_pillar']}) 与 {female_bazi['name']}({female_bazi['day_pillar']})")
    print()
    
    # 生成3个不同版本的分析报告
    for version in range(1, 4):
        print(f"{'='*20} 版本 {version} {'='*20}")
        
        # 生成综合报告
        compatibility_result = {'overall_score': 85}
        report = enhanced_output_generator.generate_comprehensive_report(
            male_bazi, female_bazi, compatibility_result, version
        )
        
        print(report)
        print()
        
        # 验证核心结论一致性
        print("🔍 核心结论验证:")
        
        # 检查甲子日柱的核心特征是否体现
        if '坚韧' in report and '智慧' in report:
            print("  ✅ 甲子日柱核心特征（坚韧、智慧）正确体现")
        else:
            print("  ❌ 甲子日柱核心特征缺失")
        
        # 检查丁卯日柱的核心特征是否体现
        if '温柔' in report and '细腻' in report:
            print("  ✅ 丁卯日柱核心特征（温柔、细腻）正确体现")
        else:
            print("  ❌ 丁卯日柱核心特征缺失")
        
        # 检查木火相生的关系是否体现
        if '木' in report and '火' in report and ('相生' in report or '事业相助' in report):
            print("  ✅ 木火相生关系正确体现")
        else:
            print("  ❌ 木火相生关系缺失")
        
        # 检查高分配对的积极评价
        if '85分' in report and ('上等' in report or '高度' in report):
            print("  ✅ 高分配对的积极评价正确")
        else:
            print("  ❌ 高分配对评价不当")
        
        print("-" * 60)

def demo_consistency_across_multiple_runs():
    """演示多次运行的一致性"""
    print("\n🔄 多次运行一致性演示")
    print("=" * 60)
    
    day_pillar = '甲子'
    print(f"测试日柱：{day_pillar}")
    print("连续5次生成个性分析，观察核心特征的一致性：")
    print()
    
    core_traits_found = []
    
    for i in range(5):
        # 清空缓存，确保每次都重新生成
        enhanced_output_generator.clear_cache()
        
        analysis = enhanced_output_generator.generate_personality_analysis(day_pillar, i+1)
        print(f"第{i+1}次分析:")
        print(f"  {analysis}")
        
        # 检查核心特征
        traits_in_this_analysis = []
        core_traits = ['坚韧', '智慧', '适应力强', '包容心']
        
        for trait in core_traits:
            if trait in analysis:
                traits_in_this_analysis.append(trait)
        
        core_traits_found.append(traits_in_this_analysis)
        print(f"  核心特征: {traits_in_this_analysis}")
        print()
    
    # 分析一致性
    print("🔍 一致性分析:")
    all_traits = set()
    for traits in core_traits_found:
        all_traits.update(traits)
    
    print(f"  所有版本中出现的核心特征: {list(all_traits)}")
    
    # 检查每个核心特征是否在大部分版本中出现
    core_traits = ['坚韧', '智慧', '适应力强', '包容心']
    for trait in core_traits:
        count = sum(1 for traits in core_traits_found if trait in traits)
        percentage = count / len(core_traits_found) * 100
        print(f"  '{trait}' 出现在 {count}/5 个版本中 ({percentage:.0f}%)")
        
        if percentage >= 60:  # 至少60%的版本中出现
            print(f"    ✅ 一致性良好")
        else:
            print(f"    ⚠️ 一致性需要改进")

def demo_forbidden_content_prevention():
    """演示禁止内容的预防机制"""
    print("\n🚫 禁止内容预防演示")
    print("=" * 60)
    
    test_cases = [
        ('甲子', ['性格急躁', '缺乏耐心', '五行缺水']),
        ('丙寅', ['性格冷漠', '缺乏热情', '不善表达']),
        ('庚午', ['性格软弱', '缺乏决断', '竞争力差']),
        ('癸酉', ['性格粗糙', '缺乏同情心', '不够纯真'])
    ]
    
    for day_pillar, forbidden_traits in test_cases:
        print(f"\n测试日柱：{day_pillar}")
        print(f"禁止特征：{forbidden_traits}")
        
        # 生成分析
        analysis = enhanced_output_generator.generate_personality_analysis(day_pillar, 1)
        print(f"生成分析：{analysis}")
        
        # 检查是否包含禁止内容
        found_forbidden = []
        for trait in forbidden_traits:
            if trait in analysis:
                found_forbidden.append(trait)
        
        if found_forbidden:
            print(f"  ❌ 发现禁止内容: {found_forbidden}")
        else:
            print(f"  ✅ 未发现禁止内容，一致性检查有效")

def main():
    """主演示函数"""
    print("🔮 八字分析一致性系统 - 最终演示")
    print("确保相同输入产生一致结论，不同表述避免重复")
    print()
    
    # 演示相同输入的不同表述
    demo_same_input_different_expressions()
    
    # 演示多次运行的一致性
    demo_consistency_across_multiple_runs()
    
    # 演示禁止内容预防
    demo_forbidden_content_prevention()
    
    print("\n" + "=" * 60)
    print("🎉 一致性系统演示完成！")
    print()
    print("✅ 核心成果:")
    print("  1. 相同八字输入确保核心结论一致")
    print("  2. 不同表述方式避免内容重复")
    print("  3. 禁止矛盾内容的出现")
    print("  4. 缓存机制确保同版本完全一致")
    print("  5. 多版本提供丰富的表述选择")
    print()
    print("🎯 系统特点:")
    print("  • 专业性：基于传统命理学理论")
    print("  • 一致性：核心逻辑绝不矛盾")
    print("  • 多样性：表述方式丰富多变")
    print("  • 可靠性：智能检查和修正机制")
    print("=" * 60)

if __name__ == "__main__":
    main()

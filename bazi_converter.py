20#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历干支转换器 - 使用lunar_python库
支持完整的年月日时柱计算、五行分析、十神分析
"""

from lunar_python import Solar

def convert_date_to_bazi(year, month, day, hour=12):
    """
    将阳历日期转换为八字信息
    
    Args:
        year: 年份
        month: 月份
        day: 日期
        hour: 小时 (0-23)
    
    Returns:
        dict: 包含完整八字信息的字典
    """
    try:
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        eightChar = lunar.getEightChar()
        
        return {
            'solar_date': f"{year}年{month}月{day}日",
            'lunar_date': lunar.toString(),
            'pillars': {
                'year': eightChar.getYear(),
                'month': eightChar.getMonth(),
                'day': eightChar.getDay(),
                'time': eightChar.getTime()
            },
            'wuxing': {
                'year': eightChar.getYearWuXing(),
                'month': eightChar.getMonthWuXing(),
                'day': eightChar.getDayWuXing(),
                'time': eightChar.getTimeWuXing()
            },
            'shishen': {
                'year': eightChar.getYearShiShenGan(),
                'month': eightChar.getMonthShiShenGan(),
                'day': eightChar.getDayShiShenGan(),
                'time': eightChar.getTimeShiShenGan()
            }
        }
    except Exception as e:
        return {'error': str(e)}

def main():
    """主函数 - 交互式转换器"""
    print("🌙 农历干支转换器")
    print("=" * 50)
    
    while True:
        try:
            print("\n请输入阳历日期:")
            year = int(input("年份 (如 1988): "))
            month = int(input("月份 (1-12): "))
            day = int(input("日期 (1-31): "))
            hour = int(input("时辰 (0-23，默认12): ") or "12")
            
            result = convert_date_to_bazi(year, month, day, hour)
            
            if 'error' in result:
                print(f"❌ 转换失败: {result['error']}")
                continue
            
            print(f"\n🌟 转换结果:")
            print(f"阳历: {result['solar_date']}")
            print(f"农历: {result['lunar_date']}")
            
            print(f"\n🎯 四柱八字:")
            print(f"年柱: {result['pillars']['year']}")
            print(f"月柱: {result['pillars']['month']}")
            print(f"日柱: {result['pillars']['day']}")
            print(f"时柱: {result['pillars']['time']}")
            
            print(f"\n🌟 五行:")
            print(f"年柱: {result['wuxing']['year']}")
            print(f"月柱: {result['wuxing']['month']}")
            print(f"日柱: {result['wuxing']['day']}")
            print(f"时柱: {result['wuxing']['time']}")
            
            print(f"\n⭐ 十神:")
            print(f"年柱: {result['shishen']['year']}")
            print(f"月柱: {result['shishen']['month']}")
            print(f"日柱: {result['shishen']['day']}")
            print(f"时柱: {result['shishen']['time']}")
            
            if input("\n继续转换？(y/n): ").lower() != 'y':
                break
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序
"""

print("开始测试主程序...")

try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    print("导入模块...")
    from modules.user_input import UserInputModule
    from modules.data_processor import DataProcessor
    from modules.bazi_calculator import BaziCalculator
    from modules.corpus_matcher import CorpusMatcher
    from modules.output_generator import OutputGenerator
    from utils.logger import setup_logger
    
    print("创建系统实例...")
    logger = setup_logger()
    user_input = UserInputModule()
    data_processor = DataProcessor()
    bazi_calculator = BaziCalculator()
    corpus_matcher = CorpusMatcher()
    output_generator = OutputGenerator()
    
    print("✅ 主程序模块导入和初始化成功！")
    
    # 测试一个完整的流程
    print("\n测试完整流程...")
    
    # 模拟用户数据
    sample_data = {
        'name': '测试用户',
        'gender': '男',
        'year': 1990,
        'month': 5,
        'day': 15,
        'hour': 14,
        'minute': 30,
        'birth_location': '北京市',
        'time_certainty': 'exact'
    }
    
    print("1. 数据处理...")
    processed_data = data_processor.process(sample_data)
    print("✅ 数据处理完成")
    
    print("2. 八字计算...")
    calculation_result = bazi_calculator.calculate(processed_data)
    print("✅ 八字计算完成")
    
    print("3. 语料匹配...")
    matched_content = corpus_matcher.match(calculation_result)
    print("✅ 语料匹配完成")
    
    print("4. 输出生成...")
    final_report = output_generator.generate(calculation_result, matched_content, processed_data)
    print("✅ 输出生成完成")
    
    print("\n🎉 完整流程测试成功！")
    print(f"用户: {final_report.get('name')}")
    print(f"八字: {final_report.get('bazi_info', {}).get('year_pillar')} {final_report.get('bazi_info', {}).get('month_pillar')} {final_report.get('bazi_info', {}).get('day_pillar')} {final_report.get('bazi_info', {}).get('hour_pillar')}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成。")

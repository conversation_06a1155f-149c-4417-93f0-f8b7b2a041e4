#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
用于测试八字算命系统的各个模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.user_input import UserInputModule
from modules.data_processor import DataProcessor
from modules.bazi_calculator import BaziCalculator
from modules.corpus_matcher import CorpusMatcher
from modules.output_generator import OutputGenerator
from utils.logger import BaziLogger

def test_with_sample_data():
    """使用示例数据测试系统"""
    print("=" * 60)
    print("🧪 八字算命系统测试模式")
    print("=" * 60)
    
    # 初始化日志
    logger = BaziLogger('test_system')
    
    # 示例数据
    sample_data_sets = [
        {
            'name': '张三',
            'gender': '男',
            'year': 1990,
            'month': 5,
            'day': 15,
            'hour': 14,
            'minute': 30,
            'birth_location': '北京市',
            'time_certainty': 'exact'
        },
        {
            'name': '李四',
            'gender': '女',
            'year': 1985,
            'month': 8,
            'day': 20,
            'hour': 10,
            'minute': 0,
            'birth_location': '上海市',
            'time_certainty': 'approximate'
        },
        {
            'name': '王五',
            'gender': '男',
            'year': 1992,
            'month': 12,
            'day': 3,
            'hour': None,
            'minute': None,
            'birth_location': '广州市',
            'time_certainty': 'unknown'
        }
    ]
    
    # 初始化模块
    data_processor = DataProcessor()
    bazi_calculator = BaziCalculator()
    corpus_matcher = CorpusMatcher()
    output_generator = OutputGenerator()
    
    for i, sample_data in enumerate(sample_data_sets, 1):
        print(f"\n{'='*20} 测试案例 {i} {'='*20}")
        print(f"测试用户: {sample_data['name']}")
        print(f"出生信息: {sample_data['year']}年{sample_data['month']}月{sample_data['day']}日")
        print(f"时间确定性: {sample_data['time_certainty']}")
        
        try:
            # 记录开始
            logger.log_user_input(sample_data)
            
            # 数据处理
            print("\n🔄 数据处理中...")
            processed_data = data_processor.process(sample_data)
            print(f"✅ 数据处理完成，质量等级: {processed_data['processing_metadata']['data_quality']}")
            
            # 八字计算
            print("\n⚡ 八字计算中...")
            calculation_result = bazi_calculator.calculate(processed_data)
            
            if calculation_result.get('analysis_type') == 'time_range':
                print("✅ 时间范围分析完成")
                print(f"   分析了 {len(calculation_result['time_analyses'])} 个可能时辰")
            else:
                print("✅ 标准八字计算完成")
                bazi_pillars = calculation_result['bazi_pillars']
                print(f"   八字: {bazi_pillars['year_pillar'][0]}{bazi_pillars['year_pillar'][1]} "
                      f"{bazi_pillars['month_pillar'][0]}{bazi_pillars['month_pillar'][1]} "
                      f"{bazi_pillars['day_pillar'][0]}{bazi_pillars['day_pillar'][1]} "
                      f"{bazi_pillars['hour_pillar'][0]}{bazi_pillars['hour_pillar'][1]}")
            
            # 语料匹配
            print("\n📚 语料匹配中...")
            matched_content = corpus_matcher.match(calculation_result)
            print("✅ 语料匹配完成")
            
            # 输出生成
            print("\n📄 报告生成中...")
            final_report = output_generator.generate(calculation_result, matched_content, processed_data)
            print("✅ 报告生成完成")
            
            # 显示简要结果
            print(f"\n📋 分析结果摘要:")
            print(f"   准确度: {final_report['metadata']['accuracy']}")
            print(f"   数据质量: {final_report['metadata']['data_quality']}")
            
            if final_report['metadata']['warnings']:
                print(f"   警告: {len(final_report['metadata']['warnings'])} 个")
                for warning in final_report['metadata']['warnings']:
                    print(f"     - {warning}")
            
            # 显示核心分析
            analysis = final_report['analysis']
            print(f"\n🎯 核心分析:")
            print(f"   性格: {analysis['personality'][:50]}...")
            print(f"   事业: {analysis['career'][:50]}...")
            print(f"   感情: {analysis['relationship'][:50]}...")
            print(f"   健康: {analysis['health'][:50]}...")
            
            print(f"\n💡 建议数量: {len(final_report['suggestions'])} 条")
            
            # 质量检查
            quality_report = final_report.get('quality_report', {})
            print(f"\n📊 质量评估:")
            print(f"   质量得分: {quality_report.get('quality_score', 0)}")
            print(f"   是否合格: {'是' if quality_report.get('is_acceptable', False) else '否'}")
            
            if quality_report.get('issues'):
                print(f"   质量问题: {len(quality_report['issues'])} 个")
            
            logger.log_quality_check(
                quality_report.get('quality_score', 0),
                quality_report.get('issues', [])
            )
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            logger.log_error('test_error', str(e), {'sample_data': sample_data})
            continue
    
    print(f"\n{'='*60}")
    print("🎉 系统测试完成")
    print("📝 详细日志请查看 logs/ 目录")
    print(f"{'='*60}")

def interactive_test():
    """交互式测试"""
    print("选择测试模式:")
    print("1. 使用示例数据测试")
    print("2. 完整交互式测试")
    print("3. 退出")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == '1':
        test_with_sample_data()
    elif choice == '2':
        # 运行完整的交互式系统
        from main import BaziMVPSystem
        system = BaziMVPSystem()
        system.run()
    elif choice == '3':
        print("退出测试")
    else:
        print("无效选择")
        interactive_test()

if __name__ == "__main__":
    interactive_test()

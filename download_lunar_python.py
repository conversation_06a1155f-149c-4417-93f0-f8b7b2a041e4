#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载并安装lunar_python库
"""

import os
import urllib.request
import zipfile
import tempfile

def download_lunar_python():
    """下载lunar_python源码"""
    print("🔄 正在下载 lunar_python 源码...")
    
    # GitHub源码下载链接
    url = "https://github.com/6tail/lunar-python/archive/refs/heads/master.zip"
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, "lunar-python.zip")
        
        print(f"下载到: {zip_path}")
        
        # 下载文件
        urllib.request.urlretrieve(url, zip_path)
        print("✅ 下载完成")
        
        # 解压文件
        print("🔄 正在解压...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 找到解压后的目录
        extracted_dir = os.path.join(temp_dir, "lunar-python-master")
        
        if os.path.exists(extracted_dir):
            print("✅ 解压完成")
            
            # 复制lunar_python目录到当前目录
            import shutil
            source_dir = os.path.join(extracted_dir, "lunar_python")
            target_dir = os.path.join(os.getcwd(), "lunar_python")
            
            if os.path.exists(source_dir):
                if os.path.exists(target_dir):
                    shutil.rmtree(target_dir)
                shutil.copytree(source_dir, target_dir)
                print(f"✅ lunar_python 库已复制到: {target_dir}")
                
                # 测试导入
                try:
                    import sys
                    sys.path.insert(0, os.getcwd())
                    from lunar_python import Solar
                    
                    # 简单测试
                    solar = Solar.fromYmd(1988, 3, 15)
                    lunar = solar.getLunar()
                    print(f"🎯 测试成功: {lunar.toString()}")
                    
                    return True
                except Exception as e:
                    print(f"❌ 导入测试失败: {e}")
                    return False
            else:
                print("❌ 未找到lunar_python源码目录")
                return False
        else:
            print("❌ 解压失败")
            return False
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def test_existing_libraries():
    """测试现有的库"""
    print("\n🔍 测试现有库...")
    
    # 测试LunarCalendar
    try:
        from LunarCalendar import Converter, Solar
        print("✅ LunarCalendar 库可用")
        
        # 测试功能
        solar = Solar(1988, 3, 15)
        lunar = Converter.Solar2Lunar(solar)
        print(f"LunarCalendar测试: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查干支属性
        print("检查LunarCalendar的干支功能:")
        for attr in ['gz_year', 'gz_month', 'gz_day']:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    print(f"  {attr}: {value}")
                except Exception as e:
                    print(f"  {attr}: 获取失败 - {e}")
        
        return True
        
    except ImportError:
        print("❌ LunarCalendar 库未安装")
        return False
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🌙 lunar_python 库安装器")
    print("=" * 50)
    
    # 先测试现有库
    lunarcalendar_available = test_existing_libraries()
    
    # 尝试下载lunar_python
    lunar_python_available = download_lunar_python()
    
    print("\n" + "=" * 50)
    print("📊 安装结果:")
    print(f"LunarCalendar: {'✅ 可用' if lunarcalendar_available else '❌ 不可用'}")
    print(f"lunar_python: {'✅ 可用' if lunar_python_available else '❌ 不可用'}")
    
    if lunar_python_available or lunarcalendar_available:
        print("\n🎉 至少有一个库可用，可以进行农历干支计算！")
        return True
    else:
        print("\n😞 暂时无法安装农历库，建议检查网络连接")
        return False

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java lunar-calendar库安装和测试脚本
"""

import os
import urllib.request
import subprocess

def download_lunar_calendar_jar():
    """下载lunar-calendar的jar包"""
    print("🔄 下载Java lunar-calendar库...")
    
    # GitHub releases页面的jar包链接
    jar_urls = [
        "https://github.com/6tail/lunar-java/releases/download/1.4.9/lunar-1.4.9.jar",
        "https://repo1.maven.org/maven2/com/nlf/lunar/1.4.9/lunar-1.4.9.jar"
    ]
    
    for url in jar_urls:
        try:
            print(f"尝试从 {url} 下载...")
            urllib.request.urlretrieve(url, "lunar-calendar.jar")
            print("✅ 下载成功: lunar-calendar.jar")
            return True
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            continue
    
    print("❌ 所有下载链接都失败")
    return False

def create_java_test_with_library():
    """创建包含lunar-calendar库调用的Java测试文件"""
    
    java_code = '''
import com.nlf.calendar.Solar;
import com.nlf.calendar.Lunar;
import com.nlf.calendar.eightchar.EightChar;
import java.util.Scanner;

/**
 * Java lunar-calendar库完整测试
 * 测试年月日时柱计算
 */
public class LunarCalendarTest {
    
    public static void main(String[] args) {
        System.out.println("🌙 Java lunar-calendar 库测试");
        System.out.println("============================================================");
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            try {
                System.out.print("\\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ");
                String input = scanner.nextLine().trim();
                
                if (input.toLowerCase().equals("quit") || input.toLowerCase().equals("q")) {
                    System.out.println("👋 再见！");
                    break;
                }
                
                // 解析日期
                String[] parts = input.split("-");
                if (parts.length != 3) {
                    System.out.println("❌ 格式错误，请使用 YYYY-MM-DD");
                    continue;
                }
                
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                
                System.out.print("时辰 (0-23，默认12): ");
                String hourInput = scanner.nextLine().trim();
                int hour = hourInput.isEmpty() ? 12 : Integer.parseInt(hourInput);
                
                System.out.println("\\n" + "=".repeat(60));
                System.out.printf("🌟 测试日期: %d年%d月%d日 %d时%n", year, month, day, hour);
                System.out.println("=".repeat(60));
                
                testLunarCalendar(year, month, day, hour);
                
            } catch (NumberFormatException e) {
                System.out.println("❌ 日期格式错误");
            } catch (Exception e) {
                System.out.println("❌ 错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }
    
    private static void testLunarCalendar(int year, int month, int day, int hour) {
        System.out.println("🔍 测试 lunar-calendar 库");
        System.out.println("-".repeat(40));
        
        try {
            // 创建阳历对象
            Solar solar = new Solar(year, month, day, hour, 0, 0);
            
            // 转换为农历
            Lunar lunar = solar.getLunar();
            
            // 获取八字
            EightChar eightChar = lunar.getEightChar();
            
            System.out.println("✅ 状态: 成功");
            System.out.println("📅 农历: " + lunar.toString());
            
            System.out.println("\\n🎯 四柱八字:");
            System.out.println("年柱: " + eightChar.getYear() + " (" + eightChar.getYearWuXing() + ") - " + eightChar.getYearShiShenGan());
            System.out.println("月柱: " + eightChar.getMonth() + " (" + eightChar.getMonthWuXing() + ") - " + eightChar.getMonthShiShenGan());
            System.out.println("日柱: " + eightChar.getDay() + " (" + eightChar.getDayWuXing() + ") - " + eightChar.getDayShiShenGan());
            System.out.println("时柱: " + eightChar.getTime() + " (" + eightChar.getTimeWuXing() + ") - " + eightChar.getTimeShiShenGan());
            
            System.out.println("\\n🌟 详细信息:");
            System.out.println("胎元: " + eightChar.getTaiYuan());
            System.out.println("命宫: " + eightChar.getMingGong());
            System.out.println("身宫: " + eightChar.getShenGong());
            
        } catch (Exception e) {
            System.out.println("❌ 状态: 错误 - " + e.getMessage());
            e.printStackTrace();
        }
    }
}
'''
    
    try:
        with open('LunarCalendarTest.java', 'w', encoding='utf-8') as f:
            f.write(java_code)
        print("✅ 创建Java测试文件: LunarCalendarTest.java")
        return True
    except Exception as e:
        print(f"❌ 创建Java文件失败: {e}")
        return False

def compile_and_run_java():
    """编译和运行Java程序"""
    print("\n🔄 编译Java程序...")
    
    # 检查是否有jar包
    if not os.path.exists("lunar-calendar.jar"):
        print("❌ 未找到lunar-calendar.jar，请先下载")
        return False
    
    try:
        # 编译Java文件
        compile_cmd = ["javac", "-cp", "lunar-calendar.jar", "LunarCalendarTest.java"]
        result = subprocess.run(compile_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 编译成功")
            
            print("\n🚀 运行Java程序...")
            print("使用命令: java -cp .;lunar-calendar.jar LunarCalendarTest")
            print("(您可以手动运行上述命令)")
            
            return True
        else:
            print(f"❌ 编译失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到javac命令，请确保安装了JDK")
        return False
    except Exception as e:
        print(f"❌ 编译错误: {e}")
        return False

def main():
    """主函数"""
    print("🌙 Java lunar-calendar库安装器")
    print("=" * 50)
    
    # 下载jar包
    if download_lunar_calendar_jar():
        print("\n📝 创建Java测试文件...")
        
        if create_java_test_with_library():
            print("\n🔧 编译Java程序...")
            
            if compile_and_run_java():
                print("\n🎉 安装完成！")
                print("\n📋 使用方法:")
                print("1. 运行: java -cp .;lunar-calendar.jar LunarCalendarTest")
                print("2. 或者在IDE中运行LunarCalendarTest.java")
                print("3. 输入日期测试年月日时柱计算")
            else:
                print("\n⚠️ 编译失败，但文件已准备好")
                print("请手动编译和运行:")
                print("javac -cp lunar-calendar.jar LunarCalendarTest.java")
                print("java -cp .;lunar-calendar.jar LunarCalendarTest")
        else:
            print("❌ 创建Java文件失败")
    else:
        print("❌ 下载失败，请手动下载lunar-calendar.jar")
        print("下载地址: https://github.com/6tail/lunar-java/releases")

if __name__ == "__main__":
    main()

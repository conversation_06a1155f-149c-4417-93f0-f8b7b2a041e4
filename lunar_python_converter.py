#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于lunar_python库的农历干支转换器
支持完整的年月日柱计算
"""

from lunar_python import Solar

def convert_date_with_ganzhi(year, month, day, hour=12):
    """转换日期并显示完整的干支信息"""
    print(f"\n🌟 阳历日期: {year}年{month}月{day}日 {hour}时")
    print("=" * 60)
    
    try:
        # 创建阳历对象
        solar = Solar.fromYmd(year, month, day)
        
        # 转换为农历
        lunar = solar.getLunar()
        
        print(f"📅 农历: {lunar.toString()}")
        print(f"详细: {lunar.toFullString()}")
        
        # 获取八字
        eightChar = lunar.getEightChar()
        
        print(f"\n🎯 四柱八字:")
        print(f"年柱: {eightChar.getYear()} ({eightChar.getYearGanZhi()})")
        print(f"月柱: {eightChar.getMonth()} ({eightChar.getMonthGanZhi()})")
        print(f"日柱: {eightChar.getDay()} ({eightChar.getDayGanZhi()})")
        print(f"时柱: {eightChar.getTime()} ({eightChar.getTimeGanZhi()})")
        
        print(f"\n🌟 五行:")
        print(f"年柱五行: {eightChar.getYearWuXing()}")
        print(f"月柱五行: {eightChar.getMonthWuXing()}")
        print(f"日柱五行: {eightChar.getDayWuXing()}")
        print(f"时柱五行: {eightChar.getTimeWuXing()}")
        
        print(f"\n⭐ 十神:")
        print(f"年柱十神: {eightChar.getYearShiShenGan()}")
        print(f"月柱十神: {eightChar.getMonthShiShenGan()}")
        print(f"日柱十神: {eightChar.getDayShiShenGan()}")
        print(f"时柱十神: {eightChar.getTimeShiShenGan()}")
        
        # 返回关键信息
        return {
            'lunar_date': lunar.toString(),
            'year_pillar': eightChar.getYearGanZhi(),
            'month_pillar': eightChar.getMonthGanZhi(),
            'day_pillar': eightChar.getDayGanZhi(),
            'time_pillar': eightChar.getTimeGanZhi(),
            'year_wuxing': eightChar.getYearWuXing(),
            'month_wuxing': eightChar.getMonthWuXing(),
            'day_wuxing': eightChar.getDayWuXing(),
            'time_wuxing': eightChar.getTimeWuXing()
        }
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

def main():
    """主函数"""
    print("🌙 lunar_python 农历干支转换器")
    print("=" * 50)
    print("支持完整的年月日时柱计算")
    print()
    
    # 演示标准案例
    demo_cases = [
        (1988, 3, 15, "甲辰年测试"),
        (1990, 7, 22, "庚午年测试"),
        (2024, 1, 1, "2024年元旦")
    ]
    
    for year, month, day, desc in demo_cases:
        print(f"\n📅 {desc}")
        convert_date_with_ganzhi(year, month, day)
        print("\n" + "="*60)
    
    # 交互模式
    while True:
        try:
            user_input = input("\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            date_parts = user_input.split('-')
            if len(date_parts) != 3:
                print("❌ 格式错误，请使用 YYYY-MM-DD")
                continue
            
            year, month, day = map(int, date_parts)
            
            # 询问时辰
            hour_input = input("请输入时辰 (0-23，默认12): ").strip()
            hour = int(hour_input) if hour_input else 12
            
            convert_date_with_ganzhi(year, month, day, hour)
            
        except ValueError:
            print("❌ 日期格式错误")
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的语料库测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_import():
    """测试基本导入"""
    try:
        print("测试基本导入...")
        from data.corpus_database import COUPLE_COMPATIBILITY_CORPUS
        print("✅ corpus_database 导入成功")
        
        from data.corpus.extended_corpus import SHISHEN_DETAILED_CORPUS
        print("✅ extended_corpus 导入成功")
        
        from data.corpus_manager import corpus_manager
        print("✅ corpus_manager 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_corpus_manager():
    """测试语料管理器"""
    try:
        from data.corpus_manager import corpus_manager
        
        print("\n测试语料管理器功能...")
        
        # 测试五行配对分析
        wuxing_analysis = corpus_manager.get_wuxing_pairing_analysis('金', '水')
        print(f"五行配对分析: {wuxing_analysis['compatibility']}")
        
        # 测试个性分析
        personality = corpus_manager.get_personality_analysis('甲子', count=1)
        if personality:
            print(f"个性分析: {personality[0][:50]}...")
        
        # 测试合婚分析
        compatibility = corpus_manager.get_compatibility_analysis('high_compatibility', count=1)
        print(f"合婚分析类型: {len(compatibility)} 个维度")
        
        print("✅ 语料管理器测试成功")
        return True
    except Exception as e:
        print(f"❌ 语料管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_corpus_content():
    """测试语料内容"""
    try:
        from data.corpus.extended_corpus import (
            SHISHEN_DETAILED_CORPUS,
            WUXING_PAIRING_CORPUS,
            LIFE_ADVICE_CORPUS
        )
        
        print("\n测试语料内容...")
        
        # 检查十神语料
        shishen_count = len(SHISHEN_DETAILED_CORPUS)
        print(f"十神语料数量: {shishen_count}")
        
        # 检查五行配对语料
        wuxing_count = len(WUXING_PAIRING_CORPUS)
        print(f"五行配对语料数量: {wuxing_count}")
        
        # 检查生活建议语料
        advice_count = len(LIFE_ADVICE_CORPUS)
        print(f"生活建议语料数量: {advice_count}")
        
        print("✅ 语料内容测试成功")
        return True
    except Exception as e:
        print(f"❌ 语料内容测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔮 八字语料库系统测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    # 测试基本导入
    if test_basic_import():
        success_count += 1
    
    # 测试语料管理器
    if test_corpus_manager():
        success_count += 1
    
    # 测试语料内容
    if test_corpus_content():
        success_count += 1
    
    print("\n" + "=" * 40)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！语料库系统运行正常")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()

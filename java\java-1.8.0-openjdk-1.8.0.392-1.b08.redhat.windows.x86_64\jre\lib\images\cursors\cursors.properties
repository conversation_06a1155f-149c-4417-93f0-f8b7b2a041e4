#
#
# Cursors Properties file
#
# Names GIF89 sources for Custom Cursors and their associated HotSpots
#
# Note: the syntax of the property name is significant and is parsed
# by java.awt.Cursor
#
# The syntax is: Cursor.<name>.<geom>.File=win32_<filename>
#                Cursor.<name>.<geom>.HotSpot=<x>,<y>
#	         Cursor.<name>.<geom>.Name=<localized name>
#
Cursor.CopyDrop.32x32.File=win32_CopyDrop32x32.gif
Cursor.CopyDrop.32x32.HotSpot=0,0
Cursor.CopyDrop.32x32.Name=CopyDrop32x32
#
Cursor.MoveDrop.32x32.File=win32_MoveDrop32x32.gif
Cursor.MoveDrop.32x32.HotSpot=0,0
Cursor.MoveDrop.32x32.Name=MoveDrop32x32
#
Cursor.LinkDrop.32x32.File=win32_LinkDrop32x32.gif
Cursor.LinkDrop.32x32.HotSpot=0,0
Cursor.LinkDrop.32x32.Name=LinkDrop32x32
#
Cursor.CopyNoDrop.32x32.File=win32_CopyNoDrop32x32.gif
Cursor.CopyNoDrop.32x32.HotSpot=6,2
Cursor.CopyNoDrop.32x32.Name=CopyNoDrop32x32
#
Cursor.MoveNoDrop.32x32.File=win32_MoveNoDrop32x32.gif
Cursor.MoveNoDrop.32x32.HotSpot=6,2
Cursor.MoveNoDrop.32x32.Name=MoveNoDrop32x32
#
Cursor.LinkNoDrop.32x32.File=win32_LinkNoDrop32x32.gif
Cursor.LinkNoDrop.32x32.HotSpot=6,2
Cursor.LinkNoDrop.32x32.Name=LinkNoDrop32x32
#
Cursor.Invalid.32x32.File=invalid32x32.gif
Cursor.Invalid.32x32.HotSpot=6,2
Cursor.Invalid.32x32.Name=Invalid32x32

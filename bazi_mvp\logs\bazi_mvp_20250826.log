2025-08-26 22:33:26 - test_system - INFO - 用户输入收集完成: {'name': '张*', 'gender': '男', 'birth_year': 1990, 'birth_month': 5, 'birth_day': 15, 'time_certainty': 'exact', 'location_provided': True}
2025-08-26 22:33:26 - test_system - INFO - 质量检查完成 - 得分: 90, 问题数: 1
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:33:26 - test_system - INFO - 用户输入收集完成: {'name': '李*', 'gender': '女', 'birth_year': 1985, 'birth_month': 8, 'birth_day': 20, 'time_certainty': 'approximate', 'location_provided': True}
2025-08-26 22:33:26 - test_system - INFO - 质量检查完成 - 得分: 80, 问题数: 2
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: relationship内容过短 (当前: 58, 最少: 100)
2025-08-26 22:33:26 - test_system - INFO - 用户输入收集完成: {'name': '王*', 'gender': '男', 'birth_year': 1992, 'birth_month': 12, 'birth_day': 3, 'time_certainty': 'unknown', 'location_provided': True}
2025-08-26 22:33:26 - test_system - INFO - 质量检查完成 - 得分: 50, 问题数: 5
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: personality内容过短 (当前: 95, 最少: 100)
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: career内容过短 (当前: 66, 最少: 100)
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: relationship内容过短 (当前: 70, 最少: 100)
2025-08-26 22:33:26 - test_system - WARNING - 质量问题: health内容过短 (当前: 56, 最少: 80)
2025-08-26 22:33:45 - bazi_mvp - INFO - 八字算命系统初始化完成
2025-08-26 22:34:27 - bazi_mvp - ERROR - 系统运行错误: 'birth_year'
2025-08-26 22:35:31 - bazi_mvp - INFO - 八字算命系统初始化完成
2025-08-26 22:35:51 - bazi_mvp - ERROR - 系统运行错误: 'birth_year'
2025-08-26 22:36:47 - bazi_mvp - INFO - 八字算命系统初始化完成
2025-08-26 22:37:57 - bazi_mvp - ERROR - 系统运行错误: 'birth_year'
2025-08-26 22:39:01 - bazi_mvp - INFO - 八字算命系统初始化完成
2025-08-26 22:39:30 - test_system - INFO - 用户输入收集完成: {'name': '张*', 'gender': '男', 'birth_year': 1990, 'birth_month': 5, 'birth_day': 15, 'time_certainty': 'exact', 'location_provided': True}
2025-08-26 22:39:30 - test_system - INFO - 质量检查完成 - 得分: 90, 问题数: 1
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:39:30 - test_system - INFO - 用户输入收集完成: {'name': '李*', 'gender': '女', 'birth_year': 1985, 'birth_month': 8, 'birth_day': 20, 'time_certainty': 'approximate', 'location_provided': True}
2025-08-26 22:39:30 - test_system - INFO - 质量检查完成 - 得分: 80, 问题数: 2
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: relationship内容过短 (当前: 58, 最少: 100)
2025-08-26 22:39:30 - test_system - INFO - 用户输入收集完成: {'name': '王*', 'gender': '男', 'birth_year': 1992, 'birth_month': 12, 'birth_day': 3, 'time_certainty': 'unknown', 'location_provided': True}
2025-08-26 22:39:30 - test_system - INFO - 质量检查完成 - 得分: 50, 问题数: 5
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: 缺少必需元素: basic_info
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: personality内容过短 (当前: 95, 最少: 100)
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: career内容过短 (当前: 66, 最少: 100)
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: relationship内容过短 (当前: 70, 最少: 100)
2025-08-26 22:39:30 - test_system - WARNING - 质量问题: health内容过短 (当前: 56, 最少: 80)
2025-08-26 22:41:56 - bazi_mvp - INFO - 八字算命系统初始化完成

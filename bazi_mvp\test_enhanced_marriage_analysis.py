#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的合婚分析系统
验证语料库扩充和报告框架优化的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.couple_compatibility import CoupleCompatibilityCalculator
from modules.enhanced_report_generator import enhanced_report_generator
from data.enhanced_marriage_analyzer import enhanced_marriage_analyzer

def test_different_couples_analysis():
    """测试不同人物信息的差异化分析"""
    print("🔮 测试不同人物信息的差异化合婚分析")
    print("=" * 60)
    
    # 测试案例1：甲子 + 丁卯
    couple1_male = {
        'name': '张先生',
        'birth_date': '1988-03-15 09:30',
        'year_pillar': '戊辰',
        'month_pillar': '乙卯',
        'day_pillar': '甲子',
        'hour_pillar': '己巳',
        'career': '教师'
    }
    
    couple1_female = {
        'name': '李女士',
        'birth_date': '1990-07-22 15:45',
        'year_pillar': '庚午',
        'month_pillar': '癸未',
        'day_pillar': '丁卯',
        'hour_pillar': '戊申',
        'career': '设计师'
    }
    
    # 测试案例2：丙寅 + 戊辰
    couple2_male = {
        'name': '王先生',
        'birth_date': '1985-06-10 14:20',
        'year_pillar': '乙丑',
        'month_pillar': '壬午',
        'day_pillar': '丙寅',
        'hour_pillar': '辛未',
        'career': '企业家'
    }
    
    couple2_female = {
        'name': '赵女士',
        'birth_date': '1987-11-08 11:15',
        'year_pillar': '丁卯',
        'month_pillar': '辛亥',
        'day_pillar': '戊辰',
        'hour_pillar': '戊午',
        'career': '医生'
    }
    
    # 测试案例3：庚午 + 癸酉
    couple3_male = {
        'name': '刘先生',
        'birth_date': '1982-09-25 16:30',
        'year_pillar': '壬戌',
        'month_pillar': '己酉',
        'day_pillar': '庚午',
        'hour_pillar': '丙申',
        'career': '程序员'
    }
    
    couple3_female = {
        'name': '陈女士',
        'birth_date': '1984-12-03 08:45',
        'year_pillar': '甲子',
        'month_pillar': '乙亥',
        'day_pillar': '癸酉',
        'hour_pillar': '丙辰',
        'career': '银行职员'
    }
    
    test_couples = [
        (couple1_male, couple1_female, "甲子+丁卯组合"),
        (couple2_male, couple2_female, "丙寅+戊辰组合"),
        (couple3_male, couple3_female, "庚午+癸酉组合")
    ]
    
    calculator = CoupleCompatibilityCalculator()
    
    for i, (male_bazi, female_bazi, description) in enumerate(test_couples, 1):
        print(f"\n{'='*20} 测试案例{i}：{description} {'='*20}")
        
        try:
            # 进行合婚分析
            result = calculator.calculate_compatibility(male_bazi, female_bazi)
            
            print(f"📊 综合评分：{result['overall_score']}分")
            print(f"🏆 配对等级：{result['compatibility_level']}")
            print("\n📋 详细分析报告：")
            print(result['detailed_analysis'])
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*80)

def test_enhanced_analysis_components():
    """测试增强分析组件的功能"""
    print("\n🔧 测试增强分析组件功能")
    print("=" * 60)
    
    # 测试数据
    male_bazi = {
        'name': '测试男方',
        'day_pillar': '甲子',
        'year_pillar': '戊辰',
        'birth_date': '1988-03-15'
    }
    
    female_bazi = {
        'name': '测试女方',
        'day_pillar': '丁卯',
        'year_pillar': '庚午',
        'birth_date': '1990-07-22'
    }
    
    print("1. 测试增强合婚分析器：")
    print("-" * 30)
    
    try:
        analysis = enhanced_marriage_analyzer.get_comprehensive_analysis(male_bazi, female_bazi)
        
        print("✅ 日柱分析：", analysis.get('rizhu_analysis', {}).get('relationship_dynamic', '未获取'))
        print("✅ 生肖分析：", analysis.get('shengxiao_analysis', {}).get('traditional_compatibility', '未获取'))
        print("✅ 五行强弱：", analysis.get('wuxing_strength_analysis', {}).get('balance_analysis', '未获取'))
        print("✅ 详细建议：", len(analysis.get('detailed_advice', {})), "项建议")
        
    except Exception as e:
        print(f"❌ 增强分析器测试失败: {e}")
    
    print("\n2. 测试报告生成器：")
    print("-" * 30)
    
    try:
        # 测试不同类型的报告
        report_types = ['comprehensive', 'summary', 'detailed']
        
        for report_type in report_types:
            report = enhanced_report_generator.generate_marriage_report(
                male_bazi, female_bazi, 85, report_type
            )
            print(f"✅ {report_type}报告生成成功，长度：{len(report)}字符")
        
    except Exception as e:
        print(f"❌ 报告生成器测试失败: {e}")

def test_content_diversity():
    """测试内容多样性"""
    print("\n🎨 测试内容多样性")
    print("=" * 60)
    
    # 相同的测试数据
    male_bazi = {
        'name': '张三',
        'day_pillar': '甲子',
        'year_pillar': '戊辰',
        'birth_date': '1988-03-15'
    }
    
    female_bazi = {
        'name': '李四',
        'day_pillar': '丁卯',
        'year_pillar': '庚午',
        'birth_date': '1990-07-22'
    }
    
    print("连续3次生成相同输入的分析报告，观察内容差异：")
    print("-" * 50)
    
    for i in range(3):
        print(f"\n第{i+1}次分析：")
        
        try:
            # 清空缓存确保重新生成
            enhanced_marriage_analyzer.analysis_cache.clear()
            
            analysis = enhanced_marriage_analyzer.get_comprehensive_analysis(male_bazi, female_bazi)
            rizhu_analysis = analysis.get('rizhu_analysis', {})
            
            print(f"关系动态：{rizhu_analysis.get('relationship_dynamic', '未获取')}")
            print(f"性格匹配：{rizhu_analysis.get('personality_match', '未获取')}")
            print(f"沟通方式：{rizhu_analysis.get('communication_style', '未获取')}")
            
        except Exception as e:
            print(f"❌ 第{i+1}次分析失败: {e}")

def test_report_readability():
    """测试报告可读性"""
    print("\n📖 测试报告可读性和结构")
    print("=" * 60)
    
    male_bazi = {
        'name': '王先生',
        'day_pillar': '丙寅',
        'year_pillar': '乙丑',
        'birth_date': '1985-06-10',
        'career': '企业家'
    }
    
    female_bazi = {
        'name': '赵女士',
        'day_pillar': '戊辰',
        'year_pillar': '丁卯',
        'birth_date': '1987-11-08',
        'career': '医生'
    }
    
    try:
        # 生成综合报告
        report = enhanced_report_generator.generate_marriage_report(
            male_bazi, female_bazi, 83, 'comprehensive'
        )
        
        print("📋 综合报告结构分析：")
        print("-" * 30)
        
        # 分析报告结构
        lines = report.split('\n')
        sections = [line for line in lines if '━━━' in line or '【' in line]
        
        print(f"✅ 报告总长度：{len(report)}字符")
        print(f"✅ 报告行数：{len(lines)}行")
        print(f"✅ 主要章节：{len(sections)}个")
        
        print("\n📑 主要章节列表：")
        for section in sections[:10]:  # 显示前10个章节
            clean_section = section.strip().replace('━', '').replace('【', '').replace('】', '')
            if clean_section:
                print(f"  • {clean_section}")
        
        # 检查关键元素
        key_elements = ['基本信息', '综合评分', '日柱配对', '生肖配对', '个性化建议', '发展前景']
        missing_elements = []
        
        for element in key_elements:
            if element not in report:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"\n⚠️ 缺失的关键元素：{missing_elements}")
        else:
            print(f"\n✅ 所有关键元素都已包含")
        
    except Exception as e:
        print(f"❌ 报告可读性测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🌟 增强的八字合婚分析系统测试")
    print("验证语料库扩充和报告框架优化效果")
    print()
    
    # 测试不同人物的差异化分析
    test_different_couples_analysis()
    
    # 测试增强分析组件
    test_enhanced_analysis_components()
    
    # 测试内容多样性
    test_content_diversity()
    
    # 测试报告可读性
    test_report_readability()
    
    print("\n" + "=" * 60)
    print("🎉 增强合婚分析系统测试完成！")
    print()
    print("✅ 主要改进成果：")
    print("  1. 大幅扩充合婚专用语料库")
    print("  2. 基于日柱组合的差异化分析")
    print("  3. 重新设计的清晰报告框架")
    print("  4. 多维度的专业分析内容")
    print("  5. 优化的可读性和用户体验")
    print("=" * 60)

if __name__ == "__main__":
    main()

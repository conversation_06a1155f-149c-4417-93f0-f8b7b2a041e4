#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("🔍 检查已安装的库")

# 检查lunar_python
try:
    import lunar_python
    print("✅ lunar_python 已安装")
    print(f"版本: {getattr(lunar_python, '__version__', '未知')}")
    
    # 测试基本功能
    from lunar_python import Solar
    solar = Solar.fromYmd(1988, 3, 15)
    lunar = solar.getLunar()
    
    print(f"测试: 1988-03-15")
    print(f"农历: {lunar.toString()}")
    
    # 获取八字
    eightChar = lunar.getEightChar()
    print(f"年柱: {eightChar.getYearGanZhi()}")
    print(f"月柱: {eightChar.getMonthGanZhi()}")
    print(f"日柱: {eightChar.getDayGanZhi()}")
    print(f"时柱: {eightChar.getTimeGanZhi()}")
    
except ImportError:
    print("❌ lunar_python 未安装")
except Exception as e:
    print(f"❌ lunar_python 测试失败: {e}")

# 检查其他库
libraries = ['zhdate', 'lunardate', 'LunarCalendar', 'sxtwl', 'borax']

for lib in libraries:
    try:
        __import__(lib)
        print(f"✅ {lib} 已安装")
    except ImportError:
        print(f"❌ {lib} 未安装")

print("\n🎯 测试完成")

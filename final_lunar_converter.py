#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版农历干支转换器
基于lunar_python库，支持完整的年月日柱计算
"""

import sys
import os

def test_and_convert():
    """测试并转换"""
    print("🌙 lunar_python 农历干支转换器")
    print("=" * 60)
    
    try:
        # 导入lunar_python库
        from lunar_python import Solar
        
        print("✅ lunar_python 库加载成功")
        
        # 演示标准案例
        demo_cases = [
            (1988, 3, 15, "甲辰年测试"),
            (1990, 7, 22, "庚午年测试"),
            (2024, 1, 1, "2024年元旦")
        ]
        
        print("\n🎬 演示标准案例:")
        for year, month, day, desc in demo_cases:
            print(f"\n📅 {desc}: {year}-{month:02d}-{day:02d}")
            print("-" * 40)
            
            try:
                solar = Solar.fromYmd(year, month, day)
                lunar = solar.getLunar()
                
                print(f"农历: {lunar.toString()}")
                
                # 获取八字
                eightChar = lunar.getEightChar()
                
                print(f"年柱: {eightChar.getYear()}")
                print(f"月柱: {eightChar.getMonth()}")
                print(f"日柱: {eightChar.getDay()}")
                print(f"时柱: {eightChar.getTime()}")

                print(f"五行 - 年:{eightChar.getYearWuXing()} 月:{eightChar.getMonthWuXing()} 日:{eightChar.getDayWuXing()} 时:{eightChar.getTimeWuXing()}")
                
            except Exception as e:
                print(f"❌ 转换失败: {e}")
        
        # 交互模式
        print("\n" + "="*60)
        print("🔄 交互模式 - 请输入您的日期")
        
        while True:
            try:
                user_input = input("\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                # 解析日期
                date_parts = user_input.split('-')
                if len(date_parts) != 3:
                    print("❌ 格式错误，请使用 YYYY-MM-DD")
                    continue
                
                year, month, day = map(int, date_parts)
                
                # 询问时辰
                hour_input = input("请输入时辰 (0-23，默认12): ").strip()
                hour = int(hour_input) if hour_input else 12
                
                print(f"\n🌟 转换结果: {year}年{month}月{day}日 {hour}时")
                print("=" * 50)
                
                solar = Solar.fromYmd(year, month, day)
                lunar = solar.getLunar()
                
                print(f"📅 农历: {lunar.toString()}")
                
                # 获取八字
                eightChar = lunar.getEightChar()
                
                print(f"\n🎯 四柱八字:")
                print(f"年柱: {eightChar.getYear()}")
                print(f"月柱: {eightChar.getMonth()}")
                print(f"日柱: {eightChar.getDay()}")
                print(f"时柱: {eightChar.getTime()}")

                print(f"\n🌟 五行:")
                print(f"年柱: {eightChar.getYearWuXing()}")
                print(f"月柱: {eightChar.getMonthWuXing()}")
                print(f"日柱: {eightChar.getDayWuXing()}")
                print(f"时柱: {eightChar.getTimeWuXing()}")

                print(f"\n⭐ 十神:")
                print(f"年柱: {eightChar.getYearShiShenGan()}")
                print(f"月柱: {eightChar.getMonthShiShenGan()}")
                print(f"日柱: {eightChar.getDayShiShenGan()}")
                print(f"时柱: {eightChar.getTimeShiShenGan()}")
                
            except ValueError:
                print("❌ 日期格式错误")
            except KeyboardInterrupt:
                print("\n👋 程序被中断，再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ lunar_python 库导入失败: {e}")
        print("请确保lunar_python目录在当前目录下")
        return False
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # 运行测试和转换
    test_and_convert()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版农历干支转换器
用户输入阳历日期，输出农历和年月日柱信息
支持多个库的对比输出，包含准确性验证
"""

from datetime import datetime, date
import sys

class EnhancedLunarConverter:
    """增强版农历干支转换器"""
    
    def __init__(self):
        self.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        self.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 已知的标准答案用于验证
        self.standard_cases = {
            (1988, 3, 15): {
                'lunar': (1988, 1, 28),
                'day_ganzhi': '甲子',
                'year_ganzhi': '戊辰'
            },
            (1990, 7, 22): {
                'lunar': (1990, 6, 1),
                'day_ganzhi': '丁卯',
                'year_ganzhi': '庚午'
            },
            (2024, 1, 1): {
                'lunar': (2023, 11, 20),
                'day_ganzhi': '庚寅',
                'year_ganzhi': '甲辰'
            }
        }
        
        # 检查可用的库
        self.available_libraries = self._check_available_libraries()
        
    def _check_available_libraries(self):
        """检查可用的农历库"""
        libraries = {}
        
        # 检查 zhdate
        try:
            import zhdate
            libraries['zhdate'] = zhdate
            print("✅ zhdate 库可用")
        except ImportError:
            print("❌ zhdate 库未安装")
        
        # 检查 lunardate
        try:
            from lunardate import LunarDate
            libraries['lunardate'] = LunarDate
            print("✅ lunardate 库可用")
        except ImportError:
            print("❌ lunardate 库未安装")
        
        # 检查 LunarCalendar
        try:
            from LunarCalendar import Converter, Solar
            libraries['LunarCalendar'] = {'Converter': Converter, 'Solar': Solar}
            print("✅ LunarCalendar 库可用")
        except ImportError:
            print("❌ LunarCalendar 库未安装")
        
        return libraries
    
    def calculate_ganzhi_year(self, year):
        """计算年柱干支"""
        # 以1984年甲子年为基准
        base_year = 1984
        year_diff = year - base_year
        ganzhi_index = year_diff % 60
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return self.tiangan[tg_index] + self.dizhi[dz_index]
    
    def calculate_ganzhi_day(self, year, month, day):
        """计算日柱干支（使用更精确的算法）"""
        # 使用儒略日数计算
        julian_day = date(year, month, day).toordinal() + 1721425
        
        # 甲子日的儒略日数（1900年1月31日是甲子日）
        jiazi_julian = 1924681
        
        # 计算距离甲子日的天数
        days_diff = julian_day - jiazi_julian
        
        # 60甲子循环
        ganzhi_index = days_diff % 60
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return self.tiangan[tg_index] + self.dizhi[dz_index]
    
    def get_lunar_info_zhdate(self, year, month, day):
        """使用zhdate获取农历信息"""
        if 'zhdate' not in self.available_libraries:
            return None
        
        try:
            zhdate = self.available_libraries['zhdate']
            dt = datetime(year, month, day)
            zh_date = zhdate.ZhDate.from_datetime(dt)
            
            return {
                'lunar_year': zh_date.lunar_year,
                'lunar_month': zh_date.lunar_month,
                'lunar_day': zh_date.lunar_day,
                'is_leap': getattr(zh_date, 'is_leap', False),
                'library': 'zhdate'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_lunar_info_lunardate(self, year, month, day):
        """使用lunardate获取农历信息"""
        if 'lunardate' not in self.available_libraries:
            return None
        
        try:
            LunarDate = self.available_libraries['lunardate']
            lunar_date = LunarDate.fromSolarDate(year, month, day)
            
            return {
                'lunar_year': lunar_date.year,
                'lunar_month': lunar_date.month,
                'lunar_day': lunar_date.day,
                'is_leap': getattr(lunar_date, 'isLeapMonth', False),
                'library': 'lunardate'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_lunar_info_lunarcalendar(self, year, month, day):
        """使用LunarCalendar获取农历信息"""
        if 'LunarCalendar' not in self.available_libraries:
            return None
        
        try:
            Converter = self.available_libraries['LunarCalendar']['Converter']
            Solar = self.available_libraries['LunarCalendar']['Solar']
            
            solar = Solar(year, month, day)
            lunar = Converter.Solar2Lunar(solar)
            
            result = {
                'lunar_year': lunar.year,
                'lunar_month': lunar.month,
                'lunar_day': lunar.day,
                'is_leap': getattr(lunar, 'isLeapMonth', False),
                'library': 'LunarCalendar'
            }
            
            # 尝试获取干支信息
            if hasattr(lunar, 'gz_year'):
                result['gz_year'] = lunar.gz_year
            if hasattr(lunar, 'gz_month'):
                result['gz_month'] = lunar.gz_month
            if hasattr(lunar, 'gz_day'):
                result['gz_day'] = lunar.gz_day
            
            return result
        except Exception as e:
            return {'error': str(e)}
    
    def validate_results(self, year, month, day, results):
        """验证结果准确性"""
        test_key = (year, month, day)
        if test_key not in self.standard_cases:
            return None
        
        expected = self.standard_cases[test_key]
        validation = {}
        
        # 验证自实现的干支计算
        self_day_gz = results['self_calculated']['day_gz']
        self_year_gz = results['self_calculated']['year_gz']
        
        validation['day_ganzhi'] = {
            'expected': expected['day_ganzhi'],
            'self_calculated': self_day_gz,
            'correct': self_day_gz == expected['day_ganzhi']
        }
        
        validation['year_ganzhi'] = {
            'expected': expected['year_ganzhi'],
            'self_calculated': self_year_gz,
            'correct': self_year_gz == expected['year_ganzhi']
        }
        
        # 验证农历转换
        expected_lunar = expected['lunar']
        for lib_name, lib_result in results.get('libraries', {}).items():
            if lib_result and 'error' not in lib_result:
                actual_lunar = (lib_result['lunar_year'], lib_result['lunar_month'], lib_result['lunar_day'])
                validation[f'{lib_name}_lunar'] = {
                    'expected': f"农历{expected_lunar[0]}年{expected_lunar[1]}月{expected_lunar[2]}日",
                    'actual': f"农历{actual_lunar[0]}年{actual_lunar[1]}月{actual_lunar[2]}日",
                    'correct': actual_lunar == expected_lunar
                }
        
        return validation
    
    def convert_date(self, year, month, day):
        """转换日期，返回所有库的结果对比"""
        print(f"\n🌟 阳历日期: {year}年{month}月{day}日")
        print("=" * 60)
        
        # 自实现的干支计算
        self_year_gz = self.calculate_ganzhi_year(year)
        self_day_gz = self.calculate_ganzhi_day(year, month, day)
        
        print(f"🔧 自实现干支算法:")
        print(f"   年柱: {self_year_gz}")
        print(f"   日柱: {self_day_gz}")
        
        # 测试各个库
        libraries_to_test = [
            ('zhdate', self.get_lunar_info_zhdate),
            ('lunardate', self.get_lunar_info_lunardate),
            ('LunarCalendar', self.get_lunar_info_lunarcalendar)
        ]
        
        library_results = {}
        
        for lib_name, lib_func in libraries_to_test:
            print(f"\n📚 {lib_name} 库:")
            result = lib_func(year, month, day)
            library_results[lib_name] = result
            
            if result is None:
                print("   ❌ 库不可用")
            elif 'error' in result:
                print(f"   ❌ 错误: {result['error']}")
            else:
                # 显示农历信息
                lunar_str = f"农历{result['lunar_year']}年{result['lunar_month']}月{result['lunar_day']}日"
                if result.get('is_leap'):
                    lunar_str += " (闰月)"
                print(f"   农历: {lunar_str}")
                
                # 显示干支信息（如果有）
                if 'gz_year' in result:
                    print(f"   年柱: {result['gz_year']}")
                if 'gz_month' in result:
                    print(f"   月柱: {result['gz_month']}")
                if 'gz_day' in result:
                    print(f"   日柱: {result['gz_day']}")
        
        # 准备结果数据
        results = {
            'self_calculated': {
                'year_gz': self_year_gz,
                'day_gz': self_day_gz
            },
            'libraries': library_results
        }
        
        # 验证准确性
        validation = self.validate_results(year, month, day, results)
        if validation:
            print(f"\n🎯 准确性验证:")
            print("-" * 30)
            
            for key, val in validation.items():
                if 'ganzhi' in key:
                    status = "✅" if val['correct'] else "❌"
                    print(f"   {key}: {status} 期望:{val['expected']} 实际:{val['self_calculated']}")
                elif 'lunar' in key:
                    status = "✅" if val['correct'] else "❌"
                    print(f"   {key}: {status} 期望:{val['expected']} 实际:{val['actual']}")
        
        return results

def demo_mode():
    """演示模式 - 展示几个测试案例"""
    print("🎬 演示模式 - 展示标准测试案例")
    print("=" * 60)
    
    converter = EnhancedLunarConverter()
    
    demo_cases = [
        (1988, 3, 15, "甲辰年测试 - 应为甲子日"),
        (1990, 7, 22, "庚午年测试 - 应为丁卯日"),
        (2024, 1, 1, "2024年元旦 - 应为庚寅日")
    ]
    
    for year, month, day, desc in demo_cases:
        print(f"\n📅 {desc}")
        converter.convert_date(year, month, day)
        print("\n" + "="*60)

def interactive_mode():
    """交互模式 - 用户输入日期"""
    print("🌙 农历干支转换器 - 交互模式")
    print("=" * 50)
    print("支持多个农历库的对比输出")
    print("包含自实现的干支计算算法")
    print()
    
    converter = EnhancedLunarConverter()
    
    if not converter.available_libraries:
        print("⚠️ 没有检测到可用的农历库")
        print("建议安装: pip install zhdate lunardate")
        print("但仍可使用自实现的干支计算功能")
        print()
    
    while True:
        try:
            print("\n" + "="*50)
            print("请输入阳历日期 (格式: YYYY-MM-DD)")
            print("输入 'demo' 查看演示")
            print("输入 'quit' 退出程序")
            
            user_input = input("请输入: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'demo':
                demo_mode()
                continue
            
            # 解析日期
            try:
                date_parts = user_input.split('-')
                if len(date_parts) != 3:
                    raise ValueError("日期格式错误")
                
                year = int(date_parts[0])
                month = int(date_parts[1])
                day = int(date_parts[2])
                
                # 验证日期有效性
                datetime(year, month, day)
                
            except ValueError as e:
                print(f"❌ 日期格式错误: {e}")
                print("请使用格式: YYYY-MM-DD (例如: 1988-03-15)")
                continue
            
            # 执行转换
            converter.convert_date(year, month, day)
            
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        demo_mode()
    else:
        interactive_mode()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字核心逻辑映射表
确保同样的八字输入产生一致的核心分析结果
"""

# 天干五行属性（固定不变）
TIANGAN_WUXING = {
    '甲': '木', '乙': '木',
    '丙': '火', '丁': '火', 
    '戊': '土', '己': '土',
    '庚': '金', '辛': '金',
    '壬': '水', '癸': '水'
}

# 地支五行属性（固定不变）
DIZHI_WUXING = {
    '子': '水', '丑': '土', '寅': '木', '卯': '木',
    '辰': '土', '巳': '火', '午': '火', '未': '土',
    '申': '金', '酉': '金', '戌': '土', '亥': '水'
}

# 五行生克关系（固定不变）
WUXING_RELATIONS = {
    '生': {
        '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
    },
    '克': {
        '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
    }
}

# 日柱核心特征映射（核心逻辑固定，表述可变）
RIZHU_CORE_TRAITS = {
    '甲子': {
        'core_element_relation': '水生木',
        'core_personality': ['坚韧', '智慧', '适应力强', '有包容心'],
        'core_tendency': '偏吉',
        'core_strength': '得水滋养，根基稳固',
        'core_weakness': '可能过于温和',
        'career_direction': ['教育', '文化', '管理', '研究'],
        'health_focus': ['肾脏', '泌尿系统'],
        'relationship_trait': '专一深情',
        'wealth_pattern': '稳步积累'
    },
    
    '乙丑': {
        'core_element_relation': '木坐土库',
        'core_personality': ['温和', '踏实', '有耐心', '包容性强'],
        'core_tendency': '中吉',
        'core_strength': '稳重务实，善于积累',
        'core_weakness': '可能过于保守',
        'career_direction': ['农业', '房地产', '服务业', '手工艺'],
        'health_focus': ['脾胃', '消化系统'],
        'relationship_trait': '温柔体贴',
        'wealth_pattern': '稳健理财'
    },
    
    '丙寅': {
        'core_element_relation': '木生火',
        'core_personality': ['热情', '开朗', '有领导力', '富有感染力'],
        'core_tendency': '大吉',
        'core_strength': '木火通明，智慧与热情并存',
        'core_weakness': '可能过于冲动',
        'career_direction': ['教育', '媒体', '娱乐', '管理'],
        'health_focus': ['心脏', '血液循环'],
        'relationship_trait': '主动热情',
        'wealth_pattern': '积极进取'
    },
    
    '丁卯': {
        'core_element_relation': '木生火',
        'core_personality': ['温柔', '细腻', '有艺术天赋', '善解人意'],
        'core_tendency': '中吉',
        'core_strength': '火木相生，才华与美感并存',
        'core_weakness': '可能过于敏感',
        'career_direction': ['艺术', '设计', '美容', '文化'],
        'health_focus': ['眼睛', '神经系统'],
        'relationship_trait': '浪漫温柔',
        'wealth_pattern': '通过才华获得'
    },
    
    '戊辰': {
        'core_element_relation': '土旺得势',
        'core_personality': ['沉稳', '大气', '有责任心', '包容力强'],
        'core_tendency': '中吉',
        'core_strength': '土旺厚重，稳定可靠',
        'core_weakness': '可能过于固执',
        'career_direction': ['建筑', '房地产', '管理', '政府'],
        'health_focus': ['脾胃', '皮肤'],
        'relationship_trait': '稳重可靠',
        'wealth_pattern': '稳健积累'
    },
    
    '己巳': {
        'core_element_relation': '火生土',
        'core_personality': ['温和', '谦逊', '有洞察力', '直觉敏锐'],
        'core_tendency': '中吉',
        'core_strength': '火土相生，智慧与温和并存',
        'core_weakness': '可能过于谨慎',
        'career_direction': ['咨询', '策划', '文化', '医疗'],
        'health_focus': ['心脏', '小肠'],
        'relationship_trait': '细腻敏感',
        'wealth_pattern': '通过智慧获得'
    },
    
    '庚午': {
        'core_element_relation': '火克金',
        'core_personality': ['刚直', '果断', '有执行力', '竞争意识强'],
        'core_tendency': '中平',
        'core_strength': '在冲突中成长，意志坚强',
        'core_weakness': '可能过于强势',
        'career_direction': ['军警', '体育', '金融', '竞争性行业'],
        'health_focus': ['心脏', '肺部'],
        'relationship_trait': '直接坦率',
        'wealth_pattern': '通过竞争获得'
    },
    
    '辛未': {
        'core_element_relation': '土生金',
        'core_personality': ['温和', '内秀', '有品味', '审美能力强'],
        'core_tendency': '中吉',
        'core_strength': '土金相生，优雅与内涵并存',
        'core_weakness': '可能过于内向',
        'career_direction': ['珠宝', '艺术', '设计', '文化'],
        'health_focus': ['肺部', '大肠'],
        'relationship_trait': '优雅温柔',
        'wealth_pattern': '通过品味获得'
    },
    
    '壬申': {
        'core_element_relation': '金生水',
        'core_personality': ['聪明', '灵活', '适应力强', '创新思维'],
        'core_tendency': '中吉',
        'core_strength': '金水相生，智慧与灵活并存',
        'core_weakness': '可能过于变化',
        'career_direction': ['科技', '金融', '贸易', '创新行业'],
        'health_focus': ['肾脏', '泌尿系统'],
        'relationship_trait': '理性冷静',
        'wealth_pattern': '通过智慧投资'
    },
    
    '癸酉': {
        'core_element_relation': '金生水',
        'core_personality': ['纯真', '善良', '直觉敏锐', '同情心强'],
        'core_tendency': '中吉',
        'core_strength': '金水相生，纯净与智慧并存',
        'core_weakness': '可能过于敏感',
        'career_direction': ['医疗', '慈善', '艺术', '服务业'],
        'health_focus': ['肺部', '肾脏'],
        'relationship_trait': '纯真专一',
        'wealth_pattern': '通过服务获得'
    }
}

# 五行配对核心逻辑（固定不变）
WUXING_PAIRING_CORE = {
    ('金', '水'): {
        'relation_type': '相生',
        'compatibility_level': '高度匹配',
        'core_advantage': '智慧互补，财运相助',
        'core_challenge': '需要平衡理性与感性',
        'stability_score': 85
    },
    ('水', '木'): {
        'relation_type': '相生', 
        'compatibility_level': '高度匹配',
        'core_advantage': '成长互助，学习相促',
        'core_challenge': '需要给予成长空间',
        'stability_score': 88
    },
    ('木', '火'): {
        'relation_type': '相生',
        'compatibility_level': '高度匹配', 
        'core_advantage': '事业相助，创造力强',
        'core_challenge': '需要控制冲动',
        'stability_score': 90
    },
    ('火', '土'): {
        'relation_type': '相生',
        'compatibility_level': '高度匹配',
        'core_advantage': '家庭和睦，情感深厚',
        'core_challenge': '需要平衡激情与稳定',
        'stability_score': 87
    },
    ('土', '金'): {
        'relation_type': '相生',
        'compatibility_level': '高度匹配',
        'core_advantage': '财运亨通，稳定发展',
        'core_challenge': '避免过于物质化',
        'stability_score': 85
    }
}

# 十神核心特征（固定逻辑）
SHISHEN_CORE_TRAITS = {
    '正官': {
        'core_nature': '正统权威',
        'personality_core': ['正直', '有责任心', '管理能力'],
        'career_tendency': '政府机关、大企业、教育',
        'wealth_pattern': '稳定收入',
        'relationship_style': '传统保守'
    },
    '七杀': {
        'core_nature': '偏官权威',
        'personality_core': ['刚强', '果断', '竞争意识'],
        'career_tendency': '军警、体育、创业',
        'wealth_pattern': '波动较大',
        'relationship_style': '主动积极'
    },
    '正财': {
        'core_nature': '正当财富',
        'personality_core': ['务实', '稳重', '理财能力'],
        'career_tendency': '商业、金融、会计',
        'wealth_pattern': '稳步增长',
        'relationship_style': '重视物质基础'
    },
    '偏财': {
        'core_nature': '意外财富',
        'personality_core': ['开朗', '大方', '机会意识'],
        'career_tendency': '销售、贸易、娱乐',
        'wealth_pattern': '多元化收入',
        'relationship_style': '浪漫多情'
    }
}

def get_rizhu_core_logic(day_pillar: str) -> dict:
    """获取日柱的核心逻辑（固定不变的部分）"""
    return RIZHU_CORE_TRAITS.get(day_pillar, {
        'core_element_relation': '需要具体分析',
        'core_personality': ['待分析'],
        'core_tendency': '中平',
        'core_strength': '待分析',
        'core_weakness': '待分析',
        'career_direction': ['多元发展'],
        'health_focus': ['整体健康'],
        'relationship_trait': '需要磨合',
        'wealth_pattern': '稳步发展'
    })

def get_wuxing_pairing_core(element1: str, element2: str) -> dict:
    """获取五行配对的核心逻辑（固定不变的部分）"""
    pair = (element1, element2)
    reverse_pair = (element2, element1)
    
    if pair in WUXING_PAIRING_CORE:
        return WUXING_PAIRING_CORE[pair]
    elif reverse_pair in WUXING_PAIRING_CORE:
        return WUXING_PAIRING_CORE[reverse_pair]
    else:
        # 默认的相克或同类关系
        if element1 == element2:
            return {
                'relation_type': '同类',
                'compatibility_level': '中等匹配',
                'core_advantage': '容易产生共鸣',
                'core_challenge': '需要避免过于相似',
                'stability_score': 70
            }
        else:
            return {
                'relation_type': '需要磨合',
                'compatibility_level': '需要努力',
                'core_advantage': '互补性发展',
                'core_challenge': '需要理解包容',
                'stability_score': 60
            }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的输出生成器
确保同样的八字输入产生一致但多样化的分析结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, List, Any
from data.corpus_manager import corpus_manager
from data.core_logic_mapping import get_rizhu_core_logic, get_wuxing_pairing_core
from data.consistency_checker import consistency_checker

class EnhancedOutputGenerator:
    """增强的输出生成器，确保一致性和多样性"""
    
    def __init__(self):
        self.output_cache = {}  # 缓存输出结果，确保一致性
        
    def generate_personality_analysis(self, day_pillar: str, variation_id: int = 1) -> str:
        """
        生成个性分析，确保同一日柱的核心特征一致
        
        Args:
            day_pillar: 日柱，如'甲子'
            variation_id: 变化版本ID，用于生成不同表述
            
        Returns:
            个性分析文本
        """
        # 获取核心逻辑
        core_logic = get_rizhu_core_logic(day_pillar)
        
        # 生成缓存键
        cache_key = f"personality_{day_pillar}_{variation_id}"
        
        if cache_key in self.output_cache:
            return self.output_cache[cache_key]
        
        # 获取多样化的语料内容
        personality_contents = corpus_manager.get_personality_analysis(day_pillar, count=2)
        
        # 构建分析文本，确保核心特征体现
        core_personalities = core_logic.get('core_personality', [])
        element_relation = core_logic.get('core_element_relation', '')
        core_strength = core_logic.get('core_strength', '')
        
        # 根据变化版本选择不同的表述方式
        if variation_id == 1:
            analysis = f"""您的日柱为{day_pillar}，{element_relation}，{core_strength}。
性格特征：{', '.join(core_personalities[:3])}，这些特质让您在人际交往中具有独特的魅力。
{personality_contents[0] if personality_contents else '您具有这个日柱的典型特征。'}"""
        
        elif variation_id == 2:
            analysis = f"""从{day_pillar}日柱来看，您天生具有{', '.join(core_personalities[:2])}的特质。
{element_relation}的组合让您{core_strength.lower()}，在生活中展现出独特的个人魅力。
{personality_contents[1] if len(personality_contents) > 1 else personality_contents[0] if personality_contents else '这种特质在您的日常表现中会很明显。'}"""
        
        else:
            analysis = f"""您的命格显示{day_pillar}日的特征，主要体现为{', '.join(core_personalities)}。
这种{element_relation}的配置使您{core_strength.lower()}，形成了独特的性格优势。
{personality_contents[0] if personality_contents else '建议发挥这些天赋特质。'}"""
        
        # 进行最终的一致性检查
        context = {'day_pillar': day_pillar}
        is_valid, final_analysis = consistency_checker.validate_content_before_output(
            'personality', analysis, context
        )
        
        # 缓存结果
        self.output_cache[cache_key] = final_analysis
        
        return final_analysis
    
    def generate_compatibility_analysis(self, male_element: str, female_element: str, 
                                      overall_score: int, variation_id: int = 1) -> str:
        """
        生成合婚分析，确保同样的五行配对核心结论一致
        
        Args:
            male_element: 男方主要五行
            female_element: 女方主要五行
            overall_score: 综合评分
            variation_id: 变化版本ID
            
        Returns:
            合婚分析文本
        """
        # 获取核心逻辑
        core_logic = get_wuxing_pairing_core(male_element, female_element)
        
        # 生成缓存键
        cache_key = f"compatibility_{male_element}_{female_element}_{overall_score}_{variation_id}"
        
        if cache_key in self.output_cache:
            return self.output_cache[cache_key]
        
        # 获取五行配对分析
        wuxing_analysis = corpus_manager.get_wuxing_pairing_analysis(male_element, female_element)
        
        # 确定配对等级描述
        if overall_score >= 85:
            level_desc = "上等婚配"
        elif overall_score >= 70:
            level_desc = "中上等婚配"
        elif overall_score >= 55:
            level_desc = "中等婚配"
        else:
            level_desc = "需要努力的婚配"
        
        # 根据变化版本生成不同表述
        relation_type = core_logic.get('relation_type', '需要磨合')
        compatibility_level = core_logic.get('compatibility_level', '中等匹配')
        core_advantage = core_logic.get('core_advantage', '需要相互理解')
        
        if variation_id == 1:
            analysis = f"""你们的五行配对为{male_element}与{female_element}，属于{relation_type}的组合。
综合评分{overall_score}分，为{level_desc}。
{compatibility_level}的配对特点：{core_advantage}。
{wuxing_analysis['description']}"""
        
        elif variation_id == 2:
            analysis = f"""从五行角度分析，{male_element}{female_element}配对显示{compatibility_level}的特征。
这种{relation_type}的组合在传统命理中评价为{level_desc}（{overall_score}分）。
主要优势体现在：{core_advantage}。
{wuxing_analysis['description']}"""
        
        else:
            analysis = f"""你们的八字显示{male_element}与{female_element}的五行组合，{relation_type}特征明显。
配对等级：{compatibility_level}，综合得分{overall_score}分。
这种配对的核心特点是{core_advantage}，{wuxing_analysis['description']}"""
        
        # 一致性检查
        context = {'element1': male_element, 'element2': female_element}
        is_valid, final_analysis = consistency_checker.validate_content_before_output(
            'compatibility', analysis, context
        )
        
        # 缓存结果
        self.output_cache[cache_key] = final_analysis
        
        return final_analysis
    
    def generate_comprehensive_report(self, male_bazi: Dict[str, Any], 
                                    female_bazi: Dict[str, Any],
                                    compatibility_result: Dict[str, Any],
                                    variation_id: int = 1) -> str:
        """
        生成综合报告，确保整体一致性
        
        Args:
            male_bazi: 男方八字信息
            female_bazi: 女方八字信息  
            compatibility_result: 合婚分析结果
            variation_id: 变化版本ID
            
        Returns:
            综合分析报告
        """
        male_name = male_bazi.get('name', '男方')
        female_name = female_bazi.get('name', '女方')
        male_day = male_bazi.get('day_pillar', '')
        female_day = female_bazi.get('day_pillar', '')
        overall_score = compatibility_result.get('overall_score', 60)
        
        # 生成个性分析
        male_personality = self.generate_personality_analysis(male_day, variation_id)
        female_personality = self.generate_personality_analysis(female_day, variation_id + 1)
        
        # 生成配对分析
        male_element = self._get_main_element(male_bazi)
        female_element = self._get_main_element(female_bazi)
        compatibility_analysis = self.generate_compatibility_analysis(
            male_element, female_element, overall_score, variation_id
        )
        
        # 构建综合报告
        if variation_id == 1:
            report = f"""
=== {male_name}与{female_name}八字合婚详细分析 ===

【{male_name}个人特征分析】
{male_personality}

【{female_name}个人特征分析】  
{female_personality}

【五行配对分析】
{compatibility_analysis}

【综合建议】
基于你们的八字配对分析，建议在相处中发挥各自的优势，理解对方的特点。
真正的幸福需要双方的共同努力和相互包容。
            """
        
        else:
            report = f"""
八字合婚分析报告

{male_name}与{female_name}的深度配对分析：

个人特征解读：
{male_name}：{male_personality}

{female_name}：{female_personality}

配对关系分析：
{compatibility_analysis}

指导建议：
命理分析为你们的关系提供了参考方向，但最终的幸福还需要双方的真心付出。
建议珍惜彼此，在理解中成长，在包容中前进。
            """
        
        return report.strip()
    
    def _get_main_element(self, bazi_info: Dict[str, Any]) -> str:
        """获取主要五行元素"""
        day_pillar = bazi_info.get('day_pillar', '')
        if len(day_pillar) >= 1:
            from data.core_logic_mapping import TIANGAN_WUXING
            return TIANGAN_WUXING.get(day_pillar[0], '土')
        return '土'
    
    def clear_cache(self):
        """清空缓存，用于测试或重置"""
        self.output_cache.clear()

# 全局输出生成器实例
enhanced_output_generator = EnhancedOutputGenerator()

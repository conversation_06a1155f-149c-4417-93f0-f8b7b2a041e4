#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农历库集成示例
展示如何在八字项目中使用农历库
"""

from datetime import datetime, date
import sys
import os

class LunarDateCalculator:
    """农历日期计算器"""
    
    def __init__(self):
        self.zhdate_available = False
        self.lunardate_available = False
        
        # 尝试导入可用的库
        try:
            import zhdate
            self.zhdate = zhdate
            self.zhdate_available = True
            print("✅ zhdate 库可用")
        except ImportError:
            print("❌ zhdate 库未安装")
        
        try:
            from lunardate import LunarDate
            self.LunarDate = LunarDate
            self.lunardate_available = True
            print("✅ lunardate 库可用")
        except ImportError:
            print("❌ lunardate 库未安装")
    
    def solar_to_lunar(self, year, month, day):
        """阳历转农历"""
        if self.zhdate_available:
            return self._solar_to_lunar_zhdate(year, month, day)
        elif self.lunardate_available:
            return self._solar_to_lunar_lunardate(year, month, day)
        else:
            raise ImportError("没有可用的农历库")
    
    def _solar_to_lunar_zhdate(self, year, month, day):
        """使用zhdate进行转换"""
        dt = datetime(year, month, day)
        zh_date = self.zhdate.ZhDate.from_datetime(dt)
        
        return {
            'lunar_year': zh_date.lunar_year,
            'lunar_month': zh_date.lunar_month,
            'lunar_day': zh_date.lunar_day,
            'is_leap': getattr(zh_date, 'is_leap', False),
            'library': 'zhdate'
        }
    
    def _solar_to_lunar_lunardate(self, year, month, day):
        """使用lunardate进行转换"""
        lunar_date = self.LunarDate.fromSolarDate(year, month, day)
        
        return {
            'lunar_year': lunar_date.year,
            'lunar_month': lunar_date.month,
            'lunar_day': lunar_date.day,
            'is_leap': getattr(lunar_date, 'isLeapMonth', False),
            'library': 'lunardate'
        }
    
    def calculate_ganzhi_day(self, year, month, day):
        """计算日柱干支"""
        # 使用传统算法计算日柱
        julian_day = date(year, month, day).toordinal() + 1721425
        
        # 甲子日的儒略日数（参考点：1900年1月31日是甲子日）
        jiazi_julian = 1924681
        
        # 计算距离甲子日的天数
        days_diff = julian_day - jiazi_julian
        
        # 60甲子循环
        ganzhi_index = days_diff % 60
        
        # 天干地支数组
        tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return tiangan[tg_index] + dizhi[dz_index]
    
    def calculate_ganzhi_year(self, year):
        """计算年柱干支"""
        # 以1984年甲子年为基准
        base_year = 1984
        year_diff = year - base_year
        
        # 60甲子循环
        ganzhi_index = year_diff % 60
        
        tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return tiangan[tg_index] + dizhi[dz_index]
    
    def get_complete_bazi_info(self, year, month, day, hour=12):
        """获取完整的八字信息"""
        # 农历信息
        lunar_info = self.solar_to_lunar(year, month, day)
        
        # 干支信息
        year_ganzhi = self.calculate_ganzhi_year(year)
        day_ganzhi = self.calculate_ganzhi_day(year, month, day)
        
        # 简化的月柱和时柱计算（实际应该更复杂）
        month_ganzhi = self._calculate_month_ganzhi(year, month)
        hour_ganzhi = self._calculate_hour_ganzhi(hour)
        
        return {
            'solar_date': f"{year}-{month:02d}-{day:02d}",
            'lunar_date': f"农历{lunar_info['lunar_year']}年{lunar_info['lunar_month']}月{lunar_info['lunar_day']}日",
            'year_pillar': year_ganzhi,
            'month_pillar': month_ganzhi,
            'day_pillar': day_ganzhi,
            'hour_pillar': hour_ganzhi,
            'is_leap_month': lunar_info['is_leap'],
            'library_used': lunar_info['library']
        }
    
    def _calculate_month_ganzhi(self, year, month):
        """简化的月柱计算"""
        # 这是简化版本，实际应该考虑节气
        year_tg_index = (year - 1984) % 10
        month_base = (year_tg_index * 2 + month - 1) % 60
        
        tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        dizhi = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑']
        
        tg_index = month_base % 10
        dz_index = (month - 1) % 12
        
        return tiangan[tg_index] + dizhi[dz_index]
    
    def _calculate_hour_ganzhi(self, hour):
        """简化的时柱计算"""
        # 时辰对应表
        hour_dizhi = ['子', '丑', '丑', '寅', '寅', '卯', '卯', '辰', '辰', 
                     '巳', '巳', '午', '午', '未', '未', '申', '申', '酉', 
                     '酉', '戌', '戌', '亥', '亥', '子']
        
        # 简化的天干计算
        tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        
        hour_index = hour // 2 if hour < 23 else 0
        tg_index = hour_index % 10
        
        return tiangan[tg_index] + hour_dizhi[hour]

def test_lunar_integration():
    """测试农历集成功能"""
    print("🌙 测试农历库集成功能")
    print("=" * 50)
    
    calculator = LunarDateCalculator()
    
    # 测试日期
    test_cases = [
        (1988, 3, 15, "甲辰年测试"),
        (1990, 7, 22, "庚午年测试"),
        (2024, 1, 1, "2024年元旦"),
        (2024, 2, 10, "2024年春节"),
    ]
    
    for year, month, day, desc in test_cases:
        print(f"\n📅 {desc}: {year}-{month:02d}-{day:02d}")
        print("-" * 40)
        
        try:
            # 基础农历转换
            lunar_info = calculator.solar_to_lunar(year, month, day)
            print(f"农历: {lunar_info['lunar_year']}年{lunar_info['lunar_month']}月{lunar_info['lunar_day']}日")
            print(f"闰月: {'是' if lunar_info['is_leap'] else '否'}")
            print(f"使用库: {lunar_info['library']}")
            
            # 干支计算
            day_ganzhi = calculator.calculate_ganzhi_day(year, month, day)
            year_ganzhi = calculator.calculate_ganzhi_year(year)
            print(f"年柱: {year_ganzhi}")
            print(f"日柱: {day_ganzhi}")
            
            # 完整八字信息
            bazi_info = calculator.get_complete_bazi_info(year, month, day)
            print(f"完整八字: {bazi_info['year_pillar']} {bazi_info['month_pillar']} {bazi_info['day_pillar']} {bazi_info['hour_pillar']}")
            
        except Exception as e:
            print(f"❌ 计算失败: {e}")

def compare_with_expected():
    """与期望结果对比"""
    print("\n🎯 与期望结果对比")
    print("=" * 50)
    
    calculator = LunarDateCalculator()
    
    # 已知的标准答案
    expected_cases = [
        {
            'solar': (1988, 3, 15),
            'expected_lunar': "农历1988年1月28日",
            'expected_day_ganzhi': "甲子",
            'expected_year_ganzhi': "戊辰"
        },
        {
            'solar': (1990, 7, 22),
            'expected_lunar': "农历1990年6月1日", 
            'expected_day_ganzhi': "丁卯",
            'expected_year_ganzhi': "庚午"
        }
    ]
    
    for case in expected_cases:
        year, month, day = case['solar']
        print(f"\n📅 测试: {year}-{month:02d}-{day:02d}")
        
        # 农历转换
        lunar_info = calculator.solar_to_lunar(year, month, day)
        actual_lunar = f"农历{lunar_info['lunar_year']}年{lunar_info['lunar_month']}月{lunar_info['lunar_day']}日"
        
        # 干支计算
        actual_day_ganzhi = calculator.calculate_ganzhi_day(year, month, day)
        actual_year_ganzhi = calculator.calculate_ganzhi_year(year)
        
        # 对比结果
        print(f"农历 - 期望: {case['expected_lunar']}")
        print(f"农历 - 实际: {actual_lunar}")
        print(f"农历 - 结果: {'✅' if actual_lunar == case['expected_lunar'] else '❌'}")
        
        print(f"年柱 - 期望: {case['expected_year_ganzhi']}")
        print(f"年柱 - 实际: {actual_year_ganzhi}")
        print(f"年柱 - 结果: {'✅' if actual_year_ganzhi == case['expected_year_ganzhi'] else '❌'}")
        
        print(f"日柱 - 期望: {case['expected_day_ganzhi']}")
        print(f"日柱 - 实际: {actual_day_ganzhi}")
        print(f"日柱 - 结果: {'✅' if actual_day_ganzhi == case['expected_day_ganzhi'] else '❌'}")

def main():
    """主函数"""
    print("🌟 农历库集成示例")
    print("=" * 60)
    
    # 测试集成功能
    test_lunar_integration()
    
    # 对比期望结果
    compare_with_expected()
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    print("✅ 成功集成农历转换功能")
    print("✅ 实现基础干支计算")
    print("💡 可以集成到八字项目中使用")
    print("🔧 建议继续优化月柱和时柱的计算算法")

if __name__ == "__main__":
    main()

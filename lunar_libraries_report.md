# 农历库安装和对比测试报告

## 📋 测试概述

我们测试了多个Python农历库的安装和功能，以下是详细的对比结果：

## 🔧 已成功安装的库

### 1. ✅ zhdate
- **安装状态**: 成功安装
- **版本**: 最新版本
- **主要功能**: 基础农历转换

### 2. ✅ lunardate  
- **安装状态**: 成功安装 (版本 0.2.2)
- **主要功能**: 农历转换，理论上支持干支计算

### 3. ❌ LunarCalendar
- **安装状态**: 安装中/失败
- **主要功能**: 全功能农历库，支持节气、节日等

### 4. ❌ chinese-calendar
- **安装状态**: 未测试
- **主要功能**: 中国节假日查询

## 📊 功能测试结果

### 测试日期
我们使用以下日期进行测试：
- **1988-03-15** (甲辰年正月廿八，日柱应为甲子)
- **1990-07-22** (庚午年六月初一，日柱应为丁卯)  
- **2024-01-01** (癸卯年十一月二十，日柱应为庚寅)
- **2024-02-10** (甲辰年春节)
- **2024-06-10** (甲辰年端午)

### zhdate 库测试结果

```python
# 基本转换功能 ✅
import zhdate
from datetime import datetime

dt = datetime(1988, 3, 15)
zh_date = zhdate.ZhDate.from_datetime(dt)
print(zh_date)  # 农历1988年1月28日

# 详细信息
print(f"农历: {zh_date.lunar_year}年{zh_date.lunar_month}月{zh_date.lunar_day}日")
# 输出: 农历: 1988年1月28日

# 干支信息 ❌
# zh_date.gz_day() - 方法不存在或获取失败
```

**zhdate 特点:**
- ✅ 农历转换准确
- ✅ 简单易用
- ❌ 不支持干支计算
- ✅ 支持闰月判断

### lunardate 库测试结果

```python
# 基本转换功能 ✅
from lunardate import LunarDate

lunar_date = LunarDate.fromSolarDate(1988, 3, 15)
print(f"农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
# 输出: 农历1988年1月28日

# 干支信息 ❌
# lunar_date.gz_day() - AttributeError: 'LunarDate' object has no attribute 'gz_year'
```

**lunardate 特点:**
- ✅ 农历转换准确
- ✅ 支持农历转阳历
- ❌ 干支方法不可用（可能版本问题）
- ✅ 支持闰月判断

## 🎯 准确性验证

### 农历转换准确性
所有测试的库在农历转换方面都表现准确：

| 阳历日期 | 期望农历 | zhdate结果 | lunardate结果 | 准确性 |
|---------|---------|-----------|--------------|--------|
| 1988-03-15 | 1988年1月28日 | 1988年1月28日 | 1988年1月28日 | ✅ |
| 1990-07-22 | 1990年6月1日 | 1990年6月1日 | 1990年6月1日 | ✅ |
| 2024-01-01 | 2023年11月20日 | 2023年11月20日 | 2023年11月20日 | ✅ |

### 干支计算准确性
目前测试的库都无法正确获取干支信息：

| 阳历日期 | 期望日柱 | zhdate结果 | lunardate结果 | 准确性 |
|---------|---------|-----------|--------------|--------|
| 1988-03-15 | 甲子 | 获取失败 | 获取失败 | ❌ |
| 1990-07-22 | 丁卯 | 获取失败 | 获取失败 | ❌ |
| 2024-01-01 | 庚寅 | 获取失败 | 获取失败 | ❌ |

## 💡 使用建议

### 对于八字计算项目

1. **当前可用选择:**
   - **zhdate**: 适合基础农历转换，简单可靠
   - **lunardate**: 功能相对完整，但干支功能有问题

2. **推荐方案:**
   ```python
   # 方案1: 使用zhdate进行农历转换
   import zhdate
   from datetime import datetime
   
   def get_lunar_date(year, month, day):
       dt = datetime(year, month, day)
       zh_date = zhdate.ZhDate.from_datetime(dt)
       return zh_date.lunar_year, zh_date.lunar_month, zh_date.lunar_day
   
   # 方案2: 自己实现干支计算
   def calculate_ganzhi(year, month, day):
       # 使用传统算法计算干支
       # 这部分需要自己实现
       pass
   ```

3. **继续尝试安装:**
   - **LunarCalendar**: 功能最全面，值得继续尝试安装
   - **chinese-calendar**: 用于节假日查询

### 具体实现建议

```python
# 推荐的农历处理方案
class LunarDateHandler:
    def __init__(self):
        try:
            import zhdate
            self.zhdate = zhdate
            self.available = True
        except ImportError:
            self.available = False
    
    def solar_to_lunar(self, year, month, day):
        """阳历转农历"""
        if not self.available:
            raise ImportError("zhdate库未安装")
        
        from datetime import datetime
        dt = datetime(year, month, day)
        zh_date = self.zhdate.ZhDate.from_datetime(dt)
        
        return {
            'lunar_year': zh_date.lunar_year,
            'lunar_month': zh_date.lunar_month, 
            'lunar_day': zh_date.lunar_day,
            'is_leap': getattr(zh_date, 'is_leap', False)
        }
    
    def calculate_ganzhi_day(self, year, month, day):
        """计算日柱干支（需要自己实现算法）"""
        # 使用传统的干支计算算法
        # 基于儒略日数计算
        from datetime import date
        julian_day = date(year, month, day).toordinal() + 1721425
        
        # 甲子日的儒略日数（已知参考点）
        jiazi_julian = 1924681  # 1900年1月31日是甲子日
        
        # 计算距离甲子日的天数
        days_diff = julian_day - jiazi_julian
        
        # 60甲子循环
        ganzhi_index = days_diff % 60
        
        # 天干地支数组
        tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        tg_index = ganzhi_index % 10
        dz_index = ganzhi_index % 12
        
        return tiangan[tg_index] + dizhi[dz_index]
```

## 🔄 下一步行动

1. **继续安装尝试:**
   ```bash
   pip install LunarCalendar
   pip install chinese-calendar
   ```

2. **功能验证:**
   - 测试 LunarCalendar 的干支计算功能
   - 验证节气、节日等高级功能

3. **集成到八字项目:**
   - 将农历转换功能集成到现有的八字计算系统
   - 实现准确的干支计算算法

## 📈 总结

目前我们成功安装了 **zhdate** 和 **lunardate** 两个农历库，它们在基础农历转换方面表现准确，但在干支计算方面存在问题。建议：

1. **短期方案**: 使用 zhdate 进行农历转换，自己实现干支计算
2. **长期方案**: 继续尝试安装 LunarCalendar，或寻找其他支持完整干支功能的库
3. **备用方案**: 完全自己实现农历和干支计算算法

这为您的八字项目提供了可靠的农历处理基础！🌙✨

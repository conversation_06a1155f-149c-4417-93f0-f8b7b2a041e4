#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础日期测试和农历库安装验证
"""

import sys
import subprocess
from datetime import datetime, date

def check_and_install_library(lib_name):
    """检查并安装库"""
    try:
        __import__(lib_name)
        print(f"✅ {lib_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {lib_name} 未安装，尝试安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", lib_name], 
                                timeout=30)
            print(f"✅ {lib_name} 安装成功")
            return True
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"❌ {lib_name} 安装失败: {e}")
            return False

def test_basic_date_operations():
    """测试基础日期操作"""
    print("📅 测试基础日期操作")
    print("=" * 40)
    
    # 测试日期
    test_dates = [
        "1988-03-15",
        "1990-07-22", 
        "2024-01-01",
        "2024-12-31"
    ]
    
    for date_str in test_dates:
        try:
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            print(f"  {date_str} → {dt.strftime('%Y年%m月%d日')} (周{dt.weekday()+1})")
            print(f"    年份: {dt.year}, 月份: {dt.month}, 日期: {dt.day}")
            print(f"    儒略日: {dt.toordinal()}")
            print()
        except Exception as e:
            print(f"  {date_str} → 解析失败: {e}")

def test_zhdate_if_available():
    """测试zhdate库（如果可用）"""
    print("🌙 测试 zhdate 库")
    print("=" * 40)
    
    try:
        import zhdate
        print("✅ zhdate 库可用")
        
        # 测试日期转换
        test_dates = [
            (1988, 3, 15),
            (1990, 7, 22),
            (2024, 1, 1),
        ]
        
        for year, month, day in test_dates:
            try:
                # 阳历转农历
                dt = datetime(year, month, day)
                zh_date = zhdate.ZhDate.from_datetime(dt)
                
                print(f"  {year}-{month:02d}-{day:02d} → {zh_date}")
                print(f"    农历: {zh_date.lunar_year}年{zh_date.lunar_month}月{zh_date.lunar_day}日")
                
                # 尝试获取干支
                if hasattr(zh_date, 'gz_year'):
                    print(f"    年干支: {zh_date.gz_year()}")
                if hasattr(zh_date, 'gz_month'):
                    print(f"    月干支: {zh_date.gz_month()}")
                if hasattr(zh_date, 'gz_day'):
                    print(f"    日干支: {zh_date.gz_day()}")
                
                print()
                
            except Exception as e:
                print(f"    转换失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ zhdate 库未安装")
        return False
    except Exception as e:
        print(f"❌ zhdate 测试失败: {e}")
        return False

def test_lunardate_if_available():
    """测试lunardate库（如果可用）"""
    print("🌙 测试 lunardate 库")
    print("=" * 40)
    
    try:
        from lunardate import LunarDate
        print("✅ lunardate 库可用")
        
        # 测试日期转换
        test_dates = [
            (1988, 3, 15),
            (1990, 7, 22),
            (2024, 1, 1),
        ]
        
        for year, month, day in test_dates:
            try:
                # 阳历转农历
                lunar_date = LunarDate.fromSolarDate(year, month, day)
                
                print(f"  {year}-{month:02d}-{day:02d} → 农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日")
                
                # 获取干支信息
                try:
                    print(f"    年干支: {lunar_date.gz_year()}")
                    print(f"    月干支: {lunar_date.gz_month()}")
                    print(f"    日干支: {lunar_date.gz_day()}")
                except Exception as e:
                    print(f"    干支获取失败: {e}")
                
                print()
                
            except Exception as e:
                print(f"    转换失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ lunardate 库未安装")
        return False
    except Exception as e:
        print(f"❌ lunardate 测试失败: {e}")
        return False

def test_lunar_calendar_if_available():
    """测试LunarCalendar库（如果可用）"""
    print("🌙 测试 LunarCalendar 库")
    print("=" * 40)
    
    try:
        from LunarCalendar import Converter, Solar
        print("✅ LunarCalendar 库可用")
        
        # 测试日期转换
        test_dates = [
            (1988, 3, 15),
            (1990, 7, 22),
            (2024, 1, 1),
        ]
        
        for year, month, day in test_dates:
            try:
                # 阳历转农历
                solar = Solar(year, month, day)
                lunar = Converter.Solar2Lunar(solar)
                
                print(f"  {year}-{month:02d}-{day:02d} → 农历{lunar.year}年{lunar.month}月{lunar.day}日")
                
                # 获取干支信息
                if hasattr(lunar, 'gz_year'):
                    print(f"    年干支: {lunar.gz_year}")
                if hasattr(lunar, 'gz_month'):
                    print(f"    月干支: {lunar.gz_month}")
                if hasattr(lunar, 'gz_day'):
                    print(f"    日干支: {lunar.gz_day}")
                
                print()
                
            except Exception as e:
                print(f"    转换失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ LunarCalendar 库未安装")
        return False
    except Exception as e:
        print(f"❌ LunarCalendar 测试失败: {e}")
        return False

def compare_libraries_output():
    """对比不同库的输出"""
    print("🔍 对比不同库的输出")
    print("=" * 40)
    
    test_date = (1988, 3, 15)
    print(f"测试日期: {test_date[0]}-{test_date[1]:02d}-{test_date[2]:02d}")
    print()
    
    results = {}
    
    # 测试 zhdate
    try:
        import zhdate
        dt = datetime(*test_date)
        zh_date = zhdate.ZhDate.from_datetime(dt)
        results["zhdate"] = f"农历{zh_date.lunar_year}年{zh_date.lunar_month}月{zh_date.lunar_day}日"
        if hasattr(zh_date, 'gz_day'):
            results["zhdate"] += f" 日柱:{zh_date.gz_day()}"
    except:
        results["zhdate"] = "不可用"
    
    # 测试 lunardate
    try:
        from lunardate import LunarDate
        lunar_date = LunarDate.fromSolarDate(*test_date)
        results["lunardate"] = f"农历{lunar_date.year}年{lunar_date.month}月{lunar_date.day}日"
        try:
            results["lunardate"] += f" 日柱:{lunar_date.gz_day()}"
        except:
            pass
    except:
        results["lunardate"] = "不可用"
    
    # 测试 LunarCalendar
    try:
        from LunarCalendar import Converter, Solar
        solar = Solar(*test_date)
        lunar = Converter.Solar2Lunar(solar)
        results["LunarCalendar"] = f"农历{lunar.year}年{lunar.month}月{lunar.day}日"
        if hasattr(lunar, 'gz_day'):
            results["LunarCalendar"] += f" 日柱:{lunar.gz_day}"
    except:
        results["LunarCalendar"] = "不可用"
    
    # 输出对比结果
    for lib_name, result in results.items():
        print(f"  {lib_name:15}: {result}")

def main():
    """主函数"""
    print("🌟 农历库安装和功能测试")
    print("=" * 50)
    
    # 测试基础日期操作
    test_basic_date_operations()
    
    print("\n" + "=" * 50)
    
    # 测试各个农历库
    available_libs = []
    
    if test_zhdate_if_available():
        available_libs.append("zhdate")
    
    if test_lunardate_if_available():
        available_libs.append("lunardate")
    
    if test_lunar_calendar_if_available():
        available_libs.append("LunarCalendar")
    
    print("\n" + "=" * 50)
    
    # 对比输出
    if available_libs:
        compare_libraries_output()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    if available_libs:
        print(f"✅ 可用的农历库: {', '.join(available_libs)}")
        
        if "lunardate" in available_libs:
            print("💡 推荐使用 lunardate: 提供完整的干支信息")
        elif "zhdate" in available_libs:
            print("💡 推荐使用 zhdate: 简单易用")
        elif "LunarCalendar" in available_libs:
            print("💡 推荐使用 LunarCalendar: 功能全面")
    else:
        print("❌ 没有可用的农历库")
        print("💡 建议手动安装:")
        print("   pip install zhdate")
        print("   pip install lunardate")
        print("   pip install LunarCalendar")

if __name__ == "__main__":
    main()

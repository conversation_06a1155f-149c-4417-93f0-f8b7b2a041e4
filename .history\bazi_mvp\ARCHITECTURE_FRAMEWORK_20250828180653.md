# 生辰八字占卜 MVP 架构与上线框架

## 1. 目标与范围
- **目标**: 在现有 `bazi_mvp` 的命令行原型基础上，发布可供用户使用的 Web 网站或微信/抖音等小程序版本，提供个人八字分析与合婚分析的在线服务。
- **范围**: 覆盖产品功能、系统架构、接口设计、数据安全与合规、部署与运维、质量保障、A/B与增长、迭代路线图。

## 2. 现有MVP核心框架（代码视角）
- **整体流程**: `用户输入 → 数据处理 → 八字计算 → 语料匹配 → 输出生成`
- **入口**: `bazi_mvp/main.py` 中 `BaziMVPSystem.run()` 组织单人与合婚两条主流程
- **核心模块**:
  - `modules/user_input.py`: 交互式输入收集（网站/小程序需要替换为前端表单/交互）
  - `modules/data_processor.py`: 输入标准化、校验、时区/真太阳时处理、质量元数据
  - `modules/bazi_calculator.py`: 四柱排盘、五行/十神/格局/大运（简化版）
  - `modules/corpus_matcher.py`: 根据计算结果匹配语料库文案
  - `modules/output_generator.py`: 报告装配与质量检查、概要总结展示
  - `modules/couple_input.py` + `modules/couple_compatibility.py`: 合婚输入与配对计算
  - `utils/calendar_converter.py`: 阳历↔阴历转换（含近似/查表与精度标注）
  - `data/corpus_database.py`: 语料数据源
  - `utils/logger.py`: 运行日志
- **日志与测试**: `logs/` 持久化，`test_*.py` 系列用例与集成测试脚本

## 3. 面向上线的目标架构（Web/小程序）
- **前端层**
  - Web: React/Vue/Nuxt/Next 任一；UI: Ant Design/Element/Naive UI
  - 小程序: 微信小程序/抖音小程序（原生或 Taro/uni-app 跨端）
  - 主要页面：
    - 引导页（产品价值、隐私合规说明）
    - 个人八字表单（姓名/性别/生日/时间精度/出生地/是否阴历）
    - 合婚表单（双方信息 + 关系问卷）
    - 报告页（概要 + 详细报告，可分享/导出PDF/图片）
    - 订单页（若商业化，支持下单/支付）
    - 个人中心（历史记录、收藏、隐私与授权管理）
- **服务层（API）**
  - 语言: Python（FastAPI/Flask）或 Node.js（NestJS）
  - 服务职责：
    - 输入验证与标准化（迁移 `DataProcessor` 能力）
    - 命理计算（封装 `BaziCalculator` / 合婚计算）
    - 语料与报告（封装 `CorpusMatcher` + `OutputGenerator`）
    - 用户/鉴权/限流/审计日志
    - 订单/支付/报告持久化
- **数据层**
  - DB: PostgreSQL/MySQL（结构化用户/订单/报告）、Redis（缓存/限流/队列）
  - 存储: 对象存储（报告PDF/图片）
- **运维与交付**
  - 容器化：Docker + Compose/K8s
  - 日志/监控：Prometheus + Grafana / OpenTelemetry + ELK
  - CI/CD：GitHub Actions/GitLab CI

## 4. API 设计（建议）
- **鉴权**
  - 匿名试用（无登录）+ 登录（短信/微信OpenID/AppleID）
  - Token: JWT（短时）+ 刷新令牌（服务端维护）
- **接口示例**
  - `POST /api/v1/bazi/analyze` 个人八字
    - 入参：姓名、性别、阳历/阴历、年月日、时间/时间精度、出生地
    - 出参：概要 summary、报告 report、质量指标、追踪ID
  - `POST /api/v1/couple/analyze` 合婚
  - `GET /api/v1/report/{id}` 查询报告
  - `POST /api/v1/payment/create` 下单支付（如需）
  - `GET /api/v1/meta/dicts` 获取字典（城市/时区/模糊时间枚举等）
- **错误码规范**
  - `OK(0)`、`INVALID_INPUT(1001)`、`RATE_LIMITED(1002)`、`UNAUTHORIZED(1401)`、`SERVER_ERROR(1500)`
- **性能要求**
  - P95 < 800ms（缓存命中）；未命中 < 2s；并发 100–500 QPS 可横向扩展

## 5. 前端交互与UX要点
- **输入容错**: 模糊时间（上午/下午/晚上）、未知时间（范围分析/默认12:00）
- **表单引导**: 步进式，展示精度对结果影响提示
- **报告可视化**: 四柱卡片、五行雷达/条形图、建议清单、合婚打分
- **保存与分享**: 生成可分享图/短链，导出PDF
- **可访问性与国际化**: i18n（中/英），高对比度与键盘可用性

## 6. 安全、隐私与合规
- **隐私最小化**: 姓名可选、脱敏日志、默认不长期存储（或需显式授权）
- **数据保留策略**: 可配置 TTL（如7/30天）与手动清除
- **加密**: HTTPS 全站、敏感字段加密存储（如手机号）
- **合规**: 中国境内合规（等保/网安法/个人信息保护法）、小程序平台规范、未成年人保护
- **审计**: 重要操作审计日志、数据访问审计
- **风控**: 频率限制、设备指纹（可选）、异常行为监测

## 7. 性能与成本优化
- **缓存**: 输入参数hash → 结果缓存（Redis），热门节日/时间段命中
- **预计算**: 常见出生年份与时间段的部分中间结果（如节气、日柱索引）
- **异步化**: 报告生成异步任务（队列），前端轮询/回调
- **冷启动**: 容器保活/预热、函数内常量/表缓存
- **静态资源**: CDN

## 8. 监控与可观测性
- **指标**: QPS、P95、错误率、缓存命中率、任务排队时长
- **日志**: 结构化日志（traceId、userId、reqId）、脱敏规范
- **追踪**: 分布式链路追踪（OpenTelemetry）
- **告警**: 阈值/异常模式告警（飞书/企业微信/钉钉）

## 9. 测试与质量保障
- **测试金字塔**
  - 单元：`bazi_calculator`、`calendar_converter`、`output_generator`
  - 集成：端到端分析流（含时间未知/范围分析）
  - 合同：API schema（OpenAPI校验）
  - 回归：关键用例集
- **质量门禁**: PR 必过测试、静态检查（flake8/ruff、mypy）、最小覆盖率阈值
- **报告质量保障**: `OutputGenerator._check_quality` + 自动补强；人工抽检

## 10. 数据与存储设计（建议）
- **表**
  - `users(id, phone/openid, nickname, created_at, ... )`
  - `reports(id, user_id, type(single|couple), input_hash, payload, quality, created_at)`
  - `orders(id, user_id, report_id, amount, status, channel, created_at)`
  - `audit_logs(id, user_id, action, meta, created_at)`
- **索引与归档**: `input_hash` 唯一索引便于缓存命中；历史报告归档/压缩

## 11. 商业化与增长（可选）
- **产品形态**: 免费摘要 + 付费完整报告/增值服务（情感/事业专项）
- **支付**: 微信支付/支付宝（小程序内）、Stripe（Web海外）
- **引流**: 可分享报告卡片、内容SEO、短视频引导落地页
- **A/B实验**: 表单顺序/价格/文案，埋点 + 指标看板

## 12. 运维与发布
- **环境**: dev / staging / prod 三套
- **配置**: 12-Factor（环境变量）、密钥管理（KMS/平台密钥）
- **发布**: 灰度/金丝雀；小程序提审分支与预览版
- **回滚**: 一键回滚、数据备份策略

## 13. 风险清单与对策
- **命理计算准确性争议**: 明确免责声明与精度标注（`calculation_metadata.accuracy_level`）
- **时间未知导致不确定性高**: 提供范围分析与“可信度”提示、引导完善信息
- **高并发峰值（节日）**: 预热 + 缓存 + 限流 + 异步化 + 扩容方案
- **平台合规变更**: 跟踪小程序规范更新，建立发布前合规检查表

## 14. 近期迭代路线图（建议）
- **V0.2（服务化）**: 用 FastAPI 包装现有计算链路，提供 `POST /bazi/analyze` 与 `POST /couple/analyze`
- **V0.3（前端可用）**: 完成 Web 表单与报告页；Redis 缓存；基础埋点
- **V0.4（小程序）**: 适配微信小程序，接入微信登录/支付；报告分享卡片
- **V0.5（质量与增长）**: 监控看板、A/B框架、自动化回归、商业化策略落地

## 15. 与现有代码的对接改造点
- 替换 `modules/user_input.py` 的交互，改为 API 入参校验层
- 将 `BaziMVPSystem` 的流程拆分为可复用的服务方法，供 API 调用
- 为 `calendar_converter` 增补更全年度数据表/第三方历法库（并保留精度元数据）
- `output_generator` 保持“质量检查 + 自动补强”机制，前端仅展示与格式化
- 提供 `input_hash`（标准化后）以提升缓存命中

## 16. 文档与运维清单
- 架构图、API 文档（OpenAPI/Swagger）、错误码表、埋点字典
- SRE 值班手册（告警、扩容、排错流程）、隐私与合规操作规程
- 上线 Checklist：域名与备案、HTTPS、隐私政策、用户协议、支付签约、备份与回滚演练

---

以上框架覆盖从原型到线上产品的关键路径，建议先完成服务化与最小可用前端，再逐步补齐合规、监控与商业化能力。 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试八字数据结构
import sys
import os
sys.path.append('.')

try:
    from modules.data_processor import DataProcessor
    from modules.bazi_calculator import BaziCalculator
    
    print("🔍 测试八字数据结构")
    print("=" * 40)
    
    # 测试数据
    user_data = {
        'name': '测试',
        'gender': '男', 
        'year': 1990,
        'month': 5,
        'day': 5,
        'hour': 1,
        'minute': 0,
        'birth_location': '东莞',
        'time_certainty': 'exact'
    }
    
    print("📝 输入数据:")
    print(f"   {user_data}")
    
    print("\n🔄 数据处理...")
    dp = DataProcessor()
    processed = dp.process(user_data)
    print(f"   处理结果键: {list(processed.keys())}")
    
    print("\n⚡ 八字计算...")
    bc = BaziCalculator()
    result = bc.calculate(processed)
    print(f"   八字结果键: {list(result.keys())}")
    
    if 'bazi_pillars' in result:
        pillars = result['bazi_pillars']
        print(f"\n📊 八字四柱:")
        for pillar_name, pillar_value in pillars.items():
            print(f"   {pillar_name}: {pillar_value}")
    
    if 'wuxing_analysis' in result:
        wuxing = result['wuxing_analysis']
        print(f"\n🌟 五行分析:")
        print(f"   日主五行: {wuxing.get('day_master_element', '未找到')}")
        print(f"   五行分布: {wuxing.get('element_count', {})}")
    
    # 测试提取函数
    print(f"\n🔧 测试数据提取...")
    
    def extract_bazi_for_compatibility(bazi_result, person_data):
        bazi_pillars = bazi_result.get('bazi_pillars', {})
        wuxing_analysis = bazi_result.get('wuxing_analysis', {})
        shishen_analysis = bazi_result.get('shishen_analysis', {})
        
        compatibility_data = {
            'name': person_data.get('name', '未知'),
            'year_pillar': bazi_pillars.get('year_pillar', ''),
            'month_pillar': bazi_pillars.get('month_pillar', ''),
            'day_pillar': bazi_pillars.get('day_pillar', ''),
            'hour_pillar': bazi_pillars.get('hour_pillar', ''),
            'day_master_element': wuxing_analysis.get('day_master_element', '未知'),
            'main_shishen': shishen_analysis.get('main_shishen', '未知')
        }
        
        return compatibility_data
    
    extracted = extract_bazi_for_compatibility(result, user_data)
    print(f"   提取的合婚数据:")
    for key, value in extracted.items():
        print(f"      {key}: {value}")
    
    print("\n✅ 测试完成")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试lunar_python库
"""

print("🔍 快速测试 lunar_python 库")

try:
    from lunar_python import Solar
    
    print("✅ lunar_python 导入成功")
    
    # 测试1988-03-15
    solar = Solar.fromYmd(1988, 3, 15)
    lunar = solar.getLunar()
    
    print(f"\n📅 测试日期: 1988-03-15")
    print(f"农历: {lunar.toString()}")
    
    # 获取八字
    eightChar = lunar.getEightChar()
    
    print(f"\n🎯 四柱八字:")
    print(f"年柱: {eightChar.getYearGanZhi()}")
    print(f"月柱: {eightChar.getMonthGanZhi()}")
    print(f"日柱: {eightChar.getDayGanZhi()}")
    print(f"时柱: {eightChar.getTimeGanZhi()}")
    
    print(f"\n🌟 五行:")
    print(f"年柱: {eightChar.getYearWuXing()}")
    print(f"月柱: {eightChar.getMonthWuXing()}")
    print(f"日柱: {eightChar.getDayWuXing()}")
    print(f"时柱: {eightChar.getTimeWuXing()}")
    
    print("\n✅ 测试成功！lunar_python库功能正常")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

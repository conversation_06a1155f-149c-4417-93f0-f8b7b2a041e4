import com.nlf.calendar.Solar;
import com.nlf.calendar.Lunar;
import com.nlf.calendar.eightchar.EightChar;
import java.util.Scanner;

/**
 * Java lunar-calendar库完整测试
 * 测试年月日时柱计算功能
 */
public class LunarCalendarTest {
    
    public static void main(String[] args) {
        System.out.println("🌙 Java lunar-calendar 库测试");
        System.out.println("============================================================");
        
        // 先测试几个固定日期
        testFixedDates();
        
        // 然后进入交互模式
        interactiveMode();
    }
    
    private static void testFixedDates() {
        System.out.println("\n🎬 演示标准案例:");
        
        int[][] testDates = {
            {1988, 3, 15, 12},
            {1990, 7, 22, 8},
            {2024, 1, 1, 12}
        };
        
        String[] descriptions = {
            "甲辰年测试",
            "庚午年测试", 
            "2024年元旦"
        };
        
        for (int i = 0; i < testDates.length; i++) {
            int[] date = testDates[i];
            System.out.println("\n📅 " + descriptions[i] + ": " + 
                             date[0] + "-" + String.format("%02d", date[1]) + "-" + 
                             String.format("%02d", date[2]) + " " + date[3] + "时");
            System.out.println("-".repeat(50));
            
            testLunarCalendar(date[0], date[1], date[2], date[3]);
        }
    }
    
    private static void interactiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🔄 交互模式 - 请输入您的日期");
        
        while (true) {
            try {
                System.out.print("\n请输入日期 (YYYY-MM-DD) 或 'quit' 退出: ");
                String input = scanner.nextLine().trim();
                
                if (input.toLowerCase().equals("quit") || input.toLowerCase().equals("q")) {
                    System.out.println("👋 再见！");
                    break;
                }
                
                // 解析日期
                String[] parts = input.split("-");
                if (parts.length != 3) {
                    System.out.println("❌ 格式错误，请使用 YYYY-MM-DD");
                    continue;
                }
                
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                
                System.out.print("时辰 (0-23，默认12): ");
                String hourInput = scanner.nextLine().trim();
                int hour = hourInput.isEmpty() ? 12 : Integer.parseInt(hourInput);
                
                System.out.println("\n" + "=".repeat(60));
                System.out.printf("🌟 测试日期: %d年%d月%d日 %d时%n", year, month, day, hour);
                System.out.println("=".repeat(60));
                
                testLunarCalendar(year, month, day, hour);
                
            } catch (NumberFormatException e) {
                System.out.println("❌ 日期格式错误");
            } catch (Exception e) {
                System.out.println("❌ 错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }
    
    private static void testLunarCalendar(int year, int month, int day, int hour) {
        System.out.println("🔍 测试 lunar-calendar 库");
        System.out.println("-".repeat(40));
        
        try {
            // 创建阳历对象
            Solar solar = new Solar(year, month, day, hour, 0, 0);
            
            // 转换为农历
            Lunar lunar = solar.getLunar();
            
            // 获取八字
            EightChar eightChar = lunar.getEightChar();
            
            System.out.println("✅ 状态: 成功");
            System.out.println("📅 农历: " + lunar.toString());
            
            System.out.println("\n🎯 四柱八字:");
            System.out.println("年柱: " + eightChar.getYear() + " (" + eightChar.getYearWuXing() + ") - " + eightChar.getYearShiShenGan());
            System.out.println("月柱: " + eightChar.getMonth() + " (" + eightChar.getMonthWuXing() + ") - " + eightChar.getMonthShiShenGan());
            System.out.println("日柱: " + eightChar.getDay() + " (" + eightChar.getDayWuXing() + ") - " + eightChar.getDayShiShenGan());
            System.out.println("时柱: " + eightChar.getTime() + " (" + eightChar.getTimeWuXing() + ") - " + eightChar.getTimeShiShenGan());
            
            System.out.println("\n🌟 详细信息:");
            System.out.println("胎元: " + eightChar.getTaiYuan());
            System.out.println("命宫: " + eightChar.getMingGong());
            System.out.println("身宫: " + eightChar.getShenGong());
            
            System.out.println("\n⭐ 纳音:");
            System.out.println("年柱纳音: " + eightChar.getYearNaYin());
            System.out.println("月柱纳音: " + eightChar.getMonthNaYin());
            System.out.println("日柱纳音: " + eightChar.getDayNaYin());
            System.out.println("时柱纳音: " + eightChar.getTimeNaYin());
            
        } catch (Exception e) {
            System.out.println("❌ 状态: 错误 - " + e.getMessage());
            e.printStackTrace();
        }
    }
}

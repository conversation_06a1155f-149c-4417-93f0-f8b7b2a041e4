#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的语料库系统
验证多样化语料输出功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.corpus_manager import corpus_manager
from modules.couple_compatibility import CoupleCompatibilityCalculator

def test_corpus_manager():
    """测试语料管理器的功能"""
    print("=== 测试语料管理器功能 ===\n")
    
    # 测试个性分析
    print("1. 测试个性分析语料：")
    personality_content = corpus_manager.get_personality_analysis('甲子', count=2)
    for i, content in enumerate(personality_content, 1):
        print(f"   {i}. {content}")
    print()
    
    # 测试合婚分析
    print("2. 测试合婚分析语料：")
    compatibility_analysis = corpus_manager.get_compatibility_analysis('high_compatibility', count=2)
    print(f"   总体分析: {compatibility_analysis['overall']}")
    print(f"   沟通建议: {compatibility_analysis['communication']}")
    print(f"   财务建议: {compatibility_analysis['financial']}")
    print(f"   事业建议: {compatibility_analysis['career']}")
    print()
    
    # 测试五行配对分析
    print("3. 测试五行配对分析：")
    wuxing_analysis = corpus_manager.get_wuxing_pairing_analysis('金', '水')
    print(f"   配对等级: {wuxing_analysis['compatibility']}")
    print(f"   描述: {wuxing_analysis['description']}")
    print(f"   优势: {wuxing_analysis['advantages']}")
    print(f"   建议: {wuxing_analysis['suggestions']}")
    print()
    
    # 测试生活建议
    print("4. 测试生活建议语料：")
    financial_advice = corpus_manager.get_life_advice('金', '财运提升建议', count=3)
    print("   财运建议:")
    for advice in financial_advice:
        print(f"   • {advice}")
    print()
    
    health_advice = corpus_manager.get_life_advice('木', '健康养生建议', count=3)
    print("   健康建议:")
    for advice in health_advice:
        print(f"   • {advice}")
    print()

def test_enhanced_compatibility_analysis():
    """测试增强的合婚分析功能"""
    print("=== 测试增强的合婚分析功能 ===\n")
    
    # 创建测试数据
    male_bazi = {
        'name': '张三',
        'birth_date': '1990-05-15 10:30',
        'year_pillar': '庚午',
        'month_pillar': '辛巳',
        'day_pillar': '甲子',
        'hour_pillar': '己巳'
    }
    
    female_bazi = {
        'name': '李四',
        'birth_date': '1992-08-20 14:20',
        'year_pillar': '壬申',
        'month_pillar': '戊申',
        'day_pillar': '丁卯',
        'hour_pillar': '丁未'
    }
    
    # 创建合婚计算器
    calculator = CoupleCompatibilityCalculator()
    
    # 进行合婚分析
    try:
        result = calculator.calculate_compatibility(male_bazi, female_bazi)
        
        print("合婚分析结果：")
        print(f"总分: {result['overall_score']}")
        print(f"等级: {result['compatibility_level']}")
        print("\n详细分析:")
        print(result['detailed_analysis'])
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_runs():
    """测试多次运行的语料多样性"""
    print("=== 测试语料多样性（多次运行） ===\n")
    
    print("连续3次获取个性分析，观察内容变化：")
    for i in range(3):
        print(f"\n第{i+1}次:")
        personality_content = corpus_manager.get_personality_analysis('丙子', count=1)
        for content in personality_content:
            print(f"   {content}")
    
    print("\n连续3次获取五行配对分析，观察内容变化：")
    for i in range(3):
        print(f"\n第{i+1}次:")
        wuxing_analysis = corpus_manager.get_wuxing_pairing_analysis('木', '火')
        print(f"   描述: {wuxing_analysis['description']}")

if __name__ == "__main__":
    print("开始测试增强的八字语料库系统...\n")
    
    # 测试语料管理器基本功能
    test_corpus_manager()
    
    # 测试增强的合婚分析
    test_enhanced_compatibility_analysis()
    
    # 测试多样性
    test_multiple_runs()
    
    print("\n测试完成！")

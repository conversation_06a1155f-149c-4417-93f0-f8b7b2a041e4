#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字合婚用户输入模块
处理两人生辰八字的输入收集
"""

from typing import Dict, Optional, Any
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.user_input import UserInputModule

class CoupleInputModule:
    def __init__(self):
        """初始化合婚输入模块"""
        self.user_input_module = UserInputModule()
        self.couple_data = {}
    
    def collect_couple_information(self) -> Optional[Dict[str, Any]]:
        """收集两人的生辰八字信息"""
        print("\n" + "=" * 60)
        print("💕 八字合婚 - 收集两人信息")
        print("=" * 60)
        print("我们将分别收集男方和女方的生辰八字信息")
        
        # 收集男方信息
        print("\n👨 第一步：收集男方信息")
        print("-" * 40)
        male_data = self._collect_person_data("男方")
        if not male_data:
            print("❌ 男方信息收集失败")
            return None
        
        # 收集女方信息
        print("\n👩 第二步：收集女方信息")
        print("-" * 40)
        female_data = self._collect_person_data("女方")
        if not female_data:
            print("❌ 女方信息收集失败")
            return None
        
        # 确认信息
        if self._confirm_couple_information(male_data, female_data):
            return {
                'male': male_data,
                'female': female_data,
                'analysis_type': 'couple_compatibility'
            }
        else:
            print("请重新输入信息")
            return self.collect_couple_information()
    
    def _collect_person_data(self, person_type: str) -> Optional[Dict[str, Any]]:
        """收集单个人的信息"""
        print(f"\n📝 收集{person_type}的基本信息")
        
        # 收集姓名
        while True:
            name = input(f"请输入{person_type}的姓名（或称呼）: ").strip()
            if name:
                break
            print("姓名不能为空，请重新输入")
        
        # 收集性别
        while True:
            gender_input = input(f"请输入{person_type}的性别（男/女）: ").strip()
            if gender_input in ['男', '女', 'M', 'F', 'm', 'f']:
                gender = '男' if gender_input.lower() in ['男', 'm'] else '女'
                break
            print("请输入 男 或 女")
        
        # 使用原有的用户输入模块收集详细信息
        print(f"\n📅 收集{person_type}的出生信息")
        
        # 临时设置用户输入模块的数据
        original_data = self.user_input_module.collected_data.copy()
        self.user_input_module.collected_data = {'name': name, 'gender': gender}
        
        try:
            # 收集出生日期
            birth_date = self.user_input_module._collect_birth_date()
            if not birth_date:
                return None
            
            # 收集出生时间
            birth_time = self.user_input_module._collect_birth_time()
            if not birth_time:
                return None
            
            # 收集出生地点
            birth_location = self.user_input_module._collect_birth_location()
            if not birth_location:
                return None
            
            # 整合数据
            person_data = {
                'name': name,
                'gender': gender,
                'year': birth_date['year'],
                'month': birth_date['month'],
                'day': birth_date['day'],
                'hour': birth_time.get('hour'),
                'minute': birth_time.get('minute', 0),
                'birth_location': birth_location,
                'time_certainty': birth_time.get('time_certainty', 'unknown'),
                'time_description': birth_time.get('time_description', ''),
                'lunar_info': birth_date.get('lunar_info')  # 阴历信息
            }
            
            return person_data
            
        finally:
            # 恢复原有数据
            self.user_input_module.collected_data = original_data
    
    def _confirm_couple_information(self, male_data: Dict[str, Any], 
                                  female_data: Dict[str, Any]) -> bool:
        """确认两人的信息"""
        print("\n" + "=" * 60)
        print("📋 请确认两人的生辰信息：")
        print("=" * 60)
        
        # 显示男方信息
        print(f"\n👨 男方信息：")
        self._display_person_info(male_data)
        
        # 显示女方信息
        print(f"\n👩 女方信息：")
        self._display_person_info(female_data)
        
        print("=" * 60)
        
        while True:
            confirm = input("以上信息是否正确？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是', '对']:
                return True
            elif confirm in ['n', 'no', '否', '不对']:
                return False
            else:
                print("请输入 y(确认) 或 n(重新输入)")
    
    def _display_person_info(self, person_data: Dict[str, Any]):
        """显示个人信息"""
        print(f"   姓名: {person_data['name']}")
        print(f"   性别: {person_data['gender']}")
        print(f"   阳历生日: {person_data['year']}年{person_data['month']}月{person_data['day']}日")
        
        # 显示阴历信息（如果有）
        if person_data.get('lunar_info'):
            lunar_info = person_data['lunar_info']
            print(f"   阴历生日: {lunar_info['lunar_date_full']}")
        
        # 显示出生时间
        if person_data.get('hour') is not None:
            print(f"   出生时间: {person_data['hour']}:{person_data['minute']:02d}")
            if person_data.get('time_description'):
                print(f"   时间描述: {person_data['time_description']}")
        else:
            print(f"   出生时间: {person_data.get('time_description', '未知')}")
        
        print(f"   出生地点: {person_data['birth_location']}")
        print(f"   时间精度: {person_data.get('time_certainty', 'unknown')}")
    
    def get_analysis_mode_choice(self) -> str:
        """获取用户选择的分析模式"""
        print("\n" + "=" * 60)
        print("🔮 生辰八字算命系统")
        print("=" * 60)
        print("请选择您要进行的分析类型：")
        print("1. 个人八字分析 - 分析一个人的生辰八字")
        print("2. 八字合婚分析 - 分析两人的八字配对")
        print("=" * 60)
        
        while True:
            choice = input("请输入您的选择 (1/2): ").strip()
            
            if choice == '1':
                print("\n✅ 您选择了：个人八字分析")
                return 'single'
            elif choice == '2':
                print("\n✅ 您选择了：八字合婚分析")
                return 'couple'
            else:
                print("❌ 无效选择，请输入 1 或 2")
    
    def collect_relationship_info(self) -> Dict[str, Any]:
        """收集关系信息"""
        print("\n💕 关系信息收集")
        print("-" * 30)
        
        # 收集关系状态
        print("请选择你们目前的关系状态：")
        print("1. 恋爱中")
        print("2. 准备结婚")
        print("3. 已婚")
        print("4. 其他")
        
        while True:
            status_choice = input("请选择 (1-4): ").strip()
            if status_choice in ['1', '2', '3', '4']:
                status_map = {
                    '1': '恋爱中',
                    '2': '准备结婚', 
                    '3': '已婚',
                    '4': '其他'
                }
                relationship_status = status_map[status_choice]
                break
            print("请输入有效选择 (1-4)")
        
        # 收集认识时间
        while True:
            try:
                know_months = input("你们认识多长时间了？(请输入月数，如：6): ").strip()
                if know_months:
                    know_months = int(know_months)
                    break
                else:
                    know_months = 0
                    break
            except ValueError:
                print("请输入有效的数字")
        
        # 收集主要关注点
        print("\n您最关心八字合婚的哪个方面？")
        print("1. 性格是否匹配")
        print("2. 事业发展是否互助")
        print("3. 婚姻是否稳定")
        print("4. 子女运势")
        print("5. 综合分析")
        
        while True:
            concern_choice = input("请选择 (1-5): ").strip()
            if concern_choice in ['1', '2', '3', '4', '5']:
                concern_map = {
                    '1': '性格匹配',
                    '2': '事业互助',
                    '3': '婚姻稳定',
                    '4': '子女运势',
                    '5': '综合分析'
                }
                main_concern = concern_map[concern_choice]
                break
            print("请输入有效选择 (1-5)")
        
        return {
            'relationship_status': relationship_status,
            'know_months': know_months,
            'main_concern': main_concern
        }

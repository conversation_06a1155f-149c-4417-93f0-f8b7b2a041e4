<?xml version="1.0" encoding="UTF-8"?>
<!--   
   Copyright (c) 2018, Oracle and/or its affiliates. All rights reserved.
   
   DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
   
   The contents of this file are subject to the terms of either the Universal Permissive License 
   v 1.0 as shown at http://oss.oracle.com/licenses/upl
   
   or the following license:
   
   Redistribution and use in source and binary forms, with or without modification, are permitted
   provided that the following conditions are met:
   
   1. Redistributions of source code must retain the above copyright notice, this list of conditions
   and the following disclaimer.
   
   2. Redistributions in binary form must reproduce the above copyright notice, this list of
   conditions and the following disclaimer in the documentation and/or other materials provided with
   the distribution.
   
   3. Neither the name of the copyright holder nor the names of its contributors may be used to
   endorse or promote products derived from this software without specific prior written permission.
   
   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
   IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
   FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
   CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
   DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
   WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
   WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<feature
      id="org.openjdk.jmc.feature.rcp.zh_CN"
      label="%name"
      version="7.1.1.202310112209"
      provider-name="%provider">

   <description url="%descriptionUrl">
      %description
   </description>

   <copyright>
      %copyright
   </copyright>
   <includes
         id="org.eclipse.babel.nls_eclipse_zh"
         version="4.12.0.v20190713060001"/>  

   <requires>
      <import feature="org.openjdk.jmc.feature.core" match="equivalent"/>
   </requires>

   <plugin
         id="org.openjdk.jmc.alert.zh_CN"
         download-size="8"
         install-size="13"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.browser.attach.zh_CN"
         download-size="10"
         install-size="19"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.browser.jdp.zh_CN"
         download-size="8"
         install-size="12"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.browser.zh_CN"
         download-size="15"
         install-size="27"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.console.persistence.zh_CN"
         download-size="22"
         install-size="27"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.console.ui.zh_CN"
         download-size="16"
         install-size="34"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.console.ui.mbeanbrowser.zh_CN"
         download-size="14"
         install-size="21"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.console.ui.notification.zh_CN"
         download-size="21"
         install-size="41"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.ui.zh_CN"
         download-size="25"
         install-size="42"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.docs.zh_CN"
         download-size="200"
         install-size="669"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.flightrecorder.ui.zh_CN"
         download-size="21"
         install-size="59"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.flightrecorder.controlpanel.ui.zh_CN"
         download-size="13"
         install-size="28"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.greychart.ui.zh_CN"
         download-size="7"
         install-size="10"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.rcp.application.zh_CN"
         download-size="9"
         install-size="15"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.rcp.intro.zh_CN"
         download-size="10"
         install-size="16"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.rjmx.zh_CN"
         download-size="16"
         install-size="30"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.rjmx.ui.zh_CN"
         download-size="16"
         install-size="28"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.openjdk.jmc.console.ui.diagnostic.zh_CN"
         download-size="11"
         install-size="14"
         version="7.1.1.202310112209"
         fragment="true"
         unpack="false"/>

<license url="%licenseUrl">
      %license
   </license></feature>
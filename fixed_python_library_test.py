#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Python农历库测试
专门测试Python库的年月日时柱功能
"""

import sys
import os
import datetime

def test_lunar_python(year, month, day, hour=12):
    """测试lunar_python库"""
    print("🔍 测试 lunar_python 库")
    print("-" * 40)
    
    try:
        from lunar_python import Solar
        
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        eightChar = lunar.getEightChar()
        
        print("✅ 状态: 成功")
        print(f"📅 农历: {lunar.toString()}")
        print(f"🎯 年柱: {eightChar.getYear()} ({eightChar.getYearWuXing()}) - {eightChar.getYearShiShenGan()}")
        print(f"🎯 月柱: {eightChar.getMonth()} ({eightChar.getMonthWuXing()}) - {eightChar.getMonthShiShenGan()}")
        print(f"🎯 日柱: {eightChar.getDay()} ({eightChar.getDayWuXing()}) - {eightChar.getDayShiShenGan()}")
        print(f"🎯 时柱: {eightChar.getTime()} ({eightChar.getTimeWuXing()}) - {eightChar.getTimeShiShenGan()}")
        
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def test_zhdate(year, month, day, hour=12):
    """测试zhdate库 - 修复日期处理问题"""
    print("\n🔍 测试 zhdate 库")
    print("-" * 40)
    
    try:
        import zhdate
        
        # 修复：使用正确的日期创建方法
        solar_date = datetime.date(year, month, day)
        lunar = zhdate.ZhDate.from_datetime(solar_date)
        
        print("✅ 状态: 成功")
        print(f"📅 农历: 农历{lunar.lunar_year}年{lunar.lunar_month}月{lunar.lunar_day}日")
        
        # 检查干支功能
        print("🔍 检查干支功能:")
        ganzhi_found = False
        
        # 检查可能的干支方法
        ganzhi_methods = ['to_ganzhi', 'get_ganzhi', 'ganzhi']
        for method_name in ganzhi_methods:
            if hasattr(lunar, method_name):
                try:
                    method = getattr(lunar, method_name)
                    if callable(method):
                        result = method()
                        print(f"  {method_name}(): {result}")
                        ganzhi_found = True
                except Exception as e:
                    print(f"  {method_name}(): 调用失败 - {e}")
        
        # 检查干支属性
        ganzhi_attrs = ['year_ganzhi', 'month_ganzhi', 'day_ganzhi', 'gz_year', 'gz_month', 'gz_day']
        for attr in ganzhi_attrs:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    print(f"  {attr}: {value}")
                    ganzhi_found = True
                except Exception as e:
                    print(f"  {attr}: 获取失败 - {e}")
        
        if not ganzhi_found:
            print("  ❌ 不支持干支计算")
            
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def test_lunardate(year, month, day, hour=12):
    """测试lunardate库 - 检查干支功能"""
    print("\n🔍 测试 lunardate 库")
    print("-" * 40)
    
    try:
        import lunardate
        
        lunar = lunardate.LunarDate.fromSolarDate(year, month, day)
        
        print("✅ 状态: 成功")
        print(f"📅 农历: 农历{lunar.year}年{lunar.month}月{lunar.day}日")
        
        # 检查干支功能
        print("🔍 检查干支功能:")
        ganzhi_found = False
        
        # 检查所有属性和方法
        all_attrs = dir(lunar)
        ganzhi_related = [attr for attr in all_attrs if 'gan' in attr.lower() or 'zhi' in attr.lower() or 'ganzhi' in attr.lower()]
        
        for attr in ganzhi_related:
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if callable(value):
                        try:
                            result = value()
                            print(f"  {attr}(): {result}")
                            ganzhi_found = True
                        except:
                            pass
                    else:
                        print(f"  {attr}: {value}")
                        ganzhi_found = True
                except Exception as e:
                    print(f"  {attr}: 获取失败 - {e}")
        
        # 检查是否有年月日柱相关方法
        pillar_methods = ['year_pillar', 'month_pillar', 'day_pillar', 'time_pillar', 
                         'yearPillar', 'monthPillar', 'dayPillar', 'timePillar']
        for method in pillar_methods:
            if hasattr(lunar, method):
                try:
                    value = getattr(lunar, method)
                    if callable(value):
                        result = value()
                        print(f"  {method}(): {result}")
                        ganzhi_found = True
                    else:
                        print(f"  {method}: {value}")
                        ganzhi_found = True
                except Exception as e:
                    print(f"  {method}: 获取失败 - {e}")
        
        if not ganzhi_found:
            print("  ❌ 不支持干支计算")
            
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def test_sxtwl(year, month, day, hour=12):
    """测试sxtwl库 - 检查干支功能"""
    print("\n🔍 测试 sxtwl 库")
    print("-" * 40)
    
    try:
        import sxtwl
        
        # 创建日期对象
        day_obj = sxtwl.fromSolar(year, month, day)
        
        print("✅ 状态: 成功")
        print(f"📅 阳历: {year}年{month}月{day}日")
        
        # 检查干支功能
        print("🔍 检查干支功能:")
        ganzhi_found = False
        
        # 检查所有属性
        all_attrs = dir(day_obj)
        for attr in all_attrs:
            if not attr.startswith('_'):
                try:
                    value = getattr(day_obj, attr)
                    if not callable(value):
                        print(f"  {attr}: {value}")
                        if 'gan' in attr.lower() or 'zhi' in attr.lower() or any(x in str(value) for x in ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']):
                            ganzhi_found = True
                except Exception as e:
                    pass
        
        # 尝试获取农历信息
        try:
            lunar_year = getattr(day_obj, 'Lyear', None)
            lunar_month = getattr(day_obj, 'Lmonth', None)
            lunar_day = getattr(day_obj, 'Lday', None)
            
            if lunar_year and lunar_month and lunar_day:
                print(f"📅 农历: {lunar_year}年{lunar_month}月{lunar_day}日")
        except:
            pass
        
        # 检查干支相关方法
        ganzhi_methods = ['getGanZhi', 'getDayGanZhi', 'getYearGanZhi', 'getMonthGanZhi']
        for method in ganzhi_methods:
            if hasattr(day_obj, method):
                try:
                    result = getattr(day_obj, method)()
                    print(f"  {method}(): {result}")
                    ganzhi_found = True
                except Exception as e:
                    print(f"  {method}(): 调用失败 - {e}")
        
        if not ganzhi_found:
            print("  ❌ 不支持干支计算或需要进一步配置")
            
        return True
        
    except ImportError:
        print("❌ 状态: 未安装")
        return False
    except Exception as e:
        print(f"❌ 状态: 错误 - {e}")
        return False

def main():
    """主函数"""
    print("🌙 修复版Python农历库测试工具")
    print("=" * 60)
    print("测试库: lunar_python, zhdate, lunardate, sxtwl")
    print("重点检查: 年月日时柱计算功能")
    print("=" * 60)
    
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    while True:
        try:
            print("\n请输入要测试的日期:")
            
            # 获取用户输入
            date_input = input("日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
            
            if date_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            try:
                date_parts = date_input.split('-')
                if len(date_parts) != 3:
                    print("❌ 格式错误，请使用 YYYY-MM-DD")
                    continue
                
                year, month, day = map(int, date_parts)
                
                # 获取时辰
                hour_input = input("时辰 (0-23，默认12): ").strip()
                hour = int(hour_input) if hour_input else 12
                
            except ValueError:
                print("❌ 日期格式错误")
                continue
            
            print(f"\n{'='*60}")
            print(f"🌟 测试日期: {year}年{month}月{day}日 {hour}时")
            print(f"{'='*60}")
            
            # 测试所有Python库
            lunar_python_ok = test_lunar_python(year, month, day, hour)
            zhdate_ok = test_zhdate(year, month, day, hour)
            lunardate_ok = test_lunardate(year, month, day, hour)
            sxtwl_ok = test_sxtwl(year, month, day, hour)
            
            # 总结推荐
            print(f"\n{'='*60}")
            print("💡 总结:")
            if lunar_python_ok:
                print("🌟 lunar_python - 完整的年月日时柱、五行、十神计算 ✅")
            if zhdate_ok:
                print("📦 zhdate - 基础农历转换")
            if lunardate_ok:
                print("📦 lunardate - 基础农历转换")
            if sxtwl_ok:
                print("📦 sxtwl - 天文历法库")
            
            print("\n🎯 推荐使用: lunar_python (唯一支持完整干支计算的库)")
            print(f"{'='*60}")
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()

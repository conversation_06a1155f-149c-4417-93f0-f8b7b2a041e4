#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合农历库测试脚本
输入日期，展示所有可用库的计算结果对比
包括：lunar_python, LunarCalendar, zhdate, lunardate, sxtwl等
"""

import sys
import os

def test_lunar_python(year, month, day, hour=12):
    """测试lunar_python库"""
    try:
        from lunar_python import Solar
        
        solar = Solar.fromYmd(year, month, day)
        lunar = solar.getLunar()
        eightChar = lunar.getEightChar()
        
        return {
            'library': 'lunar_python',
            'status': '✅ 成功',
            'lunar_date': lunar.toString(),
            'year_pillar': eightChar.getYear(),
            'month_pillar': eightChar.getMonth(),
            'day_pillar': eightChar.getDay(),
            'time_pillar': eightChar.getTime(),
            'year_wuxing': eightChar.getYearWuXing(),
            'month_wuxing': eightChar.getMonthWuXing(),
            'day_wuxing': eightChar.getDayWuXing(),
            'time_wuxing': eightChar.getTimeWuXing(),
            'year_shishen': eightChar.getYearShiShenGan(),
            'month_shishen': eightChar.getMonthShiShenGan(),
            'day_shishen': eightChar.getDayShiShenGan(),
            'time_shishen': eightChar.getTimeShiShenGan()
        }
    except ImportError:
        return {'library': 'lunar_python', 'status': '❌ 未安装', 'error': '库未找到'}
    except Exception as e:
        return {'library': 'lunar_python', 'status': '❌ 错误', 'error': str(e)}

def test_lunar_calendar(year, month, day, hour=12):
    """测试LunarCalendar库"""
    try:
        from LunarCalendar import Converter, Solar
        
        solar = Solar(year, month, day)
        lunar = Converter.Solar2Lunar(solar)
        
        # 检查干支属性
        ganzhi_info = {}
        ganzhi_attrs = ['gz_year', 'gz_month', 'gz_day', 'gzYear', 'gzMonth', 'gzDay']
        
        for attr in ganzhi_attrs:
            if hasattr(lunar, attr):
                try:
                    value = getattr(lunar, attr)
                    ganzhi_info[attr] = value
                except:
                    pass
        
        return {
            'library': 'LunarCalendar',
            'status': '✅ 成功',
            'lunar_date': f"农历{lunar.year}年{lunar.month}月{lunar.day}日",
            'lunar_year': lunar.year,
            'lunar_month': lunar.month,
            'lunar_day': lunar.day,
            'ganzhi_attrs': ganzhi_info,
            'has_ganzhi': len(ganzhi_info) > 0,
            'note': '基础农历转换，不支持干支计算'
        }
    except ImportError:
        return {'library': 'LunarCalendar', 'status': '❌ 未安装', 'error': '库未找到'}
    except Exception as e:
        return {'library': 'LunarCalendar', 'status': '❌ 错误', 'error': str(e)}

def test_zhdate(year, month, day, hour=12):
    """测试zhdate库"""
    try:
        import zhdate
        
        lunar = zhdate.ZhDate.from_datetime(zhdate.datetime.date(year, month, day))
        
        # 检查干支方法
        ganzhi_methods = []
        for method in ['to_ganzhi', 'get_ganzhi', 'ganzhi']:
            if hasattr(lunar, method):
                try:
                    result = getattr(lunar, method)()
                    ganzhi_methods.append(f"{method}: {result}")
                except:
                    pass
        
        return {
            'library': 'zhdate',
            'status': '✅ 成功',
            'lunar_date': f"农历{lunar.lunar_year}年{lunar.lunar_month}月{lunar.lunar_day}日",
            'lunar_year': lunar.lunar_year,
            'lunar_month': lunar.lunar_month,
            'lunar_day': lunar.lunar_day,
            'ganzhi_methods': ganzhi_methods,
            'has_ganzhi': len(ganzhi_methods) > 0,
            'note': '基础农历转换'
        }
    except ImportError:
        return {'library': 'zhdate', 'status': '❌ 未安装', 'error': '库未找到'}
    except Exception as e:
        return {'library': 'zhdate', 'status': '❌ 错误', 'error': str(e)}

def test_lunardate(year, month, day, hour=12):
    """测试lunardate库"""
    try:
        import lunardate
        
        lunar = lunardate.LunarDate.fromSolarDate(year, month, day)
        
        # 检查干支方法
        ganzhi_info = {}
        for attr in dir(lunar):
            if 'gan' in attr.lower() or 'zhi' in attr.lower() or 'ganzhi' in attr.lower():
                try:
                    if not attr.startswith('_') and not callable(getattr(lunar, attr)):
                        value = getattr(lunar, attr)
                        ganzhi_info[attr] = value
                except:
                    pass
        
        return {
            'library': 'lunardate',
            'status': '✅ 成功',
            'lunar_date': f"农历{lunar.year}年{lunar.month}月{lunar.day}日",
            'lunar_year': lunar.year,
            'lunar_month': lunar.month,
            'lunar_day': lunar.day,
            'ganzhi_attrs': ganzhi_info,
            'has_ganzhi': len(ganzhi_info) > 0,
            'note': '基础农历转换'
        }
    except ImportError:
        return {'library': 'lunardate', 'status': '❌ 未安装', 'error': '库未找到'}
    except Exception as e:
        return {'library': 'lunardate', 'status': '❌ 错误', 'error': str(e)}

def test_sxtwl(year, month, day, hour=12):
    """测试sxtwl库"""
    try:
        import sxtwl
        
        lunar = sxtwl.fromSolar(year, month, day)
        
        # 检查干支方法
        ganzhi_info = {}
        for attr in dir(lunar):
            if not attr.startswith('_'):
                try:
                    value = getattr(lunar, attr)
                    if not callable(value):
                        ganzhi_info[attr] = value
                except:
                    pass
        
        return {
            'library': 'sxtwl',
            'status': '✅ 成功',
            'lunar_info': ganzhi_info,
            'has_ganzhi': 'Lday' in ganzhi_info or 'Lmonth' in ganzhi_info,
            'note': '专业天文历法库'
        }
    except ImportError:
        return {'library': 'sxtwl', 'status': '❌ 未安装', 'error': '库未找到'}
    except Exception as e:
        return {'library': 'sxtwl', 'status': '❌ 错误', 'error': str(e)}

def display_results(results, year, month, day, hour):
    """显示所有库的测试结果"""
    print(f"\n{'='*80}")
    print(f"🌟 农历库对比测试结果 - {year}年{month}月{day}日 {hour}时")
    print(f"{'='*80}")
    
    for result in results:
        print(f"\n📚 {result['library']} 库:")
        print(f"状态: {result['status']}")
        
        if result['status'].startswith('✅'):
            if result['library'] == 'lunar_python':
                print(f"农历: {result['lunar_date']}")
                print(f"年柱: {result['year_pillar']} ({result['year_wuxing']}) - {result['year_shishen']}")
                print(f"月柱: {result['month_pillar']} ({result['month_wuxing']}) - {result['month_shishen']}")
                print(f"日柱: {result['day_pillar']} ({result['day_wuxing']}) - {result['day_shishen']}")
                print(f"时柱: {result['time_pillar']} ({result['time_wuxing']}) - {result['time_shishen']}")
                
            elif result['library'] in ['LunarCalendar', 'zhdate', 'lunardate']:
                print(f"农历: {result['lunar_date']}")
                if result['has_ganzhi']:
                    if 'ganzhi_attrs' in result:
                        for attr, value in result['ganzhi_attrs'].items():
                            print(f"  {attr}: {value}")
                    if 'ganzhi_methods' in result:
                        for method_result in result['ganzhi_methods']:
                            print(f"  {method_result}")
                else:
                    print(f"  干支计算: {result['note']}")
                    
            elif result['library'] == 'sxtwl':
                print(f"天文信息: {result.get('lunar_info', {})}")
                print(f"干支支持: {'是' if result['has_ganzhi'] else '否'}")
        else:
            print(f"错误: {result.get('error', '未知错误')}")
        
        print("-" * 60)

def main():
    """主函数"""
    print("🌙 综合农历库测试工具")
    print("=" * 60)
    print("支持的库: lunar_python, LunarCalendar, zhdate, lunardate, sxtwl")
    print("=" * 60)
    
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    while True:
        try:
            print("\n请输入要测试的日期:")
            
            # 获取用户输入
            date_input = input("日期 (YYYY-MM-DD) 或 'quit' 退出: ").strip()
            
            if date_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            # 解析日期
            try:
                date_parts = date_input.split('-')
                if len(date_parts) != 3:
                    print("❌ 格式错误，请使用 YYYY-MM-DD")
                    continue
                
                year, month, day = map(int, date_parts)
                
                # 获取时辰
                hour_input = input("时辰 (0-23，默认12): ").strip()
                hour = int(hour_input) if hour_input else 12
                
            except ValueError:
                print("❌ 日期格式错误")
                continue
            
            # 测试所有库
            print(f"\n🔄 正在测试所有库...")
            
            test_functions = [
                test_lunar_python,
                test_lunar_calendar,
                test_zhdate,
                test_lunardate,
                test_sxtwl
            ]
            
            results = []
            for test_func in test_functions:
                try:
                    result = test_func(year, month, day, hour)
                    results.append(result)
                except Exception as e:
                    results.append({
                        'library': test_func.__name__.replace('test_', ''),
                        'status': '❌ 测试失败',
                        'error': str(e)
                    })
            
            # 显示结果
            display_results(results, year, month, day, hour)
            
            # 推荐
            print(f"\n💡 推荐使用:")
            working_libs = [r for r in results if r['status'].startswith('✅')]
            if any(r['library'] == 'lunar_python' for r in working_libs):
                print("🌟 lunar_python - 完整的年月日时柱、五行、十神计算")
            else:
                print("📦 其他可用库仅支持基础农历转换")
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()

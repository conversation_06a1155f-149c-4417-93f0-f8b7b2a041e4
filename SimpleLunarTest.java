import com.nlf.calendar.Solar;
import com.nlf.calendar.Lunar;
import com.nlf.calendar.eightchar.EightChar;
import java.util.Scanner;

/**
 * Simple Java lunar-calendar library test
 * Test year/month/day/time pillar calculations
 */
public class SimpleLunarTest {
    
    public static void main(String[] args) {
        System.out.println("Java lunar-calendar Library Test");
        System.out.println("============================================================");
        
        // Test fixed dates first
        testFixedDates();
        
        // Interactive mode
        interactiveMode();
    }
    
    private static void testFixedDates() {
        System.out.println("\nDemo Cases:");
        
        int[][] testDates = {
            {1988, 3, 15, 12},
            {1990, 7, 22, 8},
            {2024, 1, 1, 12}
        };
        
        String[] descriptions = {
            "Test 1988-03-15",
            "Test 1990-07-22", 
            "Test 2024-01-01"
        };
        
        for (int i = 0; i < testDates.length; i++) {
            int[] date = testDates[i];
            System.out.println("\n" + descriptions[i] + ": " + 
                             date[0] + "-" + String.format("%02d", date[1]) + "-" + 
                             String.format("%02d", date[2]) + " " + date[3] + ":00");
            System.out.println("--------------------------------------------------");
            
            testLunarCalendar(date[0], date[1], date[2], date[3]);
        }
    }
    
    private static void interactiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n============================================================");
        System.out.println("Interactive Mode - Enter your date");
        
        while (true) {
            try {
                System.out.print("\nEnter date (YYYY-MM-DD) or 'quit' to exit: ");
                String input = scanner.nextLine().trim();
                
                if (input.toLowerCase().equals("quit") || input.toLowerCase().equals("q")) {
                    System.out.println("Goodbye!");
                    break;
                }
                
                // Parse date
                String[] parts = input.split("-");
                if (parts.length != 3) {
                    System.out.println("Error: Please use YYYY-MM-DD format");
                    continue;
                }
                
                int year = Integer.parseInt(parts[0]);
                int month = Integer.parseInt(parts[1]);
                int day = Integer.parseInt(parts[2]);
                
                System.out.print("Hour (0-23, default 12): ");
                String hourInput = scanner.nextLine().trim();
                int hour = hourInput.isEmpty() ? 12 : Integer.parseInt(hourInput);
                
                System.out.println("\n============================================================");
                System.out.printf("Test Date: %d-%02d-%02d %02d:00%n", year, month, day, hour);
                System.out.println("============================================================");
                
                testLunarCalendar(year, month, day, hour);
                
            } catch (NumberFormatException e) {
                System.out.println("Error: Invalid date format");
            } catch (Exception e) {
                System.out.println("Error: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        scanner.close();
    }
    
    private static void testLunarCalendar(int year, int month, int day, int hour) {
        System.out.println("Testing lunar-calendar library");
        System.out.println("----------------------------------------");
        
        try {
            // Create Solar object
            Solar solar = new Solar(year, month, day, hour, 0, 0);
            
            // Convert to Lunar
            Lunar lunar = solar.getLunar();
            
            // Get EightChar
            EightChar eightChar = lunar.getEightChar();
            
            System.out.println("Status: SUCCESS");
            System.out.println("Lunar Date: " + lunar.toString());
            
            System.out.println("\nFour Pillars (BaZi):");
            System.out.println("Year Pillar: " + eightChar.getYear() + " (" + eightChar.getYearWuXing() + ") - " + eightChar.getYearShiShenGan());
            System.out.println("Month Pillar: " + eightChar.getMonth() + " (" + eightChar.getMonthWuXing() + ") - " + eightChar.getMonthShiShenGan());
            System.out.println("Day Pillar: " + eightChar.getDay() + " (" + eightChar.getDayWuXing() + ") - " + eightChar.getDayShiShenGan());
            System.out.println("Time Pillar: " + eightChar.getTime() + " (" + eightChar.getTimeWuXing() + ") - " + eightChar.getTimeShiShenGan());
            
            System.out.println("\nAdditional Info:");
            System.out.println("TaiYuan: " + eightChar.getTaiYuan());
            System.out.println("MingGong: " + eightChar.getMingGong());
            System.out.println("ShenGong: " + eightChar.getShenGong());
            
            System.out.println("\nNaYin:");
            System.out.println("Year NaYin: " + eightChar.getYearNaYin());
            System.out.println("Month NaYin: " + eightChar.getMonthNaYin());
            System.out.println("Day NaYin: " + eightChar.getDayNaYin());
            System.out.println("Time NaYin: " + eightChar.getTimeNaYin());
            
        } catch (Exception e) {
            System.out.println("Status: ERROR - " + e.getMessage());
            e.printStackTrace();
        }
    }
}

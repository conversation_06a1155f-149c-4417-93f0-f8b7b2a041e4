#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一致性系统
验证同样的八字输入产生一致但多样化的结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.enhanced_output_generator import enhanced_output_generator
from data.core_logic_mapping import get_rizhu_core_logic, get_wuxing_pairing_core
from data.consistency_checker import consistency_checker

def test_personality_consistency():
    """测试个性分析的一致性"""
    print("=" * 60)
    print("🎭 个性分析一致性测试")
    print("=" * 60)
    
    test_day_pillars = ['甲子', '丙寅', '庚午', '癸酉']
    
    for day_pillar in test_day_pillars:
        print(f"\n【{day_pillar}日柱一致性测试】")
        print("-" * 30)
        
        # 获取核心逻辑
        core_logic = get_rizhu_core_logic(day_pillar)
        print(f"核心特征: {core_logic.get('core_personality', [])}")
        print(f"五行关系: {core_logic.get('core_element_relation', '')}")
        print(f"基本趋势: {core_logic.get('core_tendency', '')}")
        
        print("\n不同版本的分析结果:")
        
        # 生成3个不同版本的分析
        for i in range(1, 4):
            analysis = enhanced_output_generator.generate_personality_analysis(day_pillar, i)
            print(f"\n版本{i}:")
            print(f"  {analysis}")
            
            # 检查一致性
            is_consistent, errors = consistency_checker.check_rizhu_consistency(day_pillar, analysis)
            if not is_consistent:
                print(f"  ⚠️ 一致性问题: {errors}")
            else:
                print(f"  ✅ 一致性检查通过")
        
        print()

def test_compatibility_consistency():
    """测试合婚分析的一致性"""
    print("=" * 60)
    print("💕 合婚分析一致性测试")
    print("=" * 60)
    
    test_pairings = [
        ('金', '水', 88),
        ('木', '火', 92),
        ('火', '土', 85),
        ('土', '金', 83)
    ]
    
    for element1, element2, score in test_pairings:
        print(f"\n【{element1}{element2}配对一致性测试 - {score}分】")
        print("-" * 30)
        
        # 获取核心逻辑
        core_logic = get_wuxing_pairing_core(element1, element2)
        print(f"关系类型: {core_logic.get('relation_type', '')}")
        print(f"配对等级: {core_logic.get('compatibility_level', '')}")
        print(f"核心优势: {core_logic.get('core_advantage', '')}")
        print(f"稳定分数: {core_logic.get('stability_score', 0)}")
        
        print("\n不同版本的分析结果:")
        
        # 生成3个不同版本的分析
        for i in range(1, 4):
            analysis = enhanced_output_generator.generate_compatibility_analysis(element1, element2, score, i)
            print(f"\n版本{i}:")
            print(f"  {analysis}")
            
            # 检查一致性
            is_consistent, errors = consistency_checker.check_wuxing_pairing_consistency(element1, element2, analysis)
            if not is_consistent:
                print(f"  ⚠️ 一致性问题: {errors}")
            else:
                print(f"  ✅ 一致性检查通过")
        
        print()

def test_comprehensive_report_consistency():
    """测试综合报告的一致性"""
    print("=" * 60)
    print("📋 综合报告一致性测试")
    print("=" * 60)
    
    # 创建测试数据
    male_bazi = {
        'name': '张先生',
        'day_pillar': '甲子',
        'birth_date': '1988-03-15'
    }
    
    female_bazi = {
        'name': '李女士', 
        'day_pillar': '丁卯',
        'birth_date': '1990-07-22'
    }
    
    compatibility_result = {
        'overall_score': 85
    }
    
    print("测试数据:")
    print(f"男方: {male_bazi['name']} - {male_bazi['day_pillar']}")
    print(f"女方: {female_bazi['name']} - {female_bazi['day_pillar']}")
    print(f"综合评分: {compatibility_result['overall_score']}")
    
    print("\n生成2个不同版本的综合报告:")
    
    for i in range(1, 3):
        print(f"\n{'='*20} 版本{i} {'='*20}")
        report = enhanced_output_generator.generate_comprehensive_report(
            male_bazi, female_bazi, compatibility_result, i
        )
        print(report)
        
        # 检查整体一致性
        is_consistent, errors = consistency_checker.check_overall_consistency(
            male_bazi, report
        )
        if not is_consistent:
            print(f"\n⚠️ 整体一致性问题: {errors}")
        else:
            print(f"\n✅ 整体一致性检查通过")

def test_multiple_runs_same_input():
    """测试多次运行相同输入的一致性"""
    print("=" * 60)
    print("🔄 多次运行一致性测试")
    print("=" * 60)
    
    day_pillar = '甲子'
    variation_id = 1
    
    print(f"测试{day_pillar}日柱，版本{variation_id}，连续3次运行:")
    print("-" * 30)
    
    results = []
    for i in range(3):
        result = enhanced_output_generator.generate_personality_analysis(day_pillar, variation_id)
        results.append(result)
        print(f"\n第{i+1}次运行:")
        print(f"  {result}")
    
    # 检查是否完全一致
    all_same = all(result == results[0] for result in results)
    if all_same:
        print(f"\n✅ 多次运行结果完全一致（缓存机制正常）")
    else:
        print(f"\n⚠️ 多次运行结果不一致")
        
    # 清空缓存测试
    print(f"\n清空缓存后再次运行:")
    enhanced_output_generator.clear_cache()
    new_result = enhanced_output_generator.generate_personality_analysis(day_pillar, variation_id)
    print(f"  {new_result}")
    
    # 检查核心逻辑是否一致
    core_logic = get_rizhu_core_logic(day_pillar)
    core_personalities = core_logic.get('core_personality', [])
    
    consistent_core = all(
        any(trait in result for trait in core_personalities) 
        for result in results + [new_result]
    )
    
    if consistent_core:
        print(f"\n✅ 核心特征在所有版本中保持一致")
    else:
        print(f"\n⚠️ 核心特征在某些版本中缺失")

def main():
    """主测试函数"""
    print("🔮 八字一致性系统测试")
    print("验证同样输入产生一致但多样化的结果")
    print()
    
    # 测试个性分析一致性
    test_personality_consistency()
    
    # 测试合婚分析一致性
    test_compatibility_consistency()
    
    # 测试综合报告一致性
    test_comprehensive_report_consistency()
    
    # 测试多次运行一致性
    test_multiple_runs_same_input()
    
    print("\n" + "=" * 60)
    print("🎉 一致性系统测试完成！")
    print("系统确保了同样的八字输入产生一致的核心结论")
    print("同时通过多样化的表述避免了内容重复")
    print("=" * 60)

if __name__ == "__main__":
    main()

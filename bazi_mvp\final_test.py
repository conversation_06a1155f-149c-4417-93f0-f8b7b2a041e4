#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试 - 验证合婚功能修复
"""

def test_couple_compatibility_simple():
    """简单测试合婚功能"""
    try:
        print("🔮 测试合婚功能修复")
        print("=" * 40)
        
        # 模拟八字数据（直接提供正确格式）
        male_bazi = {
            'name': '张三',
            'year_pillar': '庚午',
            'month_pillar': '辛巳',
            'day_pillar': '甲子',
            'hour_pillar': '丙寅',
            'day_master_element': '甲木',
            'main_shishen': '正官'
        }
        
        female_bazi = {
            'name': '李四',
            'year_pillar': '乙亥',
            'month_pillar': '戊寅',
            'day_pillar': '己未',
            'hour_pillar': '丁巳',
            'day_master_element': '己土',
            'main_shishen': '正财'
        }
        
        print("📊 模拟八字数据:")
        print(f"   男方: {male_bazi}")
        print(f"   女方: {female_bazi}")
        
        # 导入合婚计算器
        import sys
        import os
        sys.path.append('.')
        
        from modules.couple_compatibility import CoupleCompatibilityCalculator
        
        print("\n💕 开始合婚计算...")
        calculator = CoupleCompatibilityCalculator()
        result = calculator.calculate_compatibility(male_bazi, female_bazi)
        
        print("✅ 合婚计算成功!")
        print(f"   综合评分: {result['overall_score']}分")
        print(f"   配对等级: {result['compatibility_level']}")
        print(f"   日柱分析: {result['day_pillar_analysis']['final_score']}分")
        print(f"   建议数量: {len(result['suggestions'])}条")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_calendar_conversion():
    """测试阳历阴历转换"""
    try:
        print("\n🌙 测试阳历阴历转换")
        print("=" * 40)
        
        import sys
        import os
        sys.path.append('.')
        
        from utils.calendar_converter import CalendarConverter
        
        cc = CalendarConverter()
        
        # 测试1990年9月2日
        result = cc.solar_to_lunar(1990, 9, 2)
        
        if result:
            print(f"✅ 1990年9月2日 -> {result['lunar_date_full']}")
            if result['lunar_month'] == 7 and result['lunar_day'] == 14:
                print("✅ 转换结果正确（农历七月十四）")
                return True
            else:
                print(f"❌ 转换结果错误，应该是七月十四")
                return False
        else:
            print("❌ 转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔮 最终功能验证测试")
    print("=" * 50)
    
    tests = [
        ("阳历阴历转换", test_calendar_conversion),
        ("合婚功能", test_couple_compatibility_simple)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试：{test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n" + "=" * 50)
    print(f"测试结果：{passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        print("\n📋 修复总结：")
        print("1. ✅ 合婚分析 'final_score' 错误已修复")
        print("2. ✅ 阳历阴历转换精度已改进")
        print("3. ✅ 语料库已丰富，支持随机选择")
        print("4. ✅ 系统可以正常运行合婚分析")
    else:
        print("⚠️  部分功能需要进一步检查")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字合婚计算模块
实现两人生辰八字的配对分析
"""

from typing import Dict, List, Any, Tuple
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class CoupleCompatibilityCalculator:
    def __init__(self):
        """初始化八字合婚计算器"""
        self.wuxing_elements = ['木', '火', '土', '金', '水']
        self.wuxing_relations = self._init_wuxing_relations()
        self.compatibility_rules = self._init_compatibility_rules()
        self.pillar_weights = {
            'year': 0.2,   # 年柱权重 - 代表根基
            'month': 0.25, # 月柱权重 - 代表事业
            'day': 0.35,   # 日柱权重 - 代表自身（最重要）
            'hour': 0.2    # 时柱权重 - 代表子女
        }
    
    def _init_wuxing_relations(self) -> Dict[str, Dict]:
        """初始化五行生克关系"""
        return {
            'sheng': {  # 相生关系
                '木': '火', '火': '土', '土': '金', '金': '水', '水': '木'
            },
            'ke': {     # 相克关系
                '木': '土', '火': '金', '土': '水', '金': '木', '水': '火'
            },
            'scores': { # 关系评分
                'same': 80,      # 同类
                'sheng_me': 85,  # 生我
                'me_sheng': 75,  # 我生
                'ke_me': 40,     # 克我
                'me_ke': 50,     # 我克
                'neutral': 60    # 中性
            }
        }
    
    def _init_compatibility_rules(self) -> Dict[str, Any]:
        """初始化合婚规则"""
        return {
            # 天干合化
            'tiangan_he': {
                ('甲', '己'): {'element': '土', 'score': 90, 'description': '甲己合土，中正之合'},
                ('乙', '庚'): {'element': '金', 'score': 85, 'description': '乙庚合金，仁义之合'},
                ('丙', '辛'): {'element': '水', 'score': 88, 'description': '丙辛合水，威制之合'},
                ('丁', '壬'): {'element': '木', 'score': 87, 'description': '丁壬合木，淫匿之合'},
                ('戊', '癸'): {'element': '火', 'score': 86, 'description': '戊癸合火，无情之合'}
            },
            
            # 地支三合
            'dizhi_sanhe': {
                ('申', '子', '辰'): {'element': '水', 'score': 95, 'description': '申子辰三合水局'},
                ('亥', '卯', '未'): {'element': '木', 'score': 95, 'description': '亥卯未三合木局'},
                ('寅', '午', '戌'): {'element': '火', 'score': 95, 'description': '寅午戌三合火局'},
                ('巳', '酉', '丑'): {'element': '金', 'score': 95, 'description': '巳酉丑三合金局'}
            },
            
            # 地支六合
            'dizhi_liuhe': {
                ('子', '丑'): {'score': 85, 'description': '子丑合土'},
                ('寅', '亥'): {'score': 85, 'description': '寅亥合木'},
                ('卯', '戌'): {'score': 85, 'description': '卯戌合火'},
                ('辰', '酉'): {'score': 85, 'description': '辰酉合金'},
                ('巳', '申'): {'score': 85, 'description': '巳申合水'},
                ('午', '未'): {'score': 85, 'description': '午未合土'}
            },
            
            # 地支相冲
            'dizhi_chong': {
                ('子', '午'): {'score': 30, 'description': '子午相冲'},
                ('丑', '未'): {'score': 35, 'description': '丑未相冲'},
                ('寅', '申'): {'score': 30, 'description': '寅申相冲'},
                ('卯', '酉'): {'score': 30, 'description': '卯酉相冲'},
                ('辰', '戌'): {'score': 35, 'description': '辰戌相冲'},
                ('巳', '亥'): {'score': 30, 'description': '巳亥相冲'}
            },
            
            # 地支相害
            'dizhi_hai': {
                ('子', '未'): {'score': 45, 'description': '子未相害'},
                ('丑', '午'): {'score': 45, 'description': '丑午相害'},
                ('寅', '巳'): {'score': 45, 'description': '寅巳相害'},
                ('卯', '辰'): {'score': 45, 'description': '卯辰相害'},
                ('申', '亥'): {'score': 45, 'description': '申亥相害'},
                ('酉', '戌'): {'score': 45, 'description': '酉戌相害'}
            }
        }
    
    def calculate_compatibility(self, male_bazi: Dict[str, Any], 
                              female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算两人八字合婚结果
        
        Args:
            male_bazi: 男方八字信息
            female_bazi: 女方八字信息
            
        Returns:
            合婚分析结果
        """
        # 分析各柱的配对情况
        pillar_analysis = self._analyze_pillars(male_bazi, female_bazi)
        
        # 分析五行配合
        wuxing_analysis = self._analyze_wuxing_compatibility(male_bazi, female_bazi)
        
        # 分析日柱配对（最重要）
        day_pillar_analysis = self._analyze_day_pillar(male_bazi, female_bazi)
        
        # 分析十神关系
        shishen_analysis = self._analyze_shishen_compatibility(male_bazi, female_bazi)
        
        # 计算综合评分
        overall_score = self._calculate_overall_score(
            pillar_analysis, wuxing_analysis, day_pillar_analysis, shishen_analysis
        )
        
        # 生成合婚建议
        suggestions = self._generate_compatibility_suggestions(
            overall_score, pillar_analysis, wuxing_analysis
        )
        
        return {
            'overall_score': overall_score,
            'compatibility_level': self._get_compatibility_level(overall_score),
            'pillar_analysis': pillar_analysis,
            'wuxing_analysis': wuxing_analysis,
            'day_pillar_analysis': day_pillar_analysis,
            'shishen_analysis': shishen_analysis,
            'suggestions': suggestions,
            'detailed_analysis': self._generate_detailed_analysis(
                male_bazi, female_bazi, overall_score
            )
        }
    
    def _analyze_pillars(self, male_bazi: Dict[str, Any], 
                        female_bazi: Dict[str, Any]) -> Dict[str, Any]:
        """分析四柱配对情况"""
        pillars = ['year', 'month', 'day', 'hour']
        pillar_results = {}
        
        for pillar in pillars:
            male_pillar = male_bazi.get(f'{pillar}_pillar', '')
            female_pillar = female_bazi.get(f'{pillar}_pillar', '')
            
            if male_pillar and female_pillar:
                male_tg, male_dz = male_pillar[0], male_pillar[1]
                female_tg, female_dz = female_pillar[0], female_pillar[1]
                
                # 分析天干关系
                tg_relation = self._analyze_tiangan_relation(male_tg, female_tg)
                
                # 分析地支关系
                dz_relation = self._analyze_dizhi_relation(male_dz, female_dz)
                
                # 计算该柱的配对分数
                pillar_score = (tg_relation['score'] + dz_relation['score']) / 2
                
                pillar_results[pillar] = {
                    'male_pillar': male_pillar,
                    'female_pillar': female_pillar,
                    'tiangan_relation': tg_relation,
                    'dizhi_relation': dz_relation,
                    'pillar_score': pillar_score,
                    'weight': self.pillar_weights[pillar]
                }
        
        return pillar_results
    
    def _analyze_tiangan_relation(self, male_tg: str, female_tg: str) -> Dict[str, Any]:
        """分析天干关系"""
        # 检查天干合化
        he_pair = (male_tg, female_tg)
        reverse_he_pair = (female_tg, male_tg)
        
        if he_pair in self.compatibility_rules['tiangan_he']:
            return {
                'type': 'he',
                'score': self.compatibility_rules['tiangan_he'][he_pair]['score'],
                'description': self.compatibility_rules['tiangan_he'][he_pair]['description']
            }
        elif reverse_he_pair in self.compatibility_rules['tiangan_he']:
            return {
                'type': 'he',
                'score': self.compatibility_rules['tiangan_he'][reverse_he_pair]['score'],
                'description': self.compatibility_rules['tiangan_he'][reverse_he_pair]['description']
            }
        
        # 如果没有合化，分析五行关系
        male_element = self._get_tiangan_element(male_tg)
        female_element = self._get_tiangan_element(female_tg)
        
        return self._analyze_element_relation(male_element, female_element)
    
    def _analyze_dizhi_relation(self, male_dz: str, female_dz: str) -> Dict[str, Any]:
        """分析地支关系"""
        dz_pair = (male_dz, female_dz)
        reverse_dz_pair = (female_dz, male_dz)
        
        # 检查六合
        if dz_pair in self.compatibility_rules['dizhi_liuhe'] or reverse_dz_pair in self.compatibility_rules['dizhi_liuhe']:
            rule = self.compatibility_rules['dizhi_liuhe'].get(dz_pair) or self.compatibility_rules['dizhi_liuhe'].get(reverse_dz_pair)
            return {
                'type': 'liuhe',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 检查相冲
        if dz_pair in self.compatibility_rules['dizhi_chong'] or reverse_dz_pair in self.compatibility_rules['dizhi_chong']:
            rule = self.compatibility_rules['dizhi_chong'].get(dz_pair) or self.compatibility_rules['dizhi_chong'].get(reverse_dz_pair)
            return {
                'type': 'chong',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 检查相害
        if dz_pair in self.compatibility_rules['dizhi_hai'] or reverse_dz_pair in self.compatibility_rules['dizhi_hai']:
            rule = self.compatibility_rules['dizhi_hai'].get(dz_pair) or self.compatibility_rules['dizhi_hai'].get(reverse_dz_pair)
            return {
                'type': 'hai',
                'score': rule['score'],
                'description': rule['description']
            }
        
        # 如果没有特殊关系，分析五行关系
        male_element = self._get_dizhi_element(male_dz)
        female_element = self._get_dizhi_element(female_dz)
        
        return self._analyze_element_relation(male_element, female_element)
    
    def _get_tiangan_element(self, tiangan: str) -> str:
        """获取天干对应的五行"""
        tiangan_elements = {
            '甲': '木', '乙': '木',
            '丙': '火', '丁': '火',
            '戊': '土', '己': '土',
            '庚': '金', '辛': '金',
            '壬': '水', '癸': '水'
        }
        return tiangan_elements.get(tiangan, '未知')
    
    def _get_dizhi_element(self, dizhi: str) -> str:
        """获取地支对应的五行"""
        dizhi_elements = {
            '子': '水', '丑': '土', '寅': '木', '卯': '木',
            '辰': '土', '巳': '火', '午': '火', '未': '土',
            '申': '金', '酉': '金', '戌': '土', '亥': '水'
        }
        return dizhi_elements.get(dizhi, '未知')
    
    def _analyze_element_relation(self, element1: str, element2: str) -> Dict[str, Any]:
        """分析两个五行元素的关系"""
        if element1 == element2:
            return {
                'type': 'same',
                'score': self.wuxing_relations['scores']['same'],
                'description': f'{element1}{element2}同类，和谐相处'
            }
        
        # 检查相生关系
        if self.wuxing_relations['sheng'].get(element1) == element2:
            return {
                'type': 'me_sheng',
                'score': self.wuxing_relations['scores']['me_sheng'],
                'description': f'{element1}生{element2}，有助益关系'
            }
        elif self.wuxing_relations['sheng'].get(element2) == element1:
            return {
                'type': 'sheng_me',
                'score': self.wuxing_relations['scores']['sheng_me'],
                'description': f'{element2}生{element1}，得到助力'
            }
        
        # 检查相克关系
        if self.wuxing_relations['ke'].get(element1) == element2:
            return {
                'type': 'me_ke',
                'score': self.wuxing_relations['scores']['me_ke'],
                'description': f'{element1}克{element2}，存在制约'
            }
        elif self.wuxing_relations['ke'].get(element2) == element1:
            return {
                'type': 'ke_me',
                'score': self.wuxing_relations['scores']['ke_me'],
                'description': f'{element2}克{element1}，受到制约'
            }
        
        return {
            'type': 'neutral',
            'score': self.wuxing_relations['scores']['neutral'],
            'description': f'{element1}与{element2}关系中性'
        }

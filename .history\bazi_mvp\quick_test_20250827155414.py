#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试改进功能
"""

def test_calendar_conversion():
    """测试阳历阴历转换"""
    try:
        from utils.calendar_converter import CalendarConverter
        cc = CalendarConverter()
        result = cc.solar_to_lunar(1990, 5, 15)
        if result:
            print("✅ 阳历阴历转换功能正常")
            print(f"   测试结果: {result['lunar_date_full']}")
            return True
        else:
            print("❌ 阳历阴历转换失败")
            return False
    except Exception as e:
        print(f"❌ 阳历阴历转换模块错误: {e}")
        return False

def test_time_parsing():
    """测试时间解析"""
    try:
        from modules.user_input import UserInputModule
        ui = UserInputModule()
        
        # 测试几种时间格式
        test_cases = ["14:30", "下午2点", "晚上8点"]
        success_count = 0
        
        for time_str in test_cases:
            result = ui._parse_time_input(time_str)
            if result:
                success_count += 1
        
        if success_count == len(test_cases):
            print("✅ 时间解析功能正常")
            return True
        else:
            print(f"⚠️  时间解析部分成功 ({success_count}/{len(test_cases)})")
            return True
    except Exception as e:
        print(f"❌ 时间解析模块错误: {e}")
        return False

def test_output_generation():
    """测试输出生成"""
    try:
        from modules.output_generator import OutputGenerator
        og = OutputGenerator()
        
        # 测试总结生成
        mock_data = {
            'day_master_element': '甲木',
            'strength': '偏弱',
            'useful_god': '水木'
        }
        
        summary = og._generate_summary({}, {}, mock_data, {})
        if summary and 'basic_profile' in summary:
            print("✅ 输出生成功能正常")
            return True
        else:
            print("❌ 输出生成功能异常")
            return False
    except Exception as e:
        print(f"❌ 输出生成模块错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🔮 快速功能测试")
    print("=" * 40)
    
    tests = [
        ("阳历阴历转换", test_calendar_conversion),
        ("时间解析功能", test_time_parsing),
        ("输出生成功能", test_output_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}...")
        if test_func():
            passed += 1
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
    else:
        print("⚠️  部分功能需要检查")

if __name__ == "__main__":
    main()

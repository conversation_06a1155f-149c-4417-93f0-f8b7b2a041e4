#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
将用户输入的原始数据转换为标准化的计算输入
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import re

class DataProcessor:
    def __init__(self):
        """初始化数据处理器"""
        self.location_timezone_map = self._init_location_timezone_map()
        self.solar_terms_data = self._init_solar_terms_data()
    
    def _init_location_timezone_map(self) -> Dict[str, int]:
        """初始化地点时区映射"""
        return {
            # 中国主要城市（东八区）
            '北京': 8, '上海': 8, '广州': 8, '深圳': 8, '杭州': 8,
            '南京': 8, '武汉': 8, '成都': 8, '重庆': 8, '西安': 8,
            '天津': 8, '青岛': 8, '大连': 8, '沈阳': 8, '哈尔滨': 8,
            '长春': 8, '石家庄': 8, '太原': 8, '呼和浩特': 8, '兰州': 8,
            '西宁': 8, '银川': 8, '乌鲁木齐': 8, '拉萨': 8, '昆明': 8,
            '贵阳': 8, '南宁': 8, '海口': 8, '福州': 8, '厦门': 8,
            '合肥': 8, '南昌': 8, '郑州': 8, '长沙': 8,
            # 其他地区默认东八区
            '中国': 8, '台湾': 8, '香港': 8, '澳门': 8
        }
    
    def _init_solar_terms_data(self) -> Dict[int, Dict[str, Any]]:
        """初始化节气数据（简化版）"""
        # 这里使用简化的节气数据，实际应用中需要更精确的天文算法
        return {
            1: {'name': '小寒', 'approx_day': 6},
            2: {'name': '大寒', 'approx_day': 20},
            3: {'name': '立春', 'approx_day': 4},
            4: {'name': '雨水', 'approx_day': 19},
            5: {'name': '惊蛰', 'approx_day': 6},
            6: {'name': '春分', 'approx_day': 21},
            7: {'name': '清明', 'approx_day': 5},
            8: {'name': '谷雨', 'approx_day': 20},
            9: {'name': '立夏', 'approx_day': 6},
            10: {'name': '小满', 'approx_day': 21},
            11: {'name': '芒种', 'approx_day': 6},
            12: {'name': '夏至', 'approx_day': 22},
            13: {'name': '小暑', 'approx_day': 7},
            14: {'name': '大暑', 'approx_day': 23},
            15: {'name': '立秋', 'approx_day': 8},
            16: {'name': '处暑', 'approx_day': 23},
            17: {'name': '白露', 'approx_day': 8},
            18: {'name': '秋分', 'approx_day': 23},
            19: {'name': '寒露', 'approx_day': 8},
            20: {'name': '霜降', 'approx_day': 24},
            21: {'name': '立冬', 'approx_day': 8},
            22: {'name': '小雪', 'approx_day': 22},
            23: {'name': '大雪', 'approx_day': 7},
            24: {'name': '冬至', 'approx_day': 22}
        }
    
    def process(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理原始用户数据"""
        print("正在处理和验证数据...")
        
        # 标准化基本信息
        processed_data = {
            'name': raw_data.get('name', '未提供'),
            'gender': raw_data.get('gender', '未提供'),
            'birth_location': raw_data.get('birth_location', '未提供')
        }
        
        # 处理日期信息
        date_info = self._process_date_info(raw_data)
        processed_data.update(date_info)
        
        # 处理时间信息
        time_info = self._process_time_info(raw_data, processed_data)
        processed_data.update(time_info)
        
        # 计算真太阳时
        solar_time_info = self._calculate_solar_time(processed_data)
        processed_data.update(solar_time_info)
        
        # 添加处理元数据
        processed_data['processing_metadata'] = {
            'processed_at': datetime.now().isoformat(),
            'data_quality': self._assess_data_quality(processed_data),
            'warnings': self._generate_warnings(processed_data)
        }
        
        print("✅ 数据处理完成")
        return processed_data
    
    def _process_date_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理日期信息"""
        year = raw_data.get('year') or raw_data.get('birth_year')
        month = raw_data.get('month') or raw_data.get('birth_month')
        day = raw_data.get('day') or raw_data.get('birth_day')
        
        # 创建日期对象
        birth_date = datetime(year, month, day)
        
        return {
            'birth_year': year,
            'birth_month': month,
            'birth_day': day,
            'birth_date': birth_date,
            'weekday': birth_date.weekday(),
            'is_leap_year': self._is_leap_year(year)
        }
    
    def _process_time_info(self, raw_data: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理时间信息"""
        time_certainty = raw_data.get('time_certainty', 'unknown')
        
        if time_certainty == 'unknown':
            # 时间未知，需要特殊处理
            return {
                'birth_hour': None,
                'birth_minute': None,
                'time_certainty': 'unknown',
                'time_analysis_needed': True,
                'time_description': raw_data.get('time_description', '时间未知')
            }
        
        hour = raw_data.get('hour', 12)
        minute = raw_data.get('minute', 0)
        
        # 创建完整的出生时间
        birth_datetime = datetime(
            processed_data.get('birth_year') or raw_data.get('year'),
            processed_data.get('birth_month') or raw_data.get('month'),
            processed_data.get('birth_day') or raw_data.get('day'),
            hour,
            minute
        )
        
        return {
            'birth_hour': hour,
            'birth_minute': minute,
            'birth_datetime': birth_datetime,
            'time_certainty': time_certainty,
            'time_analysis_needed': False,
            'time_description': raw_data.get('time_description', f"{hour}:{minute:02d}")
        }
    
    def _calculate_solar_time(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算真太阳时"""
        location = processed_data['birth_location']
        
        # 获取时区偏移
        timezone_offset = self._get_timezone_offset(location)
        
        # 如果时间未知，跳过真太阳时计算
        if processed_data.get('time_analysis_needed', False):
            return {
                'timezone_offset': timezone_offset,
                'solar_time_adjustment': 0,
                'solar_datetime': None
            }
        
        # 简化的真太阳时计算（实际应用中需要更复杂的天文算法）
        birth_datetime = processed_data['birth_datetime']
        
        # 经度调整（简化计算）
        longitude_adjustment = self._estimate_longitude_adjustment(location)
        
        # 时差调整（简化）
        equation_of_time = self._calculate_equation_of_time(birth_datetime)
        
        total_adjustment = longitude_adjustment + equation_of_time
        solar_datetime = birth_datetime + timedelta(minutes=total_adjustment)
        
        return {
            'timezone_offset': timezone_offset,
            'solar_time_adjustment': total_adjustment,
            'solar_datetime': solar_datetime,
            'solar_hour': solar_datetime.hour,
            'solar_minute': solar_datetime.minute
        }
    
    def _get_timezone_offset(self, location: str) -> int:
        """获取时区偏移"""
        # 简单的地点匹配
        for city, offset in self.location_timezone_map.items():
            if city in location:
                return offset
        
        # 默认返回东八区
        return 8
    
    def _estimate_longitude_adjustment(self, location: str) -> float:
        """估算经度调整（简化版）"""
        # 这里使用简化的经度估算
        # 实际应用中需要精确的地理坐标数据
        longitude_estimates = {
            '北京': 116.4, '上海': 121.5, '广州': 113.3, '深圳': 114.1,
            '杭州': 120.2, '南京': 118.8, '武汉': 114.3, '成都': 104.1,
            '重庆': 106.5, '西安': 108.9
        }
        
        # 查找匹配的城市
        for city, longitude in longitude_estimates.items():
            if city in location:
                # 计算与标准时区中心线的差异
                standard_longitude = 120.0  # 东八区中心线
                longitude_diff = longitude - standard_longitude
                # 每度经度约4分钟时差
                return longitude_diff * 4
        
        return 0  # 默认无调整
    
    def _calculate_equation_of_time(self, date: datetime) -> float:
        """计算时差（简化版）"""
        # 这是一个极简化的时差计算
        # 实际应用中需要精确的天文算法
        day_of_year = date.timetuple().tm_yday
        
        # 简化的时差公式
        equation = 9.87 * (2 * 3.14159 * (day_of_year - 81) / 365)
        return equation * 0.1  # 缩放因子
    
    def _is_leap_year(self, year: int) -> bool:
        """判断是否为闰年"""
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)
    
    def _assess_data_quality(self, processed_data: Dict[str, Any]) -> str:
        """评估数据质量"""
        quality_score = 100
        
        # 时间确定性影响
        time_certainty = processed_data.get('time_certainty', 'unknown')
        if time_certainty == 'unknown':
            quality_score -= 40
        elif time_certainty == 'approximate':
            quality_score -= 20
        elif time_certainty == 'default':
            quality_score -= 30
        
        # 地点信息影响
        if processed_data['birth_location'] == '未提供':
            quality_score -= 10
        
        # 性别信息影响
        if processed_data['gender'] == '未提供':
            quality_score -= 5
        
        # 返回质量等级
        if quality_score >= 90:
            return '优秀'
        elif quality_score >= 70:
            return '良好'
        elif quality_score >= 50:
            return '一般'
        else:
            return '较差'
    
    def _generate_warnings(self, processed_data: Dict[str, Any]) -> list:
        """生成数据警告"""
        warnings = []
        
        if processed_data.get('time_analysis_needed', False):
            warnings.append('出生时间未知，将影响分析准确性')
        
        if processed_data.get('time_certainty') == 'approximate':
            warnings.append('出生时间为大概时间，建议核实准确时间')
        
        if processed_data['birth_location'] == '未提供':
            warnings.append('未提供出生地点，使用默认时区计算')
        
        if processed_data.get('solar_time_adjustment', 0) > 30:
            warnings.append('真太阳时调整较大，请确认出生地点准确性')
        
        return warnings

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
验证系统基本功能
"""

print("开始测试八字算命系统...")

try:
    # 测试导入模块
    print("1. 测试模块导入...")
    
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from modules.data_processor import DataProcessor
    print("   ✅ DataProcessor 导入成功")
    
    from modules.bazi_calculator import BaziCalculator
    print("   ✅ BaziCalculator 导入成功")
    
    from modules.corpus_matcher import CorpusMatcher
    print("   ✅ CorpusMatcher 导入成功")
    
    from modules.output_generator import OutputGenerator
    print("   ✅ OutputGenerator 导入成功")
    
    from utils.logger import setup_logger
    print("   ✅ Logger 导入成功")
    
    print("2. 测试基本功能...")
    
    # 创建示例数据
    sample_data = {
        'name': '测试用户',
        'gender': '男',
        'year': 1990,
        'month': 5,
        'day': 15,
        'hour': 14,
        'minute': 30,
        'birth_location': '北京市',
        'time_certainty': 'exact'
    }
    
    # 测试数据处理
    print("   测试数据处理...")
    data_processor = DataProcessor()
    processed_data = data_processor.process(sample_data)
    print("   ✅ 数据处理成功")
    
    # 测试八字计算
    print("   测试八字计算...")
    bazi_calculator = BaziCalculator()
    calculation_result = bazi_calculator.calculate(processed_data)
    print("   ✅ 八字计算成功")
    
    # 显示八字结果
    if 'bazi_pillars' in calculation_result:
        pillars = calculation_result['bazi_pillars']
        year_pillar = ''.join(pillars['year_pillar'])
        month_pillar = ''.join(pillars['month_pillar'])
        day_pillar = ''.join(pillars['day_pillar'])
        hour_pillar = ''.join(pillars['hour_pillar'])
        print(f"   八字: {year_pillar} {month_pillar} {day_pillar} {hour_pillar}")
    
    # 测试语料匹配
    print("   测试语料匹配...")
    corpus_matcher = CorpusMatcher()
    matched_content = corpus_matcher.match(calculation_result)
    print("   ✅ 语料匹配成功")
    
    # 测试输出生成
    print("   测试输出生成...")
    output_generator = OutputGenerator()
    final_report = output_generator.generate(calculation_result, matched_content, processed_data)
    print("   ✅ 输出生成成功")
    
    print("\n3. 显示测试结果...")
    print(f"   用户: {final_report.get('name', 'N/A')}")
    print(f"   性别: {final_report.get('gender', 'N/A')}")
    print(f"   出生时间: {final_report.get('birth_time', 'N/A')}")
    
    bazi_info = final_report.get('bazi_info', {})
    print(f"   年柱: {bazi_info.get('year_pillar', 'N/A')}")
    print(f"   月柱: {bazi_info.get('month_pillar', 'N/A')}")
    print(f"   日柱: {bazi_info.get('day_pillar', 'N/A')}")
    print(f"   时柱: {bazi_info.get('hour_pillar', 'N/A')}")
    
    wuxing_info = final_report.get('wuxing_analysis', {})
    print(f"   五行分布: {wuxing_info.get('distribution', {})}")
    print(f"   日主强弱: {wuxing_info.get('strength', 'N/A')}")
    print(f"   用神: {wuxing_info.get('useful_god', 'N/A')}")
    
    analysis = final_report.get('analysis', {})
    print(f"   性格分析长度: {len(analysis.get('personality', ''))}")
    print(f"   事业分析长度: {len(analysis.get('career', ''))}")
    print(f"   感情分析长度: {len(analysis.get('relationship', ''))}")
    print(f"   健康分析长度: {len(analysis.get('health', ''))}")
    
    suggestions = final_report.get('suggestions', [])
    print(f"   建议数量: {len(suggestions)}")
    
    metadata = final_report.get('metadata', {})
    print(f"   准确度: {metadata.get('accuracy', 'N/A')}")
    print(f"   数据质量: {metadata.get('data_quality', 'N/A')}")
    
    print("\n🎉 所有测试通过！系统运行正常。")
    
except ImportError as e:
    print(f"❌ 模块导入错误: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成。")

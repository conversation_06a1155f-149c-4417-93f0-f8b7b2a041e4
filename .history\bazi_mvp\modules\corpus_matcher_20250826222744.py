#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语料匹配模块
根据八字计算结果匹配相应的分析内容
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from data.corpus_database import (
    PERSONALITY_CORPUS, CAREER_CORPUS, RELATIONSHIP_CORPUS, 
    HEALTH_CORPUS, LIFE_ADVICE_CORPUS, MODERNIZATION_DICT
)
from typing import Dict, Any, List, Optional
import random

class CorpusMatcher:
    def __init__(self):
        """初始化语料匹配器"""
        self.personality_corpus = PERSONALITY_CORPUS
        self.career_corpus = CAREER_CORPUS
        self.relationship_corpus = RELATIONSHIP_CORPUS
        self.health_corpus = HEALTH_CORPUS
        self.life_advice_corpus = LIFE_ADVICE_CORPUS
        self.modernization_dict = MODERNIZATION_DICT
    
    def match(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """主匹配函数"""
        print("正在匹配分析内容...")
        
        # 处理时间范围分析的情况
        if calculation_result.get('analysis_type') == 'time_range':
            return self._match_time_range_analysis(calculation_result)
        
        # 正常匹配流程
        return self._match_normal_analysis(calculation_result)
    
    def _match_normal_analysis(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """正常的语料匹配"""
        wuxing_analysis = calculation_result['wuxing_analysis']
        shishen_analysis = calculation_result['shishen_analysis']
        pattern_analysis = calculation_result['pattern_analysis']
        
        # 匹配性格特征
        personality_content = self._match_personality(wuxing_analysis)
        
        # 匹配事业运势
        career_content = self._match_career(pattern_analysis, wuxing_analysis)
        
        # 匹配感情婚姻
        relationship_content = self._match_relationship(shishen_analysis)
        
        # 匹配健康状况
        health_content = self._match_health(wuxing_analysis)
        
        # 生成人生建议
        life_suggestions = self._generate_life_suggestions(
            wuxing_analysis, pattern_analysis, shishen_analysis
        )
        
        # 现代化处理
        matched_content = {
            'personality': personality_content,
            'career': career_content,
            'relationship': relationship_content,
            'health': health_content,
            'suggestions': life_suggestions
        }
        
        # 应用现代化转换
        modernized_content = self._modernize_content(matched_content)
        
        return modernized_content
    
    def _match_time_range_analysis(self, calculation_result: Dict[str, Any]) -> Dict[str, Any]:
        """时间范围分析的语料匹配"""
        time_analyses = calculation_result['time_analyses']
        
        # 分析各时辰的共同特征
        common_traits = self._extract_common_traits(time_analyses)
        
        # 生成范围分析内容
        range_content = {
            'personality': f"根据可能的出生时辰分析，您的性格特征主要体现在：{', '.join(common_traits)}。由于出生时间不确定，建议核实准确时间以获得更精确的分析。",
            'career': "事业方面，您具有多方面的潜力，建议根据个人兴趣和实际情况选择发展方向。",
            'relationship': "感情方面，您是一个值得信赖的伴侣，但具体的感情模式需要确定准确出生时间后才能详细分析。",
            'health': "健康方面，建议保持良好的生活习惯，注意全面的身体保养。",
            'suggestions': [
                "尽量核实准确的出生时间，以获得更精确的分析",
                "关注自己的性格特点，发挥优势，改善不足",
                "在重要决策时可以参考多个时辰的分析结果",
                "保持开放的心态，不要过分依赖命理分析"
            ]
        }
        
        return range_content
    
    def _match_personality(self, wuxing_analysis: Dict[str, Any]) -> str:
        """匹配性格特征"""
        day_master_element = wuxing_analysis['day_master_element']
        strength_level = wuxing_analysis['strength_level']
        
        # 主要性格特征（基于日主五行）
        main_personality = self.personality_corpus.get(f'{day_master_element}旺', {})
        
        # 强弱程度调整
        strength_adjustment = self.personality_corpus.get(strength_level, {})
        
        # 组合描述
        personality_text = ""
        
        if main_personality:
            personality_text += main_personality.get('description', '')
            
            # 添加优点
            strengths = main_personality.get('strengths', [])
            if strengths:
                personality_text += f"\n\n您的主要优点包括：{', '.join(strengths)}。"
            
            # 添加需要注意的方面
            weaknesses = main_personality.get('weaknesses', [])
            if weaknesses:
                personality_text += f"\n\n需要注意的方面：{', '.join(weaknesses)}。"
        
        # 添加强弱程度的影响
        if strength_adjustment and 'description' in strength_adjustment:
            personality_text += f"\n\n{strength_adjustment['description']}"
        
        return personality_text if personality_text else "您是一个独特的个体，具有自己的性格特色。"
    
    def _match_career(self, pattern_analysis: Dict[str, Any], wuxing_analysis: Dict[str, Any]) -> str:
        """匹配事业运势"""
        pattern_type = pattern_analysis.get('pattern_type', '比劫格')
        
        # 获取格局对应的事业信息
        career_info = self.career_corpus.get(pattern_type, {})
        
        career_text = ""
        
        if career_info:
            # 基本描述
            career_text += career_info.get('description', '')
            
            # 适合的职业
            suitable_careers = career_info.get('suitable_careers', [])
            if suitable_careers:
                career_text += f"\n\n适合的职业方向包括：{', '.join(suitable_careers)}等。"
            
            # 事业建议
            career_advice = career_info.get('career_advice', '')
            if career_advice:
                career_text += f"\n\n事业建议：{career_advice}"
            
            # 成功因素
            success_factors = career_info.get('success_factors', [])
            if success_factors:
                career_text += f"\n\n您的成功因素：{', '.join(success_factors)}。"
        
        # 根据用神添加补充建议
        useful_god = wuxing_analysis.get('useful_god', '')
        if useful_god:
            career_text += f"\n\n建议在事业发展中多接触与{useful_god}相关的行业或环境。"
        
        return career_text if career_text else "您在事业方面有自己的发展潜力，建议根据兴趣和能力选择合适的方向。"
    
    def _match_relationship(self, shishen_analysis: Dict[str, Any]) -> str:
        """匹配感情婚姻"""
        dominant_shishen = shishen_analysis.get('dominant_shishen')
        
        # 如果没有明显的十神，使用通用分析
        if not dominant_shishen:
            return "您在感情方面比较平衡，既有自己的想法，也能考虑对方的感受。建议在感情中保持真诚和耐心，相信会找到合适的伴侣。"
        
        # 获取十神对应的感情信息
        relationship_info = self.relationship_corpus.get(dominant_shishen, {})
        
        relationship_text = ""
        
        if relationship_info:
            # 恋爱风格
            love_style = relationship_info.get('love_style', '')
            if love_style:
                relationship_text += f"您的恋爱风格属于{love_style}。"
            
            # 基本描述
            description = relationship_info.get('description', '')
            if description:
                relationship_text += f"\n\n{description}"
            
            # 理想伴侣
            ideal_partner = relationship_info.get('ideal_partner', '')
            if ideal_partner:
                relationship_text += f"\n\n您的理想伴侣类型：{ideal_partner}。"
            
            # 感情建议
            relationship_advice = relationship_info.get('relationship_advice', '')
            if relationship_advice:
                relationship_text += f"\n\n感情建议：{relationship_advice}"
            
            # 婚姻展望
            marriage_outlook = relationship_info.get('marriage_outlook', '')
            if marriage_outlook:
                relationship_text += f"\n\n婚姻展望：{marriage_outlook}。"
        
        return relationship_text if relationship_text else "您在感情方面有自己的特色，建议保持真诚，相信会找到合适的伴侣。"
    
    def _match_health(self, wuxing_analysis: Dict[str, Any]) -> str:
        """匹配健康状况"""
        day_master_element = wuxing_analysis['day_master_element']
        distribution = wuxing_analysis['distribution']
        
        # 获取日主五行对应的健康信息
        health_info = self.health_corpus.get(day_master_element, {})
        
        health_text = ""
        
        if health_info:
            # 相关器官
            related_organs = health_info.get('related_organs', [])
            if related_organs:
                health_text += f"根据您的八字，需要特别关注：{', '.join(related_organs)}的健康。"
            
            # 常见问题
            common_issues = health_info.get('common_issues', [])
            if common_issues:
                health_text += f"\n\n容易出现的健康问题：{', '.join(common_issues)}。"
            
            # 健康建议
            health_advice = health_info.get('health_advice', '')
            if health_advice:
                health_text += f"\n\n健康建议：{health_advice}"
            
            # 有益食物
            beneficial_foods = health_info.get('beneficial_foods', [])
            if beneficial_foods:
                health_text += f"\n\n建议多食用：{', '.join(beneficial_foods)}。"
            
            # 生活方式建议
            lifestyle_tips = health_info.get('lifestyle_tips', [])
            if lifestyle_tips:
                health_text += f"\n\n生活方式建议：{', '.join(lifestyle_tips)}。"
        
        # 根据五行分布添加补充建议
        weak_elements = [element for element, count in distribution.items() if count == 0]
        if weak_elements:
            health_text += f"\n\n您的八字中{', '.join(weak_elements)}较弱，建议通过饮食和生活方式进行调理。"
        
        return health_text if health_text else "建议保持良好的生活习惯，注意身体健康，定期体检。"
    
    def _generate_life_suggestions(self, wuxing_analysis: Dict[str, Any], 
                                 pattern_analysis: Dict[str, Any], 
                                 shishen_analysis: Dict[str, Any]) -> List[str]:
        """生成人生建议"""
        suggestions = []
        
        # 添加通用建议
        general_advice = self.life_advice_corpus['通用建议']
        suggestions.extend(random.sample(general_advice, min(3, len(general_advice))))
        
        # 根据五行强弱添加建议
        strength_level = wuxing_analysis.get('strength_level', '')
        if strength_level == '偏旺':
            suggestions.append("您的个性较强，建议多听取他人意见，学会适当妥协")
        elif strength_level == '偏弱':
            suggestions.append("建议增强自信心，勇于表达自己的观点和想法")
        elif strength_level == '中和':
            suggestions.append("您的性格比较平衡，建议继续保持这种优势")
        
        # 根据用神添加建议
        useful_god = wuxing_analysis.get('useful_god', '')
        if '木' in useful_god:
            suggestions.append("多接触绿色植物和自然环境，有助于运势提升")
        elif '火' in useful_god:
            suggestions.append("保持积极乐观的心态，多参与社交活动")
        elif '土' in useful_god:
            suggestions.append("脚踏实地，稳步发展，重视基础建设")
        elif '金' in useful_god:
            suggestions.append("注重品质和效率，培养专业技能")
        elif '水' in useful_god:
            suggestions.append("保持学习的心态，灵活应对变化")
        
        return suggestions[:6]  # 返回最多6条建议
    
    def _extract_common_traits(self, time_analyses: List[Dict[str, Any]]) -> List[str]:
        """提取时间范围分析的共同特征"""
        # 这里实现简化的共同特征提取
        common_traits = []
        
        # 统计各时辰的主要特征
        trait_counts = {}
        for analysis in time_analyses:
            characteristics = analysis.get('key_characteristics', [])
            for trait in characteristics:
                trait_counts[trait] = trait_counts.get(trait, 0) + 1
        
        # 选择出现频率高的特征
        for trait, count in trait_counts.items():
            if count >= len(time_analyses) * 0.5:  # 出现在50%以上的时辰中
                common_traits.append(trait)
        
        return common_traits[:5]  # 返回最多5个共同特征
    
    def _modernize_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """现代化内容表达"""
        modernized = {}
        
        for key, value in content.items():
            if isinstance(value, str):
                modernized[key] = self._modernize_text(value)
            elif isinstance(value, list):
                modernized[key] = [self._modernize_text(item) if isinstance(item, str) else item for item in value]
            else:
                modernized[key] = value
        
        return modernized
    
    def _modernize_text(self, text: str) -> str:
        """现代化文本表达"""
        modernized_text = text
        
        # 替换传统术语
        for old_term, new_term in self.modernization_dict.items():
            modernized_text = modernized_text.replace(old_term, new_term)
        
        return modernized_text
